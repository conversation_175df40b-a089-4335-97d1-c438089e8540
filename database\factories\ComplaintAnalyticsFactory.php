<?php

namespace Database\Factories;

use App\Models\ComplaintAnalytics;
use App\Models\Complaint;
use App\Models\ComplaintCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ComplaintAnalytics>
 */
class ComplaintAnalyticsFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ComplaintAnalytics::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $submittedDate = $this->faker->dateTimeBetween('-6 months', 'now');
        $priority = $this->faker->randomElement(['low', 'medium', 'high', 'critical']);
        $status = $this->faker->randomElement(['new', 'assigned', 'in_progress', 'on_hold', 'resolved', 'closed']);
        
        // Calculate resolution time based on priority and status
        $resolutionTimeHours = null;
        $resolutionDate = null;
        $firstResponseDate = null;
        $assignedDate = null;
        
        if (in_array($status, ['assigned', 'in_progress', 'on_hold', 'resolved', 'closed'])) {
            $assignedDate = $this->faker->dateTimeBetween($submittedDate, $submittedDate->format('Y-m-d') . ' +2 days');
            $firstResponseDate = $this->faker->dateTimeBetween($assignedDate, $assignedDate->format('Y-m-d') . ' +1 day');
        }
        
        if (in_array($status, ['resolved', 'closed'])) {
            $baseHours = match($priority) {
                'critical' => $this->faker->numberBetween(2, 24),
                'high' => $this->faker->numberBetween(8, 72),
                'medium' => $this->faker->numberBetween(24, 168),
                'low' => $this->faker->numberBetween(48, 336),
            };
            
            $resolutionTimeHours = $baseHours + $this->faker->numberBetween(-$baseHours * 0.3, $baseHours * 0.5);
            $resolutionDate = (clone $submittedDate)->modify("+{$resolutionTimeHours} hours");
        }

        // Calculate SLA due date
        $slaDays = match($priority) {
            'critical' => 1,
            'high' => 3,
            'medium' => 7,
            'low' => 14,
        };
        
        $slaDueDate = (clone $submittedDate)->modify("+{$slaDays} days");
        $slaBreached = $resolutionDate ? $resolutionDate > $slaDueDate : now() > $slaDueDate;

        return [
            'complaint_id' => Complaint::factory(),
            'category_id' => ComplaintCategory::factory(),
            'priority' => $priority,
            'status' => $status,
            'submitted_date' => $submittedDate,
            'assigned_date' => $assignedDate,
            'first_response_date' => $firstResponseDate,
            'resolution_date' => $resolutionDate,
            'closed_date' => $status === 'closed' ? $resolutionDate : null,
            'sla_due_date' => $slaDueDate,
            'response_time_hours' => $firstResponseDate ? 
                $submittedDate->diffInHours($firstResponseDate, true) : null,
            'resolution_time_hours' => $resolutionTimeHours,
            'total_time_hours' => $resolutionDate ? 
                $submittedDate->diffInHours($resolutionDate, true) : null,
            'escalation_count' => $this->faker->numberBetween(0, 3),
            'reassignment_count' => $this->faker->numberBetween(0, 2),
            'comment_count' => $this->faker->numberBetween(1, 10),
            'satisfaction_rating' => in_array($status, ['resolved', 'closed']) && $this->faker->boolean(80) ? 
                $this->faker->numberBetween(1, 5) : null,
            'sla_breached' => $slaBreached,
            'cost_estimate' => $this->calculateCostEstimate($priority, $resolutionTimeHours),
            'submitter_type' => $this->faker->randomElement(['student', 'teacher', 'admin']),
            'resolver_id' => in_array($status, ['resolved', 'closed']) ? 
                User::factory()->state(['user_type' => 'teacher']) : null,
        ];
    }

    /**
     * Calculate cost estimate based on priority and resolution time
     */
    private function calculateCostEstimate(string $priority, ?float $resolutionTimeHours): float
    {
        $baseCostPerHour = 50;
        $priorityMultiplier = match($priority) {
            'critical' => 2.0,
            'high' => 1.5,
            'medium' => 1.0,
            'low' => 0.8,
        };

        $timeSpent = $resolutionTimeHours ?? $this->faker->numberBetween(1, 24);
        return $timeSpent * $baseCostPerHour * $priorityMultiplier;
    }

    /**
     * Indicate that the analytics is for a resolved complaint.
     */
    public function resolved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'resolved',
            'resolution_date' => $this->faker->dateTimeBetween($attributes['submitted_date'], 'now'),
            'resolution_time_hours' => $this->faker->numberBetween(8, 72),
            'satisfaction_rating' => $this->faker->numberBetween(3, 5),
            'resolver_id' => User::factory()->state(['user_type' => 'teacher']),
        ]);
    }

    /**
     * Indicate that the analytics is for a closed complaint.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'closed',
            'resolution_date' => $this->faker->dateTimeBetween($attributes['submitted_date'], 'now'),
            'closed_date' => $this->faker->dateTimeBetween($attributes['resolution_date'] ?? $attributes['submitted_date'], 'now'),
            'resolution_time_hours' => $this->faker->numberBetween(8, 72),
            'satisfaction_rating' => $this->faker->numberBetween(1, 5),
            'resolver_id' => User::factory()->state(['user_type' => 'teacher']),
        ]);
    }

    /**
     * Indicate that the analytics is for an overdue complaint.
     */
    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => $this->faker->randomElement(['assigned', 'in_progress']),
            'sla_breached' => true,
            'sla_due_date' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
            'resolution_date' => null,
            'satisfaction_rating' => null,
        ]);
    }

    /**
     * Indicate that the analytics is SLA compliant.
     */
    public function slaCompliant(): static
    {
        return $this->state(fn (array $attributes) => [
            'sla_breached' => false,
        ]);
    }

    /**
     * Indicate that the analytics has high satisfaction.
     */
    public function highSatisfaction(): static
    {
        return $this->state(fn (array $attributes) => [
            'satisfaction_rating' => $this->faker->numberBetween(4, 5),
            'status' => $this->faker->randomElement(['resolved', 'closed']),
        ]);
    }

    /**
     * Indicate that the analytics has low satisfaction.
     */
    public function lowSatisfaction(): static
    {
        return $this->state(fn (array $attributes) => [
            'satisfaction_rating' => $this->faker->numberBetween(1, 2),
            'status' => $this->faker->randomElement(['resolved', 'closed']),
        ]);
    }

    /**
     * Indicate that the analytics is for a critical priority complaint.
     */
    public function critical(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'critical',
            'sla_due_date' => (clone $attributes['submitted_date'])->modify('+1 day'),
            'resolution_time_hours' => $this->faker->numberBetween(2, 24),
        ]);
    }

    /**
     * Indicate that the analytics is for a high priority complaint.
     */
    public function high(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
            'sla_due_date' => (clone $attributes['submitted_date'])->modify('+3 days'),
            'resolution_time_hours' => $this->faker->numberBetween(8, 72),
        ]);
    }
}
