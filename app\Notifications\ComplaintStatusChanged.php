<?php

namespace App\Notifications;

use App\Models\Complaint;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Complaint $complaint,
        public string $oldStatus,
        public string $newStatus
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $statusMessage = $this->getStatusMessage();
        
        return (new MailMessage)
            ->subject('Complaint Status Updated')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('The status of your complaint has been updated.')
            ->line('**Reference Number:** ' . $this->complaint->reference_number)
            ->line('**Title:** ' . $this->complaint->title)
            ->line('**Previous Status:** ' . ucfirst(str_replace('_', ' ', $this->oldStatus)))
            ->line('**New Status:** ' . ucfirst(str_replace('_', ' ', $this->newStatus)))
            ->line($statusMessage)
            ->action('View Complaint', url('/student/complaints/' . $this->complaint->id))
            ->line('Thank you for your patience.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'complaint_id' => $this->complaint->id,
            'reference_number' => $this->complaint->reference_number,
            'title' => $this->complaint->title,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'message' => 'Your complaint status has been updated to ' . ucfirst(str_replace('_', ' ', $this->newStatus)),
            'action_url' => url('/student/complaints/' . $this->complaint->id),
        ];
    }

    private function getStatusMessage(): string
    {
        return match ($this->newStatus) {
            'assigned' => 'Your complaint has been assigned to a staff member for review.',
            'in_progress' => 'We are actively working on resolving your complaint.',
            'on_hold' => 'Your complaint is temporarily on hold. We will update you soon.',
            'resolved' => 'Your complaint has been resolved. Please review the resolution and provide feedback.',
            'closed' => 'Your complaint has been closed. If you have any concerns, please contact us.',
            'reopened' => 'Your complaint has been reopened for further review.',
            default => 'Your complaint status has been updated.',
        };
    }
}
