<?php

namespace App\Filament\Resources\ComplaintResource\Pages;

use App\Filament\Resources\ComplaintResource;
use App\Models\ComplaintComment;
use Filament\Actions;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;

class ViewComplaint extends ViewRecord
{
    protected static string $resource = ComplaintResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('add_comment')
                ->form([
                    Textarea::make('comment')
                        ->label('Comment')
                        ->required()
                        ->rows(3),
                    Toggle::make('is_internal')
                        ->label('Internal Only')
                        ->helperText('If checked, this comment will only be visible to staff members')
                        ->default(false),
                ])
                ->action(function (array $data): void {
                    ComplaintComment::create([
                        'complaint_id' => $this->record->id,
                        'user_id' => auth()->id(),
                        'comment' => $data['comment'],
                        'is_internal' => $data['is_internal'],
                    ]);
                    
                    Notification::make()
                        ->title('Comment added successfully')
                        ->success()
                        ->send();
                    
                    // Refresh the page to show the new comment
                    $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                })
                ->icon('heroicon-o-chat-bubble-left')
                ->color('primary'),
            Actions\Action::make('assign')
                ->icon('heroicon-o-user-plus')
                ->color('warning')
                ->action(function (array $data): void {
                    $this->record->update([
                        'assigned_to' => $data['assigned_to'],
                        'status' => 'assigned',
                    ]);
                    
                    // Record status change
                    $this->record->statusHistory()->create([
                        'status' => 'assigned',
                        'changed_by' => auth()->id(),
                        'notes' => 'Complaint assigned to staff member',
                    ]);
                    
                    Notification::make()
                        ->title('Complaint assigned successfully')
                        ->success()
                        ->send();
                    
                    // Refresh the page
                    $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                })
                ->form([
                    \Filament\Forms\Components\Select::make('assigned_to')
                        ->label('Assign To')
                        ->options(\App\Models\User::role(['admin', 'teacher'])->pluck('name', 'id'))
                        ->required()
                        ->searchable(),
                ]),
            Actions\Action::make('change_status')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->action(function (array $data): void {
                    $oldStatus = $this->record->status;
                    $newStatus = $data['status'];
                    
                    $this->record->update([
                        'status' => $newStatus,
                        'resolution_date' => in_array($newStatus, ['resolved', 'closed']) ? now() : $this->record->resolution_date,
                    ]);
                    
                    // Record status change
                    $this->record->statusHistory()->create([
                        'status' => $newStatus,
                        'changed_by' => auth()->id(),
                        'notes' => $data['notes'] ?? "Status changed from {$oldStatus} to {$newStatus}",
                    ]);
                    
                    Notification::make()
                        ->title('Complaint status updated')
                        ->success()
                        ->send();
                    
                    // Refresh the page
                    $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                })
                ->form([
                    \Filament\Forms\Components\Select::make('status')
                        ->label('New Status')
                        ->options(\App\Models\Complaint::STATUSES)
                        ->required(),
                    \Filament\Forms\Components\Textarea::make('notes')
                        ->label('Notes')
                        ->rows(3),
                ]),
        ];
    }
}
