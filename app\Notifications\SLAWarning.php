<?php

namespace App\Notifications;

use App\Models\Complaint;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SLAWarning extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Complaint $complaint,
        public string $warningType = 'approaching' // 'approaching' or 'overdue'
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $isAssignee = $notifiable->id === $this->complaint->assigned_to;
        
        if ($this->warningType === 'overdue') {
            $subject = 'URGENT: Complaint SLA Breached';
            $daysPastDue = abs($this->complaint->getDaysUntilDue());
            
            if ($isAssignee) {
                $message = "A complaint assigned to you has breached its SLA and is {$daysPastDue} day(s) overdue.";
                $action = 'Take Immediate Action';
                $actionUrl = url('/admin/complaints/' . $this->complaint->id);
            } else {
                $message = "Your complaint has exceeded the expected resolution time and is {$daysPastDue} day(s) overdue.";
                $action = 'View Complaint Status';
                $actionUrl = url('/student/complaints/' . $this->complaint->id);
            }
        } else {
            $subject = 'SLA Warning: Complaint Due Soon';
            $hoursRemaining = $this->complaint->due_date->diffInHours(now());
            
            if ($isAssignee) {
                $message = "A complaint assigned to you is due within {$hoursRemaining} hours.";
                $action = 'Review Complaint';
                $actionUrl = url('/admin/complaints/' . $this->complaint->id);
            } else {
                $message = "Your complaint is scheduled for resolution within {$hoursRemaining} hours.";
                $action = 'View Complaint';
                $actionUrl = url('/student/complaints/' . $this->complaint->id);
            }
        }

        return (new MailMessage)
            ->subject($subject)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line($message)
            ->line('**Reference Number:** ' . $this->complaint->reference_number)
            ->line('**Title:** ' . $this->complaint->title)
            ->line('**Category:** ' . $this->complaint->category->name)
            ->line('**Priority:** ' . ucfirst($this->complaint->priority))
            ->line('**Due Date:** ' . $this->complaint->due_date->format('M d, Y \a\t g:i A'))
            ->line('**Current Status:** ' . ucfirst(str_replace('_', ' ', $this->complaint->status)))
            ->action($action, $actionUrl)
            ->line($this->warningType === 'overdue' 
                ? 'Immediate attention is required to resolve this complaint.'
                : 'Please ensure timely resolution to maintain service quality.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $isAssignee = $notifiable->id === $this->complaint->assigned_to;
        
        return [
            'complaint_id' => $this->complaint->id,
            'reference_number' => $this->complaint->reference_number,
            'title' => $this->complaint->title,
            'warning_type' => $this->warningType,
            'due_date' => $this->complaint->due_date,
            'days_until_due' => $this->complaint->getDaysUntilDue(),
            'message' => $this->warningType === 'overdue'
                ? 'Complaint SLA has been breached and requires immediate attention'
                : 'Complaint is approaching SLA deadline',
            'action_url' => $isAssignee 
                ? url('/admin/complaints/' . $this->complaint->id)
                : url('/student/complaints/' . $this->complaint->id),
            'urgency' => $this->warningType === 'overdue' ? 'high' : 'medium',
        ];
    }
}
