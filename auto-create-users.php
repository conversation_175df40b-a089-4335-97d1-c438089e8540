<?php

// Bootstrap Laravel
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 Creating users directly...\n";

try {
    // Create roles
    $adminRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin']);
    $studentRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'student']);
    echo "✅ Roles created\n";

    // Delete existing users
    \App\Models\User::truncate();
    echo "✅ Cleared existing users\n";

    // Create admin user
    $admin = \App\Models\User::create([
        'name' => 'Admin User',
        'email' => '<EMAIL>',
        'password' => \Illuminate\Support\Facades\Hash::make('123456'),
        'email_verified_at' => now(),
        'department' => 'IT',
        'user_type' => 'admin',
    ]);
    $admin->assignRole('admin');
    echo "✅ Admin created: <EMAIL> / 123456\n";

    // Create teacher user
    $teacher = \App\Models\User::create([
        'name' => 'Teacher User',
        'email' => '<EMAIL>',
        'password' => \Illuminate\Support\Facades\Hash::make('123456'),
        'email_verified_at' => now(),
        'department' => 'Academic Affairs',
        'user_type' => 'teacher',
    ]);
    $teacher->assignRole('teacher');
    echo "✅ Teacher created: <EMAIL> / 123456\n";

    // Create student user
    $student = \App\Models\User::create([
        'name' => 'Student User',
        'email' => '<EMAIL>',
        'password' => \Illuminate\Support\Facades\Hash::make('123456'),
        'email_verified_at' => now(),
        'department' => 'Computer Science',
        'user_type' => 'student',
    ]);
    $student->assignRole('student');
    echo "✅ Student created: <EMAIL> / 123456\n";

    echo "\n🎉 SUCCESS!\n";
    echo "Login at: http://localhost:8000/admin\n";
    echo "Credentials: <EMAIL> / 123456\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Trying alternative method...\n";

    // Direct database approach
    $db = new PDO('sqlite:database/database.sqlite');

    // Clear tables
    $db->exec("DELETE FROM users");
    $db->exec("DELETE FROM roles WHERE name IN ('admin', 'student')");
    $db->exec("DELETE FROM model_has_roles");

    // Create roles
    $db->exec("INSERT INTO roles (name, guard_name, created_at, updated_at) VALUES
        ('admin', 'web', datetime('now'), datetime('now')),
        ('student', 'web', datetime('now'), datetime('now'))");

    // Create users
    $adminHash = password_hash('123456', PASSWORD_DEFAULT);
    $studentHash = password_hash('123456', PASSWORD_DEFAULT);

    $db->exec("INSERT INTO users (name, email, password, email_verified_at, department, user_type, created_at, updated_at) VALUES
        ('Admin User', '<EMAIL>', '$adminHash', datetime('now'), 'IT', 'admin', datetime('now'), datetime('now')),
        ('Student User', '<EMAIL>', '$studentHash', datetime('now'), 'CS', 'student', datetime('now'), datetime('now'))");

    // Assign roles
    $db->exec("INSERT INTO model_has_roles (role_id, model_type, model_id) VALUES
        (1, 'App\\\\Models\\\\User', 1),
        (2, 'App\\\\Models\\\\User', 2)");

    echo "✅ Users created via direct database method\n";
    echo "Login at: http://localhost:8000/admin\n";
    echo "Credentials: <EMAIL> / 123456\n";
}
