<?php

namespace App\Filament\Widgets;

use App\Models\ComplaintAnalytics;
use Filament\Widgets\ChartWidget;

class ResolutionTimeWidget extends ChartWidget
{
    protected static ?string $heading = 'Average Resolution Time by Priority';

    protected static ?int $sort = 6;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $maxHeight = '300px';

    public ?string $filter = '30days';

    protected function getData(): array
    {
        $data = $this->getResolutionTimeData();

        return [
            'datasets' => [
                [
                    'label' => 'Average Resolution Time (Hours)',
                    'data' => array_values($data['times']),
                    'backgroundColor' => [
                        '#ef4444', // Critical - Red
                        '#f59e0b', // High - Amber
                        '#3b82f6', // Medium - Blue
                        '#6b7280', // Low - Gray
                    ],
                    'borderColor' => [
                        '#dc2626',
                        '#d97706',
                        '#2563eb',
                        '#4b5563',
                    ],
                    'borderWidth' => 2,
                ],
            ],
            'labels' => array_keys($data['times']),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getFilters(): ?array
    {
        return [
            '7days' => 'Last 7 days',
            '30days' => 'Last 30 days',
            '3months' => 'Last 3 months',
            '6months' => 'Last 6 months',
            '12months' => 'Last 12 months',
        ];
    }

    private function getResolutionTimeData(): array
    {
        $period = $this->filter ?? '30days';
        $startDate = $this->getStartDate($period);

        $analytics = ComplaintAnalytics::where('submitted_date', '>=', $startDate)
            ->whereNotNull('resolution_time_hours')
            ->selectRaw('
                priority,
                AVG(resolution_time_hours) as avg_resolution_time,
                COUNT(*) as count
            ')
            ->groupBy('priority')
            ->get();

        $priorities = ['Critical', 'High', 'Medium', 'Low'];
        $times = [];

        foreach ($priorities as $priority) {
            $data = $analytics->firstWhere('priority', strtolower($priority));
            $times[$priority] = $data ? round($data->avg_resolution_time, 1) : 0;
        }

        return [
            'times' => $times,
            'counts' => $analytics->pluck('count', 'priority')->toArray(),
        ];
    }

    private function getStartDate(string $period): \Carbon\Carbon
    {
        return match ($period) {
            '7days' => now()->subDays(7),
            '30days' => now()->subDays(30),
            '3months' => now()->subMonths(3),
            '6months' => now()->subMonths(6),
            '12months' => now()->subMonths(12),
            default => now()->subDays(30),
        };
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.parsed.y + " hours average";
                        }',
                    ],
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Hours',
                    ],
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Priority Level',
                    ],
                ],
            ],
        ];
    }
}
