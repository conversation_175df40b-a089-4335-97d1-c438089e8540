<?php

namespace Database\Seeders;

use App\Models\SLASetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SLASettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $slaSettings = [
            [
                'name' => 'Critical Priority SLA',
                'description' => 'SLA for critical priority complaints requiring immediate attention',
                'priority' => 'critical',
                'first_response_hours' => 1,
                'resolution_days' => 1,
                'escalation_days' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'High Priority SLA',
                'description' => 'SLA for high priority complaints requiring urgent attention',
                'priority' => 'high',
                'first_response_hours' => 4,
                'resolution_days' => 3,
                'escalation_days' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Medium Priority SLA',
                'description' => 'SLA for medium priority complaints with standard resolution time',
                'priority' => 'medium',
                'first_response_hours' => 8,
                'resolution_days' => 7,
                'escalation_days' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'Low Priority SLA',
                'description' => 'SLA for low priority complaints with extended resolution time',
                'priority' => 'low',
                'first_response_hours' => 24,
                'resolution_days' => 14,
                'escalation_days' => 10,
                'is_active' => true,
            ],
        ];

        foreach ($slaSettings as $setting) {
            SLASetting::create($setting);
        }
    }
}
