@echo off
echo ========================================
echo FINAL FIX - Creating Users Directly
echo ========================================

echo Step 1: Setting up environment...
copy .env.working .env
echo ✅ Environment file created

echo Step 2: Creating database...
echo. > database\database.sqlite
echo ✅ Database file created

echo Step 3: Generating application key...
php artisan key:generate --force
echo ✅ Application key generated

echo Step 4: Clearing cache...
php artisan config:clear
php artisan cache:clear
echo ✅ Cache cleared

echo Step 5: Running migrations...
php artisan migrate:fresh --force
echo ✅ Database tables created

echo Step 6: Creating demo users...
php artisan users:create-demo
echo ✅ Demo users created

echo.
echo ========================================
echo SETUP COMPLETE!
echo ========================================
echo.
echo Login Credentials:
echo • Admin: <EMAIL> / password
echo • Teacher: <EMAIL> / password
echo • Student: <EMAIL> / password
echo.
echo Access URLs:
echo • Admin Panel: http://localhost:8000/admin
echo • Student Portal: http://localhost:8000/student/dashboard
echo.
echo Starting development server...
echo Press Ctrl+C to stop the server
echo.
pause

php artisan serve
