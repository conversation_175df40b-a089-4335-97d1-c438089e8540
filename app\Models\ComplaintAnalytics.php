<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ComplaintAnalytics extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'complaint_id',
        'category_id',
        'priority',
        'status',
        'submitted_date',
        'assigned_date',
        'first_response_date',
        'resolution_date',
        'closed_date',
        'sla_due_date',
        'response_time_hours',
        'resolution_time_hours',
        'total_time_hours',
        'escalation_count',
        'reassignment_count',
        'comment_count',
        'satisfaction_rating',
        'sla_breached',
        'cost_estimate',
        'submitter_type',
        'resolver_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'submitted_date' => 'datetime',
        'assigned_date' => 'datetime',
        'first_response_date' => 'datetime',
        'resolution_date' => 'datetime',
        'closed_date' => 'datetime',
        'sla_due_date' => 'datetime',
        'response_time_hours' => 'decimal:2',
        'resolution_time_hours' => 'decimal:2',
        'total_time_hours' => 'decimal:2',
        'escalation_count' => 'integer',
        'reassignment_count' => 'integer',
        'comment_count' => 'integer',
        'satisfaction_rating' => 'integer',
        'sla_breached' => 'boolean',
        'cost_estimate' => 'decimal:2',
    ];

    /**
     * Get the complaint that this analytics record belongs to
     */
    public function complaint(): BelongsTo
    {
        return $this->belongsTo(Complaint::class);
    }

    /**
     * Get the category
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ComplaintCategory::class);
    }

    /**
     * Get the resolver
     */
    public function resolver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolver_id');
    }

    /**
     * Update analytics data from complaint
     */
    public static function updateFromComplaint(Complaint $complaint): self
    {
        $analytics = self::updateOrCreate(
            ['complaint_id' => $complaint->id],
            [
                'category_id' => $complaint->category_id,
                'priority' => $complaint->priority,
                'status' => $complaint->status,
                'submitted_date' => $complaint->created_at,
                'assigned_date' => $complaint->assignments()->first()?->assigned_at,
                'first_response_date' => $complaint->comments()->first()?->created_at,
                'resolution_date' => $complaint->resolution_date,
                'closed_date' => $complaint->status === 'closed' ? $complaint->updated_at : null,
                'sla_due_date' => $complaint->due_date,
                'escalation_count' => $complaint->escalations()->count(),
                'reassignment_count' => $complaint->assignments()->count() - 1,
                'comment_count' => $complaint->comments()->count(),
                'satisfaction_rating' => $complaint->satisfaction_rating,
                'sla_breached' => $complaint->isOverdue(),
                'submitter_type' => $complaint->submitter?->getRoleNames()->first(),
                'resolver_id' => $complaint->assigned_to,
            ]
        );

        // Calculate time metrics
        $analytics->calculateTimeMetrics();
        $analytics->save();

        return $analytics;
    }

    /**
     * Calculate time-based metrics
     */
    public function calculateTimeMetrics(): void
    {
        if ($this->submitted_date && $this->first_response_date) {
            $this->response_time_hours = $this->submitted_date->diffInHours($this->first_response_date, true);
        }

        if ($this->submitted_date && $this->resolution_date) {
            $this->resolution_time_hours = $this->submitted_date->diffInHours($this->resolution_date, true);
        }

        if ($this->submitted_date && $this->closed_date) {
            $this->total_time_hours = $this->submitted_date->diffInHours($this->closed_date, true);
        }

        // Estimate cost based on time and priority
        $this->cost_estimate = $this->calculateCostEstimate();
    }

    /**
     * Calculate cost estimate for complaint resolution
     */
    private function calculateCostEstimate(): float
    {
        $baseCostPerHour = 50; // Base cost per hour
        $priorityMultiplier = match($this->priority) {
            'critical' => 2.0,
            'high' => 1.5,
            'medium' => 1.0,
            'low' => 0.8,
            default => 1.0,
        };

        $timeSpent = $this->resolution_time_hours ?? $this->total_time_hours ?? 1;
        return $timeSpent * $baseCostPerHour * $priorityMultiplier;
    }

    /**
     * Get analytics for date range
     */
    public static function getAnalyticsForPeriod(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate)
    {
        return self::whereBetween('submitted_date', [$startDate, $endDate]);
    }

    /**
     * Get average resolution time by category
     */
    public static function getAverageResolutionTimeByCategory()
    {
        return self::whereNotNull('resolution_time_hours')
            ->selectRaw('category_id, AVG(resolution_time_hours) as avg_resolution_time')
            ->groupBy('category_id')
            ->with('category')
            ->get();
    }

    /**
     * Get SLA compliance rate
     */
    public static function getSLAComplianceRate(\Carbon\Carbon $startDate = null, \Carbon\Carbon $endDate = null)
    {
        $query = self::query();
        
        if ($startDate && $endDate) {
            $query->whereBetween('submitted_date', [$startDate, $endDate]);
        }

        $total = $query->count();
        $compliant = $query->where('sla_breached', false)->count();

        return $total > 0 ? ($compliant / $total) * 100 : 0;
    }

    /**
     * Get satisfaction metrics
     */
    public static function getSatisfactionMetrics(\Carbon\Carbon $startDate = null, \Carbon\Carbon $endDate = null)
    {
        $query = self::whereNotNull('satisfaction_rating');
        
        if ($startDate && $endDate) {
            $query->whereBetween('submitted_date', [$startDate, $endDate]);
        }

        return [
            'average_rating' => $query->avg('satisfaction_rating'),
            'total_responses' => $query->count(),
            'rating_distribution' => $query->selectRaw('satisfaction_rating, COUNT(*) as count')
                ->groupBy('satisfaction_rating')
                ->pluck('count', 'satisfaction_rating')
                ->toArray(),
        ];
    }
}
