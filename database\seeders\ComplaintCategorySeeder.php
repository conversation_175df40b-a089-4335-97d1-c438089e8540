<?php

namespace Database\Seeders;

use App\Models\ComplaintCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ComplaintCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Academic Issues',
                'description' => 'Issues related to academic programs, courses, and instruction',
                'iso_code' => 'AC001',
                'sla_days' => 7,
                'is_active' => true,
                'subcategories' => [
                    [
                        'name' => 'Course Content',
                        'description' => 'Issues with course materials, curriculum, or content quality',
                        'iso_code' => 'AC001.1',
                        'sla_days' => 5,
                    ],
                    [
                        'name' => 'Grading Issues',
                        'description' => 'Problems with grading, assessment, or evaluation',
                        'iso_code' => 'AC001.2',
                        'sla_days' => 3,
                    ],
                    [
                        'name' => 'Instructor Performance',
                        'description' => 'Concerns about teaching quality or instructor behavior',
                        'iso_code' => 'AC001.3',
                        'sla_days' => 10,
                    ],
                ]
            ],
            [
                'name' => 'Administrative Issues',
                'description' => 'Issues related to administrative processes and services',
                'iso_code' => 'AD001',
                'sla_days' => 5,
                'is_active' => true,
                'subcategories' => [
                    [
                        'name' => 'Registration Problems',
                        'description' => 'Issues with course registration, enrollment, or scheduling',
                        'iso_code' => 'AD001.1',
                        'sla_days' => 3,
                    ],
                    [
                        'name' => 'Financial Services',
                        'description' => 'Problems with billing, payments, or financial aid',
                        'iso_code' => 'AD001.2',
                        'sla_days' => 7,
                    ],
                    [
                        'name' => 'Student Records',
                        'description' => 'Issues with transcripts, certificates, or student records',
                        'iso_code' => 'AD001.3',
                        'sla_days' => 5,
                    ],
                ]
            ],
            [
                'name' => 'Facility Issues',
                'description' => 'Issues related to physical facilities and infrastructure',
                'iso_code' => 'FA001',
                'sla_days' => 3,
                'is_active' => true,
                'subcategories' => [
                    [
                        'name' => 'Classroom Facilities',
                        'description' => 'Problems with classrooms, furniture, or learning spaces',
                        'iso_code' => 'FA001.1',
                        'sla_days' => 2,
                    ],
                    [
                        'name' => 'Technology Issues',
                        'description' => 'Problems with computers, projectors, or other technology',
                        'iso_code' => 'FA001.2',
                        'sla_days' => 1,
                    ],
                    [
                        'name' => 'Safety and Security',
                        'description' => 'Safety concerns or security issues on campus',
                        'iso_code' => 'FA001.3',
                        'sla_days' => 1,
                    ],
                ]
            ],
            [
                'name' => 'Student Services',
                'description' => 'Issues related to student support services',
                'iso_code' => 'SS001',
                'sla_days' => 5,
                'is_active' => true,
                'subcategories' => [
                    [
                        'name' => 'Library Services',
                        'description' => 'Issues with library resources, access, or services',
                        'iso_code' => 'SS001.1',
                        'sla_days' => 3,
                    ],
                    [
                        'name' => 'Counseling Services',
                        'description' => 'Issues with academic or personal counseling services',
                        'iso_code' => 'SS001.2',
                        'sla_days' => 7,
                    ],
                    [
                        'name' => 'Career Services',
                        'description' => 'Issues with career guidance or job placement services',
                        'iso_code' => 'SS001.3',
                        'sla_days' => 7,
                    ],
                ]
            ],
            [
                'name' => 'Discrimination and Harassment',
                'description' => 'Reports of discrimination, harassment, or misconduct',
                'iso_code' => 'DH001',
                'sla_days' => 1,
                'is_active' => true,
                'subcategories' => [
                    [
                        'name' => 'Academic Discrimination',
                        'description' => 'Discrimination in academic settings or programs',
                        'iso_code' => 'DH001.1',
                        'sla_days' => 1,
                    ],
                    [
                        'name' => 'Workplace Harassment',
                        'description' => 'Harassment in workplace or professional settings',
                        'iso_code' => 'DH001.2',
                        'sla_days' => 1,
                    ],
                ]
            ],
            [
                'name' => 'Other',
                'description' => 'Other issues not covered by specific categories',
                'iso_code' => 'OT001',
                'sla_days' => 7,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            $subcategories = $categoryData['subcategories'] ?? [];
            unset($categoryData['subcategories']);
            
            $category = ComplaintCategory::create($categoryData);
            
            foreach ($subcategories as $subcategoryData) {
                $subcategoryData['parent_id'] = $category->id;
                $subcategoryData['is_active'] = true;
                ComplaintCategory::create($subcategoryData);
            }
        }
    }
}
