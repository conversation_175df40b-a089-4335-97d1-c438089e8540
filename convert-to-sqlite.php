<?php

echo "🔄 Converting database to SQLite...\n";

// Step 1: Update .env file
echo "📝 Step 1: Updating .env configuration...\n";

$envPath = '.env';
if (!file_exists($envPath)) {
    if (file_exists('.env.example')) {
        copy('.env.example', '.env');
        echo "✅ Created .env from .env.example\n";
    } else {
        echo "❌ No .env.example found\n";
        exit(1);
    }
}

// Read current .env
$envContent = file_get_contents($envPath);

// Update database configuration
$envContent = preg_replace('/^DB_CONNECTION=.*$/m', 'DB_CONNECTION=sqlite', $envContent);
$envContent = preg_replace('/^DB_HOST=.*$/m', '# DB_HOST=127.0.0.1', $envContent);
$envContent = preg_replace('/^DB_PORT=.*$/m', '# DB_PORT=3306', $envContent);
$envContent = preg_replace('/^DB_DATABASE=.*$/m', '# DB_DATABASE=complaints_management', $envContent);
$envContent = preg_replace('/^DB_USERNAME=.*$/m', '# DB_USERNAME=root', $envContent);
$envContent = preg_replace('/^DB_PASSWORD=.*$/m', '# DB_PASSWORD=', $envContent);

// Ensure other settings are SQLite-compatible
$envContent = preg_replace('/^SESSION_DRIVER=.*$/m', 'SESSION_DRIVER=database', $envContent);
$envContent = preg_replace('/^CACHE_DRIVER=.*$/m', 'CACHE_DRIVER=database', $envContent);
$envContent = preg_replace('/^QUEUE_CONNECTION=.*$/m', 'QUEUE_CONNECTION=database', $envContent);

file_put_contents($envPath, $envContent);
echo "✅ Updated .env for SQLite\n";

// Step 2: Create database directory and file
echo "\n📝 Step 2: Creating SQLite database file...\n";

if (!is_dir('database')) {
    mkdir('database', 0755, true);
    echo "✅ Created database directory\n";
}

$dbFile = 'database/database.sqlite';
if (file_exists($dbFile)) {
    unlink($dbFile);
    echo "✅ Removed old database file\n";
}

file_put_contents($dbFile, '');
chmod($dbFile, 0664);
echo "✅ Created new SQLite database file\n";

// Step 3: Create required directories
echo "\n📝 Step 3: Creating required directories...\n";

$dirs = [
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    }
}

// Step 4: Clear cache files
echo "\n📝 Step 4: Clearing cache files...\n";

$cacheFiles = glob('bootstrap/cache/*.php');
foreach ($cacheFiles as $file) {
    if (is_file($file)) {
        unlink($file);
    }
}

$configCache = 'bootstrap/cache/config.php';
if (file_exists($configCache)) {
    unlink($configCache);
}

echo "✅ Cache files cleared\n";

echo "\n🎉 SQLite conversion preparation complete!\n";
echo "Next steps:\n";
echo "1. php artisan config:clear\n";
echo "2. php artisan migrate:fresh --force\n";
echo "3. php artisan db:seed --force\n";
echo "4. php artisan serve\n";
