<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complaint & Feedback Management System - Laravel Course Curriculum</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 3.2em;
            margin-bottom: 15px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.3em;
            margin-bottom: 25px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .course-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-top: 25px;
            position: relative;
            z-index: 1;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            padding: 18px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }

        .stat-card h3 {
            font-size: 1.1em;
            margin-bottom: 8px;
            color: #fff;
        }

        .stat-card p {
            color: rgba(255,255,255,0.9);
            font-size: 0.9em;
        }

        .content {
            padding: 40px;
        }

        .project-overview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
        }

        .project-overview h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2em;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .iso-compliance {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }

        .iso-compliance h3 {
            color: #0c5460;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .agile-framework {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }

        .agile-framework h3 {
            color: #155724;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .sprint-timeline {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .sprint-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .sprint-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .sprint-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .sprint-card p {
            color: #6c757d;
            margin-bottom: 15px;
        }

        .sprint-card .duration {
            background: #e8f4fd;
            color: #0c5460;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            display: inline-block;
        }

        .module {
            margin-bottom: 60px;
            page-break-inside: avoid;
        }

        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            margin-bottom: 0;
        }

        .module-header h2 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .module-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .meta-item {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .module-content {
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 15px 15px;
            padding: 30px;
        }

        .objectives {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .objectives h3 {
            color: #0c5460;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .objectives ul {
            list-style-type: none;
            padding-left: 0;
        }

        .objectives li {
            padding: 8px 0;
            color: #0c5460;
            position: relative;
            padding-left: 30px;
        }

        .objectives li::before {
            content: "🎯";
            position: absolute;
            left: 0;
            top: 8px;
        }

        .lesson {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .lesson-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .lesson-title {
            color: #2c3e50;
            font-size: 1.3em;
            font-weight: 600;
            margin: 0;
        }

        .lesson-duration {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            white-space: nowrap;
        }

        .lesson-description {
            color: #495057;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .lesson-topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .topic-tag {
            background: #e9ecef;
            color: #495057;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            position: relative;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .code-example::before {
            content: attr(data-lang);
            position: absolute;
            top: 8px;
            right: 15px;
            background: #4a5568;
            color: #e2e8f0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            text-transform: uppercase;
        }

        .diagram {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 30px;
            margin: 25px 0;
            text-align: center;
        }

        .diagram h4 {
            color: #495057;
            margin-bottom: 15px;
        }

        .diagram-content {
            font-family: monospace;
            color: #6c757d;
            white-space: pre-line;
        }

        .best-practices {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .best-practices h4 {
            color: #155724;
            margin-bottom: 10px;
        }

        .pitfalls {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .pitfalls h4 {
            color: #721c24;
            margin-bottom: 10px;
        }

        .exercise {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .exercise h4 {
            color: #856404;
            margin-bottom: 15px;
        }

        .tools {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .tools h4 {
            color: #0056b3;
            margin-bottom: 15px;
        }

        .tools ul {
            list-style-type: none;
            padding-left: 0;
        }

        .tools li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }

        .tools li::before {
            content: "🔧";
            position: absolute;
            left: 0;
            top: 5px;
        }

        @media print {
            body {
                background: white;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
            }

            .module {
                page-break-before: always;
                margin-bottom: 30px;
            }

            .module:first-of-type {
                page-break-before: auto;
            }

            .code-example {
                font-size: 10px;
                padding: 15px;
            }

            .header {
                page-break-after: always;
            }
        }

        @media (max-width: 768px) {
            .header {
                padding: 40px 20px;
            }

            .header h1 {
                font-size: 2.5em;
            }

            .content {
                padding: 20px;
            }

            .course-stats {
                grid-template-columns: 1fr;
            }

            .lesson-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 Complaint & Feedback Management System</h1>
            <p class="subtitle">Laravel Course Curriculum for Final-Year Computer Science Students</p>

            <div class="course-stats">
                <div class="stat-card">
                    <h3>📚 Duration</h3>
                    <p>14-Week Semester</p>
                </div>
                <div class="stat-card">
                    <h3>🏗️ Project Focus</h3>
                    <p>ISO 21001 Compliant</p>
                </div>
                <div class="stat-card">
                    <h3>⚡ Methodology</h3>
                    <p>Agile SCRUM</p>
                </div>
                <div class="stat-card">
                    <h3>🎯 Modules</h3>
                    <p>6 Core Modules</p>
                </div>
                <div class="stat-card">
                    <h3>👥 User Roles</h3>
                    <p>Student, Teacher, Admin</p>
                </div>
                <div class="stat-card">
                    <h3>🔧 Tech Stack</h3>
                    <p>Laravel, Filament, Tailwind</p>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="project-overview">
                <h2>📋 Project Overview & Learning Objectives</h2>
                <p style="margin-bottom: 25px; color: #6c757d; font-size: 1.1em;">
                    This course prepares final-year computer science students to build a complete Complaint and Feedback
                    Management System using Laravel, aligned with ISO 21001 educational quality standards. Students will
                    work in agile teams to deliver a production-ready application with role-based access control.
                </p>

                <div class="iso-compliance">
                    <h3>🏆 ISO 21001 Compliance Requirements</h3>
                    <ul style="margin-left: 20px; color: #0c5460;">
                        <li><strong>Quality Management:</strong> Systematic complaint handling and resolution tracking</li>
                        <li><strong>Stakeholder Engagement:</strong> Multi-role access (Students, Teachers, Administrators)</li>
                        <li><strong>Continuous Improvement:</strong> Analytics and reporting for process enhancement</li>
                        <li><strong>Documentation:</strong> Audit trails and compliance reporting features</li>
                        <li><strong>Risk Management:</strong> Escalation procedures and SLA monitoring</li>
                    </ul>
                </div>

                <div class="agile-framework">
                    <h3>🔄 Agile SCRUM Framework</h3>
                    <ul style="margin-left: 20px; color: #155724;">
                        <li><strong>Sprint Duration:</strong> 2-week sprints throughout the 14-week semester</li>
                        <li><strong>Team Structure:</strong> 4-6 students per team with rotating Scrum Master role</li>
                        <li><strong>Ceremonies:</strong> Daily standups, sprint planning, reviews, and retrospectives</li>
                        <li><strong>Deliverables:</strong> Working software increments with demo presentations</li>
                        <li><strong>Tools:</strong> Jira/Trello for backlog management, Git for version control</li>
                    </ul>
                </div>

                <div class="sprint-timeline">
                    <div class="sprint-card">
                        <h3>🏗️ Foundation Phase</h3>
                        <p>Sprints 1-2: Project setup, team formation, requirements analysis, and basic Laravel foundation.</p>
                        <span class="duration">Weeks 1-4</span>
                    </div>

                    <div class="sprint-card">
                        <h3>⚡ Development Phase</h3>
                        <p>Sprints 3-5: Core feature development, authentication, database design, and admin panel.</p>
                        <span class="duration">Weeks 5-10</span>
                    </div>

                    <div class="sprint-card">
                        <h3>🎨 Integration Phase</h3>
                        <p>Sprints 6-7: Frontend development, testing, security implementation, and final integration.</p>
                        <span class="duration">Weeks 11-14</span>
                    </div>
                </div>
            </div>

            <!-- Module 1: Project Foundation & Laravel Fundamentals -->
            <div class="module" id="module1">
                <div class="module-header">
                    <h2>Module 1: Project Foundation & Laravel Fundamentals</h2>
                    <p>Establish project foundation and master Laravel MVC architecture for complaint management</p>
                    <div class="module-meta">
                        <span class="meta-item">⏱️ Duration: Weeks 1-2</span>
                        <span class="meta-item">📚 Sprint 1</span>
                        <span class="meta-item">🏗️ Foundation Setup</span>
                        <span class="meta-item">📊 Difficulty: Beginner</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Set up Laravel project with proper structure for complaint management</li>
                            <li>Understand MVC architecture in context of complaint handling workflows</li>
                            <li>Create foundational models for Users, Complaints, and Categories</li>
                            <li>Implement basic routing and controller structure</li>
                            <li>Establish team collaboration workflows using Git and SCRUM</li>
                            <li>Define project requirements and user stories for complaint system</li>
                        </ul>
                    </div>

                    <!-- Lesson 1.1 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">1.1 Project Setup & Team Organization</h3>
                            <span class="lesson-duration">3 hours</span>
                        </div>
                        <p class="lesson-description">
                            Establish the development environment, team structure, and project foundation for building
                            the complaint management system. Learn agile methodologies and set up collaboration tools.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Laravel Installation</span>
                            <span class="topic-tag">Git Workflow</span>
                            <span class="topic-tag">SCRUM Setup</span>
                            <span class="topic-tag">Requirements Analysis</span>
                            <span class="topic-tag">Team Roles</span>
                        </div>

                        <div class="code-example" data-lang="bash">
# Project Setup Commands
composer create-project laravel/laravel complaint-management-system
cd complaint-management-system

# Install required packages for complaint system
composer require filament/filament:"^3.0"
composer require spatie/laravel-permission
composer require laravel/breeze --dev

# Set up environment
cp .env.example .env
php artisan key:generate

# Database setup (SQLite for development)
touch database/database.sqlite
php artisan migrate

# Install Filament
php artisan filament:install --panels

# Install Breeze for authentication
php artisan breeze:install blade
npm install && npm run dev
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Sprint 1 Exercise: Project Foundation</h4>
                            <p><strong>Team Task:</strong> Set up the complete development environment and define user stories.</p>
                            <ul>
                                <li><strong>Technical Setup:</strong> Each team member sets up Laravel with required packages</li>
                                <li><strong>Git Repository:</strong> Create shared repository with branching strategy</li>
                                <li><strong>User Stories:</strong> Define 15-20 user stories for complaint management</li>
                                <li><strong>Sprint Planning:</strong> Plan first sprint with story point estimation</li>
                                <li><strong>Role Assignment:</strong> Assign Scrum Master, Product Owner, and Developer roles</li>
                            </ul>
                        </div>

                        <div class="tools">
                            <h4>🔧 Required Tools & Libraries</h4>
                            <ul>
                                <li><strong>Laravel 10.x:</strong> Main framework for complaint system</li>
                                <li><strong>Filament 3.x:</strong> Admin panel for managing complaints and users</li>
                                <li><strong>Laravel Breeze:</strong> Authentication scaffolding</li>
                                <li><strong>Spatie Laravel Permission:</strong> Role and permission management</li>
                                <li><strong>Tailwind CSS:</strong> Frontend styling framework</li>
                                <li><strong>SQLite/MySQL:</strong> Database for development/production</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Lesson 1.2 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">1.2 Complaint System Database Design</h3>
                            <span class="lesson-duration">4 hours</span>
                        </div>
                        <p class="lesson-description">
                            Design the database schema specifically for complaint and feedback management, including
                            user roles, complaint categories, status tracking, and ISO 21001 compliance requirements.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Database Schema</span>
                            <span class="topic-tag">Entity Relationships</span>
                            <span class="topic-tag">Migrations</span>
                            <span class="topic-tag">ISO 21001 Requirements</span>
                            <span class="topic-tag">Audit Trails</span>
                        </div>

                        <div class="diagram">
                            <h4>Complaint Management System ERD</h4>
                            <div class="diagram-content">
Users                    Complaints               Categories
├── id                   ├── id                   ├── id
├── name                 ├── reference_number     ├── name
├── email                ├── title                ├── description
├── role (enum)          ├── description          ├── color
├── student_id           ├── category_id (FK)     ├── is_active
├── department           ├── submitted_by (FK)    ├── sort_order
├── phone                ├── assigned_to (FK)     └── created_at
├── created_at           ├── priority (enum)
└── updated_at           ├── status (enum)        Responses
                         ├── due_date             ├── id
Roles                    ├── resolved_at          ├── complaint_id (FK)
├── id                   ├── satisfaction_rating  ├── user_id (FK)
├── name                 ├── attachments (JSON)   ├── message
├── guard_name           ├── created_at           ├── is_internal
└── created_at           └── updated_at           ├── created_at
                                                  └── updated_at
Permissions              ComplaintHistory
├── id                   ├── id                   Notifications
├── name                 ├── complaint_id (FK)    ├── id
├── guard_name           ├── user_id (FK)         ├── user_id (FK)
└── created_at           ├── action               ├── complaint_id (FK)
                         ├── old_status           ├── type
                         ├── new_status           ├── title
                         ├── notes                ├── message
                         └── created_at           ├── read_at
                                                  └── created_at
            </div>
                        </div>

                        <div class="best-practices">
                            <h4>✅ Best Practices for Complaint System Database</h4>
                            <ul>
                                <li>Use enum types for status and priority to ensure data consistency</li>
                                <li>Include audit trail fields for ISO 21001 compliance</li>
                                <li>Add indexes on frequently queried columns (status, priority, dates)</li>
                                <li>Use foreign key constraints to maintain referential integrity</li>
                                <li>Store file attachments as JSON array with metadata</li>
                                <li>Include SLA tracking fields (due_date, acknowledged_at)</li>
                            </ul>
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Database Design Exercise</h4>
                            <p><strong>Team Task:</strong> Create complete database schema for complaint management.</p>
                            <ul>
                                <li>Create all necessary migrations for the complaint system</li>
                                <li>Implement proper foreign key relationships</li>
                                <li>Add indexes for performance optimization</li>
                                <li>Create seeders for categories, roles, and test data</li>
                                <li>Validate schema against ISO 21001 requirements</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Module 2: Authentication & Role-Based Access Control -->
            <div class="module" id="module2">
                <div class="module-header">
                    <h2>Module 2: Authentication & Role-Based Access Control</h2>
                    <p>Implement secure authentication with student, teacher, and admin roles for complaint system</p>
                    <div class="module-meta">
                        <span class="meta-item">⏱️ Duration: Weeks 3-4</span>
                        <span class="meta-item">📚 Sprint 2</span>
                        <span class="meta-item">🔐 Security Focus</span>
                        <span class="meta-item">📊 Difficulty: Intermediate</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Implement Laravel Breeze authentication for complaint system users</li>
                            <li>Set up role-based access control using Spatie Laravel Permission</li>
                            <li>Create middleware for protecting complaint-related routes</li>
                            <li>Design user registration with role assignment (Student/Teacher/Admin)</li>
                            <li>Implement permission-based UI components and navigation</li>
                            <li>Build secure login system with proper validation and error handling</li>
                        </ul>
                    </div>

                    <!-- Lesson 2.1 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">2.1 Multi-Role Authentication System</h3>
                            <span class="lesson-duration">4 hours</span>
                        </div>
                        <p class="lesson-description">
                            Build a comprehensive authentication system that supports different user roles (Student, Teacher, Admin)
                            with appropriate access levels for complaint management functionality.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Laravel Breeze</span>
                            <span class="topic-tag">User Registration</span>
                            <span class="topic-tag">Role Assignment</span>
                            <span class="topic-tag">Login Validation</span>
                            <span class="topic-tag">Session Management</span>
                        </div>

                        <div class="code-example" data-lang="php">
<?php
// app/Models/User.php - Enhanced User Model for Complaint System

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name', 'email', 'password', 'student_id', 'department',
        'phone', 'role', 'is_active'
    ];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
    ];

    // Role-specific relationships
    public function submittedComplaints()
    {
        return $this->hasMany(Complaint::class, 'submitted_by');
    }

    public function assignedComplaints()
    {
        return $this->hasMany(Complaint::class, 'assigned_to');
    }

    public function responses()
    {
        return $this->hasMany(Response::class);
    }

    // Role checking methods
    public function isStudent(): bool
    {
        return $this->hasRole('student');
    }

    public function isTeacher(): bool
    {
        return $this->hasRole('teacher');
    }

    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    // Scope for filtering by role
    public function scopeByRole($query, $role)
    {
        return $query->role($role);
    }

    // Get user's dashboard route based on role
    public function getDashboardRoute(): string
    {
        return match(true) {
            $this->isAdmin() => route('admin.dashboard'),
            $this->isTeacher() => route('teacher.dashboard'),
            default => route('student.dashboard'),
        };
    }
}
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Authentication Exercise</h4>
                            <p><strong>Team Task:</strong> Implement complete authentication system with role-based access.</p>
                            <ul>
                                <li>Set up Laravel Breeze with custom registration fields</li>
                                <li>Create role seeder for Student, Teacher, Admin roles</li>
                                <li>Modify registration to include role selection and validation</li>
                                <li>Create custom login logic with role-based redirects</li>
                                <li>Test authentication flow for all three user types</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Lesson 2.2 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">2.2 Permission-Based Access Control</h3>
                            <span class="lesson-duration">3 hours</span>
                        </div>
                        <p class="lesson-description">
                            Implement granular permissions for complaint management operations, ensuring users can only
                            access and modify data appropriate to their role and responsibilities.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Spatie Permissions</span>
                            <span class="topic-tag">Middleware</span>
                            <span class="topic-tag">Route Protection</span>
                            <span class="topic-tag">UI Authorization</span>
                            <span class="topic-tag">Policy Classes</span>
                        </div>

                        <div class="code-example" data-lang="php">
<?php
// database/seeders/RolePermissionSeeder.php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Create permissions for complaint management
        $permissions = [
            // Complaint permissions
            'view complaints',
            'create complaints',
            'edit own complaints',
            'edit all complaints',
            'delete complaints',
            'assign complaints',
            'resolve complaints',
            'escalate complaints',

            // Response permissions
            'view responses',
            'create responses',
            'create internal responses',

            // User management permissions
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Analytics permissions
            'view analytics',
            'export reports',

            // Admin permissions
            'manage categories',
            'manage system settings',
            'view audit logs',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $studentRole = Role::create(['name' => 'student']);
        $studentRole->givePermissionTo([
            'view complaints',
            'create complaints',
            'edit own complaints',
            'view responses',
        ]);

        $teacherRole = Role::create(['name' => 'teacher']);
        $teacherRole->givePermissionTo([
            'view complaints',
            'edit all complaints',
            'assign complaints',
            'resolve complaints',
            'view responses',
            'create responses',
            'create internal responses',
            'view analytics',
        ]);

        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());
    }
}
                        </div>

                        <div class="best-practices">
                            <h4>✅ Security Best Practices</h4>
                            <ul>
                                <li>Always validate user permissions before displaying UI elements</li>
                                <li>Use middleware to protect routes at the application level</li>
                                <li>Implement policy classes for complex authorization logic</li>
                                <li>Log all permission-related actions for audit trails</li>
                                <li>Regularly review and update permission assignments</li>
                                <li>Use HTTPS in production for secure authentication</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Module 3: Filament Admin Panel for Complaint Management -->
            <div class="module" id="module3">
                <div class="module-header">
                    <h2>Module 3: Filament Admin Panel for Complaint Management</h2>
                    <p>Build comprehensive admin interface for managing complaints, users, and system analytics</p>
                    <div class="module-meta">
                        <span class="meta-item">⏱️ Duration: Weeks 5-7</span>
                        <span class="meta-item">📚 Sprint 3</span>
                        <span class="meta-item">🎛️ Admin Interface</span>
                        <span class="meta-item">📊 Difficulty: Advanced</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Create Filament resources for complaint, user, and category management</li>
                            <li>Build custom widgets for complaint analytics and ISO 21001 reporting</li>
                            <li>Implement advanced filtering and search for complaint tracking</li>
                            <li>Design custom forms for complaint resolution and status updates</li>
                            <li>Create dashboard widgets showing key performance indicators</li>
                            <li>Integrate file upload and attachment management</li>
                        </ul>
                    </div>

                    <!-- Lesson 3.1 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">3.1 Complaint Management Resource</h3>
                            <span class="lesson-duration">4 hours</span>
                        </div>
                        <p class="lesson-description">
                            Create a comprehensive Filament resource for managing complaints with advanced features
                            like status tracking, assignment management, and ISO 21001 compliance reporting.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Filament Resources</span>
                            <span class="topic-tag">Custom Forms</span>
                            <span class="topic-tag">Advanced Tables</span>
                            <span class="topic-tag">Bulk Actions</span>
                            <span class="topic-tag">Custom Actions</span>
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Filament Resource Exercise</h4>
                            <p><strong>Team Task:</strong> Build complete admin interface for complaint management.</p>
                            <ul>
                                <li>Create Complaint resource with comprehensive form fields</li>
                                <li>Add custom actions for status updates and assignments</li>
                                <li>Implement advanced filtering by status, priority, and date</li>
                                <li>Create bulk actions for mass status updates</li>
                                <li>Add file upload functionality for attachments</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Capstone Project & Final Deliverables -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; margin: 40px 0; border-radius: 15px; text-align: center;">
                <h2 style="color: white; margin-bottom: 20px; font-size: 2.5em;">🎓 Capstone Project Deliverables</h2>
                <p style="font-size: 1.3em; margin-bottom: 30px; opacity: 0.9;">
                    Final project presentation and demonstration of complete ISO 21001-compliant complaint management system
                </p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;">
                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                        <h3 style="color: white; margin-bottom: 10px;">🚀 Technical Deliverables</h3>
                        <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                            <li>Complete Laravel application</li>
                            <li>Filament admin panel</li>
                            <li>Role-based authentication</li>
                            <li>Responsive frontend</li>
                            <li>Database with test data</li>
                            <li>Documentation</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                        <h3 style="color: white; margin-bottom: 10px;">📊 ISO 21001 Compliance</h3>
                        <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                            <li>Quality management processes</li>
                            <li>Audit trail implementation</li>
                            <li>SLA monitoring system</li>
                            <li>Stakeholder engagement features</li>
                            <li>Continuous improvement metrics</li>
                            <li>Compliance reporting</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                        <h3 style="color: white; margin-bottom: 10px;">🎯 Project Presentation</h3>
                        <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                            <li>Live system demonstration</li>
                            <li>Architecture overview</li>
                            <li>Feature walkthrough</li>
                            <li>Code quality review</li>
                            <li>Team collaboration showcase</li>
                            <li>Future enhancement plans</li>
                        </ul>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <button onclick="window.print()" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; margin: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                        📄 Save Curriculum as PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>