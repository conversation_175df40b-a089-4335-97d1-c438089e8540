<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Complaint Management System - Complete Development Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .toc {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .toc h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .toc ul {
            list-style: none;
            padding-left: 0;
        }

        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .toc li:last-child {
            border-bottom: none;
        }

        .toc a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .toc a:hover {
            text-decoration: underline;
        }

        .sprint {
            margin-bottom: 50px;
            page-break-inside: avoid;
        }

        .sprint-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px 10px 0 0;
            margin-bottom: 0;
        }

        .sprint-header h2 {
            font-size: 2em;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .sprint-header .meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }

        .meta-item {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }

        .sprint-content {
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 10px 10px;
            padding: 25px;
        }

        .step {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }

        .step-header {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px 8px 0 0;
            padding: 20px;
            margin-bottom: 0;
        }

        .step-header h3 {
            color: #0c5460;
            margin-bottom: 10px;
            font-size: 1.4em;
        }

        .step-header .objective {
            font-weight: 600;
            color: #0c5460;
            margin-bottom: 8px;
        }

        .step-header .why {
            color: #495057;
            font-style: italic;
        }

        .step-content {
            background: white;
            border: 1px solid #bee5eb;
            border-top: none;
            border-radius: 0 0 8px 8px;
            padding: 20px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            margin: 15px 0;
            overflow-x: auto;
            position: relative;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: 8px;
            right: 12px;
            background: #4a5568;
            color: #e2e8f0;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            text-transform: uppercase;
        }

        .file-path {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 10px 0 5px 0;
            font-family: monospace;
            font-size: 0.9em;
            color: #495057;
        }

        .explanation {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .explanation h4 {
            color: #155724;
            margin-bottom: 10px;
        }

        .explanation ul {
            margin-left: 20px;
            color: #155724;
        }

        .next-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .next-steps h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .next-steps ol {
            margin-left: 20px;
            color: #856404;
        }

        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .warning h4 {
            color: #721c24;
            margin-bottom: 10px;
        }

        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .info h4 {
            color: #0056b3;
            margin-bottom: 10px;
        }

        @media print {
            body {
                font-size: 12px;
            }

            .sprint {
                page-break-before: always;
                margin-bottom: 30px;
            }

            .sprint:first-of-type {
                page-break-before: auto;
            }

            .step {
                page-break-inside: avoid;
                margin-bottom: 25px;
            }

            .code-block {
                font-size: 10px;
                padding: 15px;
            }

            .header {
                page-break-after: always;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .meta {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 Laravel Complaint Management System</h1>
            <p class="subtitle">Complete Development Guide for Final-Year Students</p>
            <p style="margin-top: 15px; opacity: 0.9;">ISO 21001 Compliant | Laravel 11 | Filament Admin | Spatie Permissions</p>
        </div>

        <div class="toc">
            <h2>📋 Table of Contents</h2>
            <ul>
                <li><a href="#sprint1">Sprint 1: Project Foundation & Authentication (Weeks 1-2)</a></li>
                <li><a href="#sprint2">Sprint 2: Database Design & Core Models (Weeks 3-4)</a></li>
                <li><a href="#sprint3">Sprint 3: Complaint Management System (Weeks 5-7)</a></li>
                <li><a href="#sprint4">Sprint 4: Filament Admin Panel (Weeks 8-9)</a></li>
                <li><a href="#sprint5">Sprint 5: Frontend & User Interfaces (Weeks 10-11)</a></li>
                <li><a href="#sprint6">Sprint 6: Testing, Security & Deployment (Weeks 12-14)</a></li>
            </ul>
        </div>

        <div class="info">
            <h4>🎯 Project Overview</h4>
            <p><strong>System Purpose:</strong> Educational institution complaint management with role-based access control</p>
            <p><strong>User Roles:</strong> Students (submit complaints), Teachers (review/respond), Admins (full management)</p>
            <p><strong>Compliance:</strong> ISO 21001 quality management standards for educational organizations</p>
            <p><strong>Tech Stack:</strong> Laravel 11, Filament 3.x, Tailwind CSS, MySQL, Spatie Permissions</p>
        </div>

        <!-- Sprint 1: Project Foundation & Authentication -->
        <div class="sprint" id="sprint1">
            <div class="sprint-header">
                <h2>🚀 Sprint 1: Project Foundation & Authentication</h2>
                <p>Set up Laravel project with authentication and role-based access control</p>
                <div class="meta">
                    <span class="meta-item">⏱️ Duration: Weeks 1-2</span>
                    <span class="meta-item">🎯 Goal: Working Authentication</span>
                    <span class="meta-item">👥 Roles: Student, Teacher, Admin</span>
                </div>
            </div>

            <div class="sprint-content">
                <!-- Step 1: Project Setup -->
                <div class="step">
                    <div class="step-header">
                        <h3>Step 1: Project Setup and Initial Configuration</h3>
                        <div class="objective">Objective: Create a fresh Laravel 11 project with all necessary dependencies</div>
                        <div class="why">Why: A proper foundation ensures we have all tools needed for authentication, admin panels, and role management. This establishes the development environment for ISO 21001 compliance.</div>
                    </div>

                    <div class="step-content">
                        <h4>Installation Commands</h4>
                        <div class="code-block" data-lang="bash">
# Create new Laravel 11 project
composer create-project laravel/laravel complaint-management-system
cd complaint-management-system

# Install required packages
composer require filament/filament:"^3.0"
composer require spatie/laravel-permission
composer require laravel/breeze --dev

# Set up environment
cp .env.example .env
php artisan key:generate
                        </div>

                        <div class="file-path">File: .env</div>
                        <div class="code-block" data-lang="env">
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=complaint_management
DB_USERNAME=root
DB_PASSWORD=your_password

APP_NAME="Complaint Management System"
APP_URL=http://localhost:8000
                        </div>

                        <div class="explanation">
                            <h4>What This Does</h4>
                            <ul>
                                <li>Creates a fresh Laravel 11 installation with latest features</li>
                                <li>Installs Filament for powerful admin panel functionality</li>
                                <li>Adds Spatie Permission for granular role-based access control</li>
                                <li>Sets up Laravel Breeze for authentication scaffolding</li>
                            </ul>
                        </div>

                        <div class="next-steps">
                            <h4>Next Steps</h4>
                            <ol>
                                <li>Create MySQL database named 'complaint_management'</li>
                                <li>Run <code>php artisan migrate</code> to set up basic Laravel tables</li>
                                <li>Test installation: <code>php artisan serve</code> and visit http://localhost:8000</li>
                                <li>Verify you see the Laravel welcome page</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Authentication Setup -->
                <div class="step">
                    <div class="step-header">
                        <h3>Step 2: Install and Configure Authentication</h3>
                        <div class="objective">Objective: Set up Laravel Breeze authentication with custom fields for complaint system users</div>
                        <div class="why">Why: Authentication is the foundation of our role-based system. We need additional user information (student ID, department) to properly categorize and route complaints according to ISO 21001 standards.</div>
                    </div>

                    <div class="step-content">
                        <h4>Install Breeze</h4>
                        <div class="code-block" data-lang="bash">
php artisan breeze:install blade
npm install && npm run dev
                        </div>

                        <h4>Modify User Migration</h4>
                        <div class="file-path">File: database/migrations/2014_10_12_000000_create_users_table.php</div>
                        <div class="code-block" data-lang="php">
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');

            // Additional fields for complaint system
            $table->string('student_id')->nullable()->unique();
            $table->string('department')->nullable();
            $table->string('phone')->nullable();
            $table->enum('user_type', ['student', 'teacher', 'admin'])->default('student');
            $table->boolean('is_active')->default(true);

            $table->rememberToken();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_type');
            $table->index('department');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
                        </div>

                        <div class="explanation">
                            <h4>What This Code Does</h4>
                            <ul>
                                <li>Extends the default user table with complaint-system specific fields</li>
                                <li>Adds student_id for institutional identification</li>
                                <li>Includes department for proper complaint routing</li>
                                <li>Creates user_type enum for role-based access control</li>
                                <li>Includes proper indexing for performance optimization</li>
                            </ul>
                        </div>

                        <div class="next-steps">
                            <h4>Next Steps</h4>
                            <ol>
                                <li>Run <code>php artisan migrate</code> to create the updated users table</li>
                                <li>Visit <code>/register</code> to test the registration form</li>
                                <li>Check database to verify tables were created correctly</li>
                                <li>We'll customize the registration form in the next step</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sprint 2: Database Design & Core Models -->
        <div class="sprint" id="sprint2">
            <div class="sprint-header">
                <h2>🗄️ Sprint 2: Database Design & Core Models</h2>
                <p>Create complaint management database schema and Eloquent models</p>
                <div class="meta">
                    <span class="meta-item">⏱️ Duration: Weeks 3-4</span>
                    <span class="meta-item">🎯 Goal: Complete Data Layer</span>
                    <span class="meta-item">📊 Focus: ISO 21001 Compliance</span>
                </div>
            </div>

            <div class="sprint-content">
                <!-- Step 4: Roles and Permissions Setup -->
                <div class="step">
                    <div class="step-header">
                        <h3>Step 4: Set Up Roles and Permissions</h3>
                        <div class="objective">Objective: Configure Spatie Laravel Permission package for role-based access control</div>
                        <div class="why">Why: ISO 21001 requires clear role definitions and access controls. This ensures students can only submit complaints, teachers can review and respond, and admins have full system access.</div>
                    </div>

                    <div class="step-content">
                        <h4>Publish Spatie Permission Migration</h4>
                        <div class="code-block" data-lang="bash">
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate
                        </div>

                        <h4>Create Role and Permission Seeder</h4>
                        <div class="file-path">File: database/seeders/RolePermissionSeeder.php</div>
                        <div class="code-block" data-lang="php">
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for complaint management
        $permissions = [
            // Complaint permissions
            'view own complaints',
            'view all complaints',
            'create complaints',
            'edit own complaints',
            'edit all complaints',
            'delete complaints',
            'assign complaints',
            'resolve complaints',
            'escalate complaints',

            // Response permissions
            'view responses',
            'create responses',
            'edit responses',
            'delete responses',

            // User management permissions
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Analytics and reporting
            'view analytics',
            'export reports',
            'view audit logs',

            // System administration
            'manage categories',
            'manage system settings',
            'access admin panel',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Student Role
        $studentRole = Role::create(['name' => 'student']);
        $studentRole->givePermissionTo([
            'view own complaints',
            'create complaints',
            'edit own complaints',
            'view responses',
        ]);

        // Teacher Role
        $teacherRole = Role::create(['name' => 'teacher']);
        $teacherRole->givePermissionTo([
            'view all complaints',
            'edit all complaints',
            'assign complaints',
            'resolve complaints',
            'view responses',
            'create responses',
            'edit responses',
            'view analytics',
        ]);

        // Admin Role
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        // Create default admin user
        $admin = User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'department' => 'Administration',
            'is_active' => true,
        ]);
        $admin->assignRole('admin');
    }
}
                        </div>

                        <div class="explanation">
                            <h4>What This Code Does</h4>
                            <ul>
                                <li>Creates granular permissions for complaint management operations</li>
                                <li>Defines three main roles: student, teacher, and admin</li>
                                <li>Assigns appropriate permissions to each role</li>
                                <li>Creates a default admin user for system management</li>
                                <li>Follows principle of least privilege for security</li>
                            </ul>
                        </div>

                        <div class="next-steps">
                            <h4>Next Steps</h4>
                            <ol>
                                <li>Update DatabaseSeeder to include RolePermissionSeeder</li>
                                <li>Run <code>php artisan db:seed --class=RolePermissionSeeder</code></li>
                                <li>Verify roles and permissions in database</li>
                                <li>Test <NAME_EMAIL> / password</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Complaint Database Schema -->
                <div class="step">
                    <div class="step-header">
                        <h3>Step 5: Create Complaint Management Database Schema</h3>
                        <div class="objective">Objective: Design and implement database tables for complaint management system</div>
                        <div class="why">Why: A well-designed database schema is crucial for ISO 21001 compliance, ensuring proper tracking, audit trails, and data integrity for complaint management processes.</div>
                    </div>

                    <div class="step-content">
                        <h4>Create Categories Migration</h4>
                        <div class="code-block" data-lang="bash">
php artisan make:migration create_categories_table
                        </div>

                        <div class="file-path">File: database/migrations/xxxx_create_categories_table.php</div>
                        <div class="code-block" data-lang="php">
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('color')->default('#3B82F6');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
                        </div>

                        <h4>Create Complaints Migration</h4>
                        <div class="code-block" data-lang="bash">
php artisan make:migration create_complaints_table
                        </div>

                        <div class="file-path">File: database/migrations/xxxx_create_complaints_table.php</div>
                        <div class="code-block" data-lang="php">
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('complaints', function (Blueprint $table) {
            $table->id();
            $table->string('reference_number')->unique();
            $table->string('title');
            $table->text('description');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('submitted_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');

            // ISO 21001 Compliance Fields
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('status', [
                'submitted', 'acknowledged', 'investigating',
                'resolved', 'closed', 'escalated'
            ])->default('submitted');

            // SLA and Quality Management
            $table->timestamp('due_date')->nullable();
            $table->timestamp('acknowledged_at')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->integer('satisfaction_rating')->nullable(); // 1-5 scale

            // Attachments and metadata
            $table->json('attachments')->nullable();
            $table->text('resolution_notes')->nullable();
            $table->boolean('is_anonymous')->default(false);

            $table->timestamps();

            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['priority', 'status']);
            $table->index(['submitted_by', 'status']);
            $table->index('reference_number');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('complaints');
    }
};
                        </div>

                        <div class="explanation">
                            <h4>What This Schema Provides</h4>
                            <ul>
                                <li><strong>Reference Number:</strong> Unique identifier for tracking complaints</li>
                                <li><strong>Status Tracking:</strong> Complete workflow from submission to resolution</li>
                                <li><strong>SLA Management:</strong> Due dates and acknowledgment tracking</li>
                                <li><strong>Quality Metrics:</strong> Satisfaction ratings for continuous improvement</li>
                                <li><strong>Audit Trail:</strong> Timestamps for all major status changes</li>
                                <li><strong>Performance Indexes:</strong> Optimized queries for reporting</li>
                            </ul>
                        </div>

                        <div class="next-steps">
                            <h4>Next Steps</h4>
                            <ol>
                                <li>Run <code>php artisan migrate</code> to create the tables</li>
                                <li>Verify tables in your database</li>
                                <li>We'll create the Eloquent models next</li>
                                <li>Then add seeders for test data</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sprint 3: Complaint Management System -->
        <div class="sprint" id="sprint3">
            <div class="sprint-header">
                <h2>📝 Sprint 3: Complaint Management System</h2>
                <p>Build core complaint submission and management functionality</p>
                <div class="meta">
                    <span class="meta-item">⏱️ Duration: Weeks 5-7</span>
                    <span class="meta-item">🎯 Goal: Working Complaint System</span>
                    <span class="meta-item">🔄 Focus: User Workflows</span>
                </div>
            </div>

            <div class="sprint-content">
                <div class="info">
                    <h4>Sprint 3 Overview</h4>
                    <p>In this sprint, you'll create the core complaint management functionality including:</p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>Eloquent models with relationships and business logic</li>
                        <li>Complaint submission forms for students</li>
                        <li>Teacher dashboard for reviewing complaints</li>
                        <li>Status tracking and workflow management</li>
                        <li>Response system for communication</li>
                    </ul>
                </div>

                <!-- Step 6: Eloquent Models -->
                <div class="step">
                    <div class="step-header">
                        <h3>Step 6: Create Eloquent Models</h3>
                        <div class="objective">Objective: Build Eloquent models with relationships and business logic for complaint management</div>
                        <div class="why">Why: Models encapsulate business logic and provide a clean interface for data manipulation. They ensure data integrity and implement the complaint workflow according to ISO 21001 standards.</div>
                    </div>

                    <div class="step-content">
                        <h4>Create Models</h4>
                        <div class="code-block" data-lang="bash">
php artisan make:model Category
php artisan make:model Complaint
php artisan make:model ComplaintResponse
                        </div>

                        <h4>Category Model</h4>
                        <div class="file-path">File: app/Models/Category.php</div>
                        <div class="code-block" data-lang="php">
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    protected $fillable = [
        'name', 'description', 'color', 'is_active', 'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)->orderBy('sort_order');
    }
}
                        </div>

                        <h4>Complaint Model</h4>
                        <div class="file-path">File: app/Models/Complaint.php</div>
                        <div class="code-block" data-lang="php">
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Complaint extends Model
{
    protected $fillable = [
        'reference_number', 'title', 'description', 'category_id',
        'priority', 'status', 'submitted_by', 'assigned_to',
        'due_date', 'attachments', 'is_anonymous', 'satisfaction_rating'
    ];

    protected $casts = [
        'attachments' => 'array',
        'due_date' => 'datetime',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
        'is_anonymous' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($complaint) {
            if (empty($complaint->reference_number)) {
                $complaint->reference_number = 'CMP-' . date('Y') . '-' .
                    str_pad(static::whereYear('created_at', date('Y'))->count() + 1, 4, '0', STR_PAD_LEFT);
            }
        });
    }

    // Relationships
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function submitter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_by');
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function responses(): HasMany
    {
        return $this->hasMany(ComplaintResponse::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeForUser($query, $userId, $role)
    {
        if ($role === 'student') {
            return $query->where('submitted_by', $userId);
        } elseif ($role === 'teacher') {
            return $query->where('assigned_to', $userId);
        }
        return $query; // Admin sees all
    }

    // Business logic methods
    public function acknowledge($userId = null)
    {
        $this->update([
            'status' => 'acknowledged',
            'acknowledged_at' => now(),
            'assigned_to' => $userId ?? auth()->id(),
        ]);
    }

    public function resolve($notes = null)
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolution_notes' => $notes,
        ]);
    }
}
                        </div>

                        <div class="explanation">
                            <h4>Key Model Features</h4>
                            <ul>
                                <li><strong>Auto-generated Reference Numbers:</strong> Unique tracking identifiers</li>
                                <li><strong>Relationship Methods:</strong> Clean access to related data</li>
                                <li><strong>Query Scopes:</strong> Reusable query logic for filtering</li>
                                <li><strong>Business Logic Methods:</strong> Encapsulated workflow operations</li>
                                <li><strong>Proper Casting:</strong> Automatic data type conversion</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Step 7: Controllers and Routes -->
                <div class="step">
                    <div class="step-header">
                        <h3>Step 7: Create Controllers and Routes</h3>
                        <div class="objective">Objective: Build controllers to handle complaint submission, viewing, and management</div>
                        <div class="why">Why: Controllers handle HTTP requests and coordinate between models and views. They implement the complaint workflow and ensure proper authorization for each user role.</div>
                    </div>

                    <div class="step-content">
                        <h4>Create Controllers</h4>
                        <div class="code-block" data-lang="bash">
php artisan make:controller ComplaintController --resource
php artisan make:controller DashboardController
php artisan make:controller Teacher/ComplaintController --resource
                        </div>

                        <h4>Student Complaint Controller</h4>
                        <div class="file-path">File: app/Http/Controllers/ComplaintController.php</div>
                        <div class="code-block" data-lang="php">
<?php

namespace App\Http\Controllers;

use App\Models\Complaint;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ComplaintController extends Controller
{
    public function index()
    {
        $complaints = Complaint::forUser(Auth::id(), Auth::user()->user_type)
            ->with(['category', 'assignee'])
            ->latest()
            ->paginate(10);

        return view('complaints.index', compact('complaints'));
    }

    public function create()
    {
        $categories = Category::active()->get();
        return view('complaints.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'priority' => 'required|in:low,medium,high,critical',
            'is_anonymous' => 'boolean',
            'attachments.*' => 'file|max:10240', // 10MB max
        ]);

        $complaint = Complaint::create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'category_id' => $validated['category_id'],
            'priority' => $validated['priority'],
            'submitted_by' => Auth::id(),
            'is_anonymous' => $request->boolean('is_anonymous'),
        ]);

        return redirect()->route('complaints.show', $complaint)
            ->with('success', 'Complaint submitted successfully. Reference: ' . $complaint->reference_number);
    }

    public function show(Complaint $complaint)
    {
        // Authorization check
        if (Auth::user()->user_type === 'student' && $complaint->submitted_by !== Auth::id()) {
            abort(403);
        }

        $complaint->load(['category', 'submitter', 'assignee', 'responses.user']);

        return view('complaints.show', compact('complaint'));
    }
}
                        </div>

                        <h4>Routes Setup</h4>
                        <div class="file-path">File: routes/web.php</div>
                        <div class="code-block" data-lang="php">
<?php

use App\Http\Controllers\ComplaintController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Teacher\ComplaintController as TeacherComplaintController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Authentication routes (provided by Breeze)
require __DIR__.'/auth.php';

// Protected routes
Route::middleware(['auth', 'verified'])->group(function () {

    // Student routes
    Route::middleware('role:student')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'student'])->name('dashboard');
        Route::resource('complaints', ComplaintController::class)->except(['edit', 'update', 'destroy']);
    });

    // Teacher routes
    Route::middleware('role:teacher')->prefix('teacher')->name('teacher.')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'teacher'])->name('dashboard');
        Route::resource('complaints', TeacherComplaintController::class);
    });

    // Admin routes (handled by Filament)
    Route::middleware('role:admin')->group(function () {
        // Admin routes will be handled by Filament in next sprint
    });
});
                        </div>

                        <div class="next-steps">
                            <h4>Next Steps</h4>
                            <ol>
                                <li>Create the dashboard views for each user type</li>
                                <li>Build the complaint submission form</li>
                                <li>Test the complaint creation workflow</li>
                                <li>Implement teacher complaint management interface</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Final Project Summary -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; margin: 40px 0; border-radius: 15px; text-align: center;">
            <h2 style="color: white; margin-bottom: 20px; font-size: 2.5em;">🎓 Project Completion Guide</h2>
            <p style="font-size: 1.3em; margin-bottom: 30px; opacity: 0.9;">
                Complete development roadmap for your ISO 21001-compliant Complaint Management System
            </p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;">
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h3 style="color: white; margin-bottom: 10px;">🚀 Remaining Sprints</h3>
                    <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                        <li>Sprint 4: Filament Admin Panel</li>
                        <li>Sprint 5: Frontend & UI</li>
                        <li>Sprint 6: Testing & Deployment</li>
                        <li>Final: Documentation & Presentation</li>
                    </ul>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h3 style="color: white; margin-bottom: 10px;">📊 ISO 21001 Features</h3>
                    <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                        <li>Quality management processes</li>
                        <li>Audit trail implementation</li>
                        <li>SLA monitoring system</li>
                        <li>Stakeholder engagement</li>
                        <li>Continuous improvement metrics</li>
                    </ul>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h3 style="color: white; margin-bottom: 10px;">🎯 Final Deliverables</h3>
                    <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                        <li>Complete Laravel application</li>
                        <li>Filament admin panel</li>
                        <li>Role-based authentication</li>
                        <li>Responsive frontend</li>
                        <li>Documentation & testing</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 30px;">
                <button onclick="window.print()" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; margin: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    📄 Save as PDF
                </button>
            </div>
        </div>
    </div>
</body>
</html>