<?php

echo "🔧 COMPREHENSIVE CODEBASE FIX - Checking and fixing all issues...\n\n";

// Issue 1: Fix middleware aliases in routes
echo "📝 Step 1: Fixing middleware aliases in routes...\n";

$webRoutesContent = file_get_contents('routes/web.php');
$webRoutesContent = str_replace('ensure.student', 'student', $webRoutesContent);
$webRoutesContent = str_replace('ensure.teacher', 'teacher', $webRoutesContent);
file_put_contents('routes/web.php', $webRoutesContent);
echo "✅ Fixed middleware aliases in web routes\n";

// Issue 2: Fix bootstrap/app.php - remove non-existent classes
echo "\n📝 Step 2: Fixing bootstrap/app.php...\n";

$bootstrapContent = file_get_contents('bootstrap/app.php');

// Remove the problematic job reference
$bootstrapContent = str_replace(
    "        // Aggregate analytics data daily\n        \$schedule->job(new \\App\\Jobs\\AggregateComplaintAnalytics())->daily();",
    "        // Aggregate analytics data daily (disabled until job is implemented)\n        // \$schedule->job(new \\App\\Jobs\\AggregateComplaintAnalytics())->daily();",
    $bootstrapContent
);

file_put_contents('bootstrap/app.php', $bootstrapContent);
echo "✅ Fixed bootstrap/app.php\n";

// Issue 3: Create missing auth controllers directory and files
echo "\n📝 Step 3: Creating missing auth controllers...\n";

$authDir = 'app/Http/Controllers/Auth';
if (!is_dir($authDir)) {
    mkdir($authDir, 0755, true);
    echo "✅ Created Auth controllers directory\n";
}

// Create basic auth controllers if they don't exist
$authControllers = [
    'AuthenticatedSessionController.php' => '<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthenticatedSessionController extends Controller
{
    public function create()
    {
        return view("auth.login");
    }

    public function store(Request $request)
    {
        $credentials = $request->validate([
            "email" => ["required", "email"],
            "password" => ["required"],
        ]);

        if (Auth::attempt($credentials)) {
            $request->session()->regenerate();
            
            $user = Auth::user();
            if ($user->hasRole("admin") || $user->hasRole("teacher")) {
                return redirect()->intended("/admin");
            } else {
                return redirect()->intended("/student/dashboard");
            }
        }

        return back()->withErrors([
            "email" => "The provided credentials do not match our records.",
        ]);
    }

    public function destroy(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect("/");
    }
}',
    'RegisteredUserController.php' => '<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class RegisteredUserController extends Controller
{
    public function create()
    {
        return view("auth.register");
    }

    public function store(Request $request)
    {
        $request->validate([
            "name" => ["required", "string", "max:255"],
            "email" => ["required", "string", "email", "max:255", "unique:users"],
            "password" => ["required", "confirmed", Rules\Password::defaults()],
            "department" => ["required", "string", "max:255"],
        ]);

        $user = User::create([
            "name" => $request->name,
            "email" => $request->email,
            "password" => Hash::make($request->password),
            "department" => $request->department,
            "user_type" => "student",
            "email_verified_at" => now(),
        ]);

        $user->assignRole("student");

        return redirect("/student/dashboard");
    }
}'
];

foreach ($authControllers as $filename => $content) {
    $filePath = $authDir . '/' . $filename;
    if (!file_exists($filePath)) {
        file_put_contents($filePath, $content);
        echo "✅ Created $filename\n";
    }
}

// Issue 4: Create missing auth views
echo "\n📝 Step 4: Creating missing auth views...\n";

$authViewsDir = 'resources/views/auth';
if (!is_dir($authViewsDir)) {
    mkdir($authViewsDir, 0755, true);
    echo "✅ Created auth views directory\n";
}

// Create basic login view
$loginView = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Complaints Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-center mb-6">Login</h2>
            
            @if ($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    @foreach ($errors->all() as $error)
                        <p>{{ $error }}</p>
                    @endforeach
                </div>
            @endif

            <form method="POST" action="{{ route(\'login\') }}">
                @csrf
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                    <input type="email" name="email" value="{{ old(\'email\') }}" required 
                           class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500">
                </div>
                
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                    <input type="password" name="password" required 
                           class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500">
                </div>
                
                <button type="submit" class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600">
                    Login
                </button>
            </form>
            
            <div class="mt-4 text-center">
                <p class="text-sm text-gray-600">Demo Credentials:</p>
                <p class="text-xs text-gray-500">Admin: <EMAIL> / 123456</p>
                <p class="text-xs text-gray-500">Student: <EMAIL> / 123456</p>
            </div>
        </div>
    </div>
</body>
</html>';

file_put_contents($authViewsDir . '/login.blade.php', $loginView);
echo "✅ Created login view\n";

// Issue 5: Fix .env file
echo "\n📝 Step 5: Creating proper .env file...\n";

$envContent = 'APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
';

file_put_contents('.env', $envContent);
echo "✅ Created .env file\n";

// Issue 6: Create required directories
echo "\n📝 Step 6: Creating required directories...\n";

$dirs = [
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache',
    'database'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    }
}

// Issue 7: Create database file
echo "\n📝 Step 7: Creating database file...\n";

$dbFile = 'database/database.sqlite';
if (file_exists($dbFile)) {
    unlink($dbFile);
}
file_put_contents($dbFile, '');
echo "✅ Created database file\n";

// Issue 8: Clear problematic cache files
echo "\n📝 Step 8: Clearing cache files...\n";

$cacheFiles = [
    'bootstrap/cache/config.php',
    'bootstrap/cache/routes-v7.php',
    'bootstrap/cache/services.php',
    'bootstrap/cache/packages.php'
];

foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "✅ Removed cache file: $file\n";
    }
}

echo "\n🎉 COMPREHENSIVE FIX COMPLETE!\n";
echo "All major issues have been identified and fixed.\n\n";
echo "Next steps:\n";
echo "1. php artisan key:generate --force\n";
echo "2. php artisan config:clear\n";
echo "3. php artisan migrate:fresh --seed\n";
echo "4. php artisan serve\n";
