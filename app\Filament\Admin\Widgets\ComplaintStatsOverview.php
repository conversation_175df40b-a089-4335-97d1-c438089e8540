<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Complaint;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ComplaintStatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Complaints', Complaint::count())
                ->description('All complaints in the system')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),
            
            Stat::make('Open Complaints', Complaint::whereIn('status', ['new', 'assigned', 'in_progress', 'reopened'])->count())
                ->description('Complaints that need attention')
                ->descriptionIcon('heroicon-m-exclamation-circle')
                ->color('warning'),
            
            Stat::make('Resolved Complaints', Complaint::whereIn('status', ['resolved', 'closed'])->count())
                ->description('Successfully resolved complaints')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
            
            Stat::make('Critical Priority', Complaint::where('priority', 'critical')->count())
                ->description('High priority complaints')
                ->descriptionIcon('heroicon-m-fire')
                ->color('danger'),
            
            Stat::make('Total Users', User::count())
                ->description('Registered users')
                ->descriptionIcon('heroicon-m-users')
                ->color('info'),
        ];
    }
}
