<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for each guard
        $guards = ['web', 'admin', 'teacher', 'student'];

        foreach ($guards as $guard) {
            $this->createPermissions($guard);
        }

        // Create roles for each guard
        $this->createRoles();

        // Create default users
        $this->createDefaultUsers();
    }

    private function createPermissions(string $guard): void
    {
        $permissions = [
            // Complaint permissions
            'view complaints',
            'create complaints',
            'edit complaints',
            'delete complaints',
            'assign complaints',
            'resolve complaints',
            'close complaints',
            'reopen complaints',
            'view all complaints',
            'view own complaints',

            // Comment permissions
            'view comments',
            'create comments',
            'edit comments',
            'delete comments',
            'view internal comments',
            'create internal comments',

            // Category permissions
            'view categories',
            'create categories',
            'edit categories',
            'delete categories',

            // Attachment permissions
            'view attachments',
            'upload attachments',
            'delete attachments',

            // User permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
            'assign roles',

            // Report permissions
            'view reports',
            'create reports',
            'export reports',

            // Settings permissions
            'manage settings',

            // SLA permissions
            'view sla',
            'edit sla',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => $guard]);
        }
    }

    private function createRoles(): void
    {
        // Create super-admin role
        $superAdminRole = Role::create(['name' => 'super-admin', 'guard_name' => 'web']);
        $superAdminRole->givePermissionTo(Permission::where('guard_name', 'web')->get());

        // Create admin role for web guard
        $adminRole = Role::create(['name' => 'admin', 'guard_name' => 'web']);
        $adminRole->givePermissionTo(Permission::where('guard_name', 'web')->get());

        // Create admin role for admin guard
        $adminPanelRole = Role::create(['name' => 'admin', 'guard_name' => 'admin']);
        $adminPanelRole->givePermissionTo(Permission::where('guard_name', 'admin')->get());

        // Create teacher role for web guard
        $teacherRole = Role::create(['name' => 'teacher', 'guard_name' => 'web']);
        $teacherRole->givePermissionTo([
            'view complaints',
            'create complaints',
            'edit complaints',
            'assign complaints',
            'resolve complaints',
            'close complaints',
            'view categories',
            'view comments',
            'create comments',
            'edit comments',
            'view internal comments',
            'create internal comments',
            'view attachments',
            'upload attachments',
            'view reports',
            'view sla',
        ]);

        // Create teacher role for teacher guard
        $teacherPanelRole = Role::create(['name' => 'teacher', 'guard_name' => 'teacher']);
        $teacherPanelRole->givePermissionTo(Permission::where('guard_name', 'teacher')->get());

        // Create student role for web guard
        $studentRole = Role::create(['name' => 'student', 'guard_name' => 'web']);
        $studentRole->givePermissionTo([
            'view complaints',
            'create complaints',
            'edit complaints',
            'reopen complaints',
            'view own complaints',
            'view categories',
            'view comments',
            'create comments',
            'view attachments',
            'upload attachments',
        ]);

        // Create student role for student guard
        $studentPanelRole = Role::create(['name' => 'student', 'guard_name' => 'student']);
        $studentPanelRole->givePermissionTo(Permission::where('guard_name', 'student')->get());
    }

    private function createDefaultUsers(): void
    {
        // Create super-admin user
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $superAdmin->assignRole('super-admin');

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $admin->assignRole('admin');

        // Create teacher user
        $teacher = User::create([
            'name' => 'Teacher User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $teacher->assignRole('teacher');

        // Create student user
        $student = User::create([
            'name' => 'Student User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $student->assignRole('student');
    }
}
