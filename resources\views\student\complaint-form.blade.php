<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800">Submit New Complaint</h2>
                </div>

                <form class="p-6 space-y-6">
                    <!-- Title Field -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                        <input type="text" id="title" name="title" required
                            class="form-input"
                            placeholder="Enter a descriptive title">
                    </div>

                    <!-- Category Field -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select id="category" name="category_id" required
                            class="form-select">
                            <option value="">Select a category</option>
                            <option value="1">Academic Issues</option>
                            <option value="2">Administrative Issues</option>
                            <option value="3">Facility Issues</option>
                            <option value="4">Technical Issues</option>
                            <option value="5">Other</option>
                        </select>
                    </div>

                    <!-- Priority Field (Hidden - will be set by admin) -->
                    <input type="hidden" name="priority" value="medium">

                    <!-- Description Field -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="description" name="description" rows="5" required
                            class="form-textarea"
                            placeholder="Please provide detailed information about your complaint"></textarea>
                        <p class="mt-1 text-sm text-gray-500">Please be as specific as possible to help us address your complaint effectively.</p>
                    </div>

                    <!-- File Attachments -->
                    <div>
                        <label for="attachments" class="block text-sm font-medium text-gray-700 mb-1">Attachments (Optional)</label>
                        <div id="drop-zone" class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md transition-colors duration-200 hover:border-blue-400">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>Upload files</span>
                                        <input id="file-upload" name="attachments[]" type="file" class="sr-only" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">
                                    PNG, JPG, PDF, DOC, DOCX up to 10MB each
                                </p>
                            </div>
                        </div>

                        <!-- File Preview Area -->
                        <div id="file-preview" class="mt-4 space-y-2 hidden">
                            <h4 class="text-sm font-medium text-gray-700">Selected Files:</h4>
                            <div id="file-list" class="space-y-2"></div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button type="button" class="btn btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            Submit Complaint
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Drag and drop functionality
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-upload');
        const filePreview = document.getElementById('file-preview');
        const fileList = document.getElementById('file-list');
        let selectedFiles = [];

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);

        // Handle file input change
        fileInput.addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            dropZone.classList.add('border-blue-500', 'bg-blue-50');
        }

        function unhighlight(e) {
            dropZone.classList.remove('border-blue-500', 'bg-blue-50');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        function handleFiles(files) {
            ([...files]).forEach(uploadFile);
            updateFileInput();
            showFilePreview();
        }

        function uploadFile(file) {
            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            if (!allowedTypes.includes(file.type)) {
                alert(`File type ${file.type} is not allowed. Please upload JPG, PNG, PDF, DOC, or DOCX files.`);
                return;
            }

            // Validate file size (10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert(`File ${file.name} is too large. Maximum size is 10MB.`);
                return;
            }

            // Check if file already exists
            if (selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
                alert(`File ${file.name} is already selected.`);
                return;
            }

            selectedFiles.push(file);
            addFileToPreview(file);
        }

        function addFileToPreview(file) {
            const fileItem = document.createElement('div');
            fileItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-md';

            const fileInfo = document.createElement('div');
            fileInfo.className = 'flex items-center space-x-3';

            const fileIcon = getFileIcon(file.type);
            const fileName = document.createElement('span');
            fileName.className = 'text-sm font-medium text-gray-900';
            fileName.textContent = file.name;

            const fileSize = document.createElement('span');
            fileSize.className = 'text-xs text-gray-500';
            fileSize.textContent = formatFileSize(file.size);

            fileInfo.appendChild(fileIcon);
            fileInfo.appendChild(fileName);
            fileInfo.appendChild(fileSize);

            const removeButton = document.createElement('button');
            removeButton.type = 'button';
            removeButton.className = 'text-red-600 hover:text-red-800';
            removeButton.innerHTML = '×';
            removeButton.onclick = () => removeFile(file, fileItem);

            fileItem.appendChild(fileInfo);
            fileItem.appendChild(removeButton);
            fileList.appendChild(fileItem);
        }

        function removeFile(file, fileItem) {
            selectedFiles = selectedFiles.filter(f => f !== file);
            fileItem.remove();
            updateFileInput();

            if (selectedFiles.length === 0) {
                filePreview.classList.add('hidden');
            }
        }

        function updateFileInput() {
            const dt = new DataTransfer();
            selectedFiles.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;
        }

        function showFilePreview() {
            if (selectedFiles.length > 0) {
                filePreview.classList.remove('hidden');
            }
        }

        function getFileIcon(fileType) {
            const icon = document.createElement('div');
            icon.className = 'w-8 h-8 flex items-center justify-center rounded text-white text-xs font-bold';

            if (fileType.startsWith('image/')) {
                icon.className += ' bg-green-500';
                icon.textContent = 'IMG';
            } else if (fileType === 'application/pdf') {
                icon.className += ' bg-red-500';
                icon.textContent = 'PDF';
            } else if (fileType.includes('word')) {
                icon.className += ' bg-blue-500';
                icon.textContent = 'DOC';
            } else {
                icon.className += ' bg-gray-500';
                icon.textContent = 'FILE';
            }

            return icon;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const category = document.getElementById('category').value;
            const description = document.getElementById('description').value.trim();

            if (!title) {
                e.preventDefault();
                alert('Please enter a title for your complaint.');
                document.getElementById('title').focus();
                return;
            }

            if (!category) {
                e.preventDefault();
                alert('Please select a category for your complaint.');
                document.getElementById('category').focus();
                return;
            }

            if (!description) {
                e.preventDefault();
                alert('Please provide a description of your complaint.');
                document.getElementById('description').focus();
                return;
            }

            if (description.length < 10) {
                e.preventDefault();
                alert('Please provide a more detailed description (at least 10 characters).');
                document.getElementById('description').focus();
                return;
            }
        });

        // Auto-save functionality (save to localStorage)
        const formElements = ['title', 'category', 'description'];
        const autoSaveKey = 'complaint_draft';

        // Load saved data
        function loadDraft() {
            const savedData = localStorage.getItem(autoSaveKey);
            if (savedData) {
                const data = JSON.parse(savedData);
                formElements.forEach(field => {
                    const element = document.getElementById(field);
                    if (element && data[field]) {
                        element.value = data[field];
                    }
                });
            }
        }

        // Save data
        function saveDraft() {
            const data = {};
            formElements.forEach(field => {
                const element = document.getElementById(field);
                if (element) {
                    data[field] = element.value;
                }
            });
            localStorage.setItem(autoSaveKey, JSON.stringify(data));
        }

        // Clear draft
        function clearDraft() {
            localStorage.removeItem(autoSaveKey);
        }

        // Auto-save every 30 seconds
        setInterval(saveDraft, 30000);

        // Save on input change
        formElements.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                element.addEventListener('input', saveDraft);
            }
        });

        // Clear draft on successful submission
        document.querySelector('form').addEventListener('submit', function() {
            setTimeout(clearDraft, 1000);
        });

        // Load draft on page load
        document.addEventListener('DOMContentLoaded', loadDraft);
    </script>
</x-app-layout>
