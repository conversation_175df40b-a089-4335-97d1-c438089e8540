@echo off
echo ========================================
echo PERMISSION ERROR FIX
echo ========================================

echo Fixing Spatie Permission initialization error...

echo Step 1: Clear all caches...
if exist bootstrap\cache rmdir /s /q bootstrap\cache 2>nul
if exist storage\framework\cache rmdir /s /q storage\framework\cache 2>nul
mkdir bootstrap\cache 2>nul
mkdir storage\framework\cache 2>nul
mkdir storage\framework\cache\data 2>nul
mkdir storage\framework\sessions 2>nul
mkdir storage\framework\views 2>nul

echo Step 2: Create .env with permission cache disabled...
(
echo APP_NAME="Complaints Management System"
echo APP_ENV=local
echo APP_KEY=base64:SGVsbG9Xb3JsZEhlbGxvV29ybGRIZWxsb1dvcmxkSGVsbG9Xb3JsZA==
echo APP_DEBUG=true
echo APP_URL=http://localhost
echo.
echo DB_CONNECTION=sqlite
echo CACHE_DRIVER=file
echo SESSION_DRIVER=file
echo QUEUE_CONNECTION=sync
echo.
echo PERMISSION_CACHE_ENABLED=false
echo.
echo MAIL_MAILER=log
) > .env

echo Step 3: Create fresh database...
if exist database\database.sqlite del database\database.sqlite
echo. > database\database.sqlite

echo Step 4: Run migrations...
php artisan migrate:fresh --force

echo Step 5: Create roles manually...
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();
use Spatie\\Permission\\Models\\Role;
Role::create(['name' => 'admin']);
Role::create(['name' => 'teacher']);
Role::create(['name' => 'student']);
echo 'Roles created\n';
"

echo Step 6: Create users...
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();
use App\\Models\\User;
use Illuminate\\Support\\Facades\\Hash;
\$admin = User::create(['name' => 'Admin', 'email' => '<EMAIL>', 'password' => Hash::make('123456'), 'email_verified_at' => now(), 'department' => 'IT', 'user_type' => 'admin']);
\$admin->assignRole('admin');
\$student = User::create(['name' => 'Student', 'email' => '<EMAIL>', 'password' => Hash::make('123456'), 'email_verified_at' => now(), 'department' => 'CS', 'user_type' => 'student']);
\$student->assignRole('student');
echo 'Users created\n';
"

echo.
echo ========================================
echo PERMISSION ERROR FIXED!
echo ========================================
echo.
echo Login: http://localhost:8000/login
echo Credentials: <EMAIL> / 123456
echo.
php artisan serve
