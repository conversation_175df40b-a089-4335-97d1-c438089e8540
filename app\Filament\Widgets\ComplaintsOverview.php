<?php

namespace App\Filament\Widgets;

use App\Models\Complaint;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Carbon;

class ComplaintsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $totalComplaints = Complaint::count();
        $newComplaints = Complaint::where('status', 'new')->count();
        $inProgressComplaints = Complaint::where('status', 'in_progress')->count();
        $resolvedComplaints = Complaint::where('status', 'resolved')->count();
        $overdueComplaints = Complaint::where('due_date', '<', now())
            ->whereNotIn('status', ['resolved', 'closed'])
            ->count();
        
        // Calculate trends (compared to last month)
        $lastMonthTotal = Complaint::where('created_at', '>=', now()->subMonth(2))
            ->where('created_at', '<', now()->subMonth())
            ->count();
        $thisMonthTotal = Complaint::where('created_at', '>=', now()->subMonth())->count();
        $totalTrend = $lastMonthTotal > 0 ? (($thisMonthTotal - $lastMonthTotal) / $lastMonthTotal) * 100 : 0;
        
        $lastMonthResolved = Complaint::where('resolution_date', '>=', now()->subMonth(2))
            ->where('resolution_date', '<', now()->subMonth())
            ->count();
        $thisMonthResolved = Complaint::where('resolution_date', '>=', now()->subMonth())->count();
        $resolvedTrend = $lastMonthResolved > 0 ? (($thisMonthResolved - $lastMonthResolved) / $lastMonthResolved) * 100 : 0;

        return [
            Stat::make('Total Complaints', $totalComplaints)
                ->description($totalTrend >= 0 ? '+' . number_format($totalTrend, 1) . '% from last month' : number_format($totalTrend, 1) . '% from last month')
                ->descriptionIcon($totalTrend >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($totalTrend >= 0 ? 'success' : 'danger')
                ->chart([7, 2, 10, 3, 15, 4, 17]),
            
            Stat::make('New Complaints', $newComplaints)
                ->description('Awaiting assignment')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('warning'),
            
            Stat::make('In Progress', $inProgressComplaints)
                ->description('Being actively worked on')
                ->descriptionIcon('heroicon-m-clock')
                ->color('info'),
            
            Stat::make('Resolved', $resolvedComplaints)
                ->description($resolvedTrend >= 0 ? '+' . number_format($resolvedTrend, 1) . '% from last month' : number_format($resolvedTrend, 1) . '% from last month')
                ->descriptionIcon($resolvedTrend >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color('success'),
            
            Stat::make('Overdue', $overdueComplaints)
                ->description('Past due date')
                ->descriptionIcon('heroicon-m-exclamation-circle')
                ->color($overdueComplaints > 0 ? 'danger' : 'success'),
        ];
    }
}
