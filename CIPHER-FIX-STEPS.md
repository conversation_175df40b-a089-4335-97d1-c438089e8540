# Cipher Error Fix - Step by Step

The "Unsupported cipher or incorrect key length" error means your APP_KEY is malformed or missing.

## Quick Fix (Choose ONE)

### Option 1: One Command Fix
```bash
ONE-COMMAND-FIX.bat
```

### Option 2: Cipher Fix Script
```bash
FIX-CIPHER-ERROR.bat
```

### Option 3: PHP Fix Script
```bash
php fix-cipher.php
php artisan config:clear
php artisan migrate:fresh --seed
php artisan serve
```

## Manual Fix (if scripts don't work)

### Step 1: Delete problematic files
```bash
del .env
del bootstrap\cache\config.php
del bootstrap\cache\routes-v7.php
del bootstrap\cache\services.php
```

### Step 2: Create new .env file
Create a file named `.env` with this content:
```env
APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### Step 3: Generate proper encryption key
```bash
php artisan key:generate --force
```

### Step 4: Verify the key was generated
```bash
php artisan config:show app.key
```
You should see something like: `base64:LONG_STRING_HERE`

### Step 5: Clear caches
```bash
php artisan config:clear
php artisan cache:clear
```

### Step 6: Create database
```bash
echo. > database\database.sqlite
```

### Step 7: Run migrations
```bash
php artisan migrate:fresh --force
```

### Step 8: Seed database
```bash
php artisan db:seed --force
```

### Step 9: Start server
```bash
php artisan serve
```

## What Causes This Error

1. **Missing APP_KEY**: The .env file doesn't have an APP_KEY
2. **Malformed APP_KEY**: The key is not in the correct format
3. **Wrong cipher**: The key length doesn't match the cipher
4. **Cached config**: Old configuration is cached

## Supported Ciphers

Laravel supports these ciphers:
- `aes-128-cbc` (requires 16-byte key)
- `aes-256-cbc` (requires 32-byte key) - **Default**
- `aes-128-gcm` (requires 16-byte key)
- `aes-256-gcm` (requires 32-byte key)

## Verification

After fixing, verify the key is correct:
```bash
php artisan tinker
>>> config('app.key')
>>> exit
```

Should return something like: `"base64:LONG_STRING_HERE"`

## Expected Result

After fixing:
- ✅ No cipher errors
- ✅ Home page loads correctly
- ✅ Can access admin panel: http://localhost:8000/admin
- ✅ Can login with: <EMAIL> / 123456

## If Still Getting Errors

1. **Check PHP OpenSSL extension**:
   ```bash
   php -m | findstr openssl
   ```

2. **Verify .env file encoding**: Make sure it's UTF-8 without BOM

3. **Check file permissions**: Ensure .env is readable

4. **Nuclear option**: Delete everything and start fresh:
   ```bash
   del .env
   del database\database.sqlite
   rmdir /s bootstrap\cache
   FIX-CIPHER-ERROR.bat
   ```

The cipher error should be completely resolved after following any of these methods!
