<?php

echo "🔧 Simple System Fix - Starting...\n";

// Step 1: Create .env file
if (!file_exists('.env')) {
    $envContent = "APP_NAME=\"Complaints Management System\"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=\"<EMAIL>\"
MAIL_FROM_NAME=\"\${APP_NAME}\"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME=\"\${APP_NAME}\"
";
    
    file_put_contents('.env', $envContent);
    echo "✅ Created .env file\n";
}

// Step 2: Create database directory and file
if (!is_dir('database')) {
    mkdir('database', 0755, true);
}

$dbFile = 'database/database.sqlite';
if (!file_exists($dbFile)) {
    file_put_contents($dbFile, '');
    echo "✅ Created SQLite database\n";
}

echo "✅ Basic setup complete. Now run these commands:\n\n";
echo "1. php artisan key:generate\n";
echo "2. php artisan migrate:fresh\n";
echo "3. php artisan db:seed\n";
echo "4. php artisan serve\n\n";
echo "Copy and paste each command one by one.\n";
