<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RolesAndPermissionsSeeder::class,
            ComplaintCategorySeeder::class,
            SLASettingSeeder::class,
        ]);

        // Create demo users
        $this->createDemoUsers();
    }

    /**
     * Create demo users for testing
     */
    private function createDemoUsers(): void
    {
        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'IT',
                'user_type' => 'admin',
            ]
        );
        $admin->assignRole('admin');

        // Create teacher user
        $teacher = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Teacher',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'Academic',
                'user_type' => 'teacher',
            ]
        );
        $teacher->assignRole('teacher');

        // Create student user
        $student = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Jane Student',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'Computer Science',
                'user_type' => 'student',
            ]
        );
        $student->assignRole('student');

        $this->command->info('Demo users created:');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Teacher: <EMAIL> / password');
        $this->command->info('Student: <EMAIL> / password');
    }
}
