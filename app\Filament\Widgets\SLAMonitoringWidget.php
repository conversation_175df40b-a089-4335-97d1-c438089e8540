<?php

namespace App\Filament\Widgets;

use App\Models\Complaint;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SLAMonitoringWidget extends BaseWidget
{
    protected static ?int $sort = 4;

    protected function getHeading(): string
    {
        return 'SLA Monitoring';
    }

    protected function getStats(): array
    {
        // Get active complaints
        $activeComplaints = Complaint::whereNotIn('status', ['resolved', 'closed'])->get();

        $onTime = 0;
        $atRisk = 0;
        $breached = 0;
        $totalComplaints = $activeComplaints->count();

        foreach ($activeComplaints as $complaint) {
            $daysUntilDue = $complaint->getDaysUntilDue();

            if ($daysUntilDue === null) {
                continue;
            }

            if ($daysUntilDue < 0) {
                $breached++;
            } elseif ($daysUntilDue <= 1) {
                $atRisk++;
            } else {
                $onTime++;
            }
        }

        // Calculate SLA compliance percentage
        $slaCompliance = $totalComplaints > 0 ? (($onTime + $atRisk) / $totalComplaints) * 100 : 100;

        // Get complaints due today
        $dueTodayCount = Complaint::whereDate('due_date', today())
            ->whereNotIn('status', ['resolved', 'closed'])
            ->count();

        // Get average resolution time for resolved complaints this month
        $avgResolutionTime = Complaint::where('status', 'resolved')
            ->whereMonth('resolution_date', now()->month)
            ->whereYear('resolution_date', now()->year)
            ->get()
            ->avg(function ($complaint) {
                return $complaint->created_at->diffInDays($complaint->resolution_date);
            });

        return [
            Stat::make('SLA Compliance', number_format($slaCompliance, 1) . '%')
                ->description('Overall SLA compliance rate')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($slaCompliance >= 90 ? 'success' : ($slaCompliance >= 75 ? 'warning' : 'danger'))
                ->chart([65, 70, 75, 80, 85, 90, $slaCompliance]),

            Stat::make('On Time', $onTime)
                ->description('Complaints within SLA')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('At Risk', $atRisk)
                ->description('Due within 1 day')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('warning'),

            Stat::make('Breached', $breached)
                ->description('Past due date')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),

            Stat::make('Due Today', $dueTodayCount)
                ->description('Complaints due today')
                ->descriptionIcon('heroicon-m-calendar')
                ->color($dueTodayCount > 0 ? 'warning' : 'success'),

            Stat::make('Avg Resolution', $avgResolutionTime ? number_format($avgResolutionTime, 1) . ' days' : 'N/A')
                ->description('This month average')
                ->descriptionIcon('heroicon-m-clock')
                ->color('info'),
        ];
    }
}
