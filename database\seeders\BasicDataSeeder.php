<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\ComplaintCategory;
use Illuminate\Support\Facades\Hash;

class BasicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create complaint categories
        $categories = [
            [
                'name' => 'Academic Issues',
                'description' => 'Issues related to courses, grading, curriculum',
                'iso_code' => 'AC001',
                'sla_days' => 7,
                'is_active' => true,
            ],
            [
                'name' => 'Facilities & Infrastructure',
                'description' => 'Problems with buildings, equipment, maintenance',
                'iso_code' => 'FA001',
                'sla_days' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'Student Services',
                'description' => 'Issues with registration, financial aid, counseling',
                'iso_code' => 'SS001',
                'sla_days' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Faculty & Staff',
                'description' => 'Concerns about teaching quality, staff behavior',
                'iso_code' => 'FS001',
                'sla_days' => 10,
                'is_active' => true,
            ],
            [
                'name' => 'Technology & IT',
                'description' => 'Computer lab, network, software issues',
                'iso_code' => 'IT001',
                'sla_days' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Safety & Security',
                'description' => 'Campus safety, security concerns',
                'iso_code' => 'SF001',
                'sla_days' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Other',
                'description' => 'General complaints not covered by other categories',
                'iso_code' => 'OT001',
                'sla_days' => 7,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            ComplaintCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }

        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
                'user_type' => 'admin',
                'department' => 'Administration',
                'phone' => '+1234567890',
            ]
        );

        // Create teacher user
        $teacher = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Dr. John Smith',
                'password' => Hash::make('password'),
                'user_type' => 'teacher',
                'department' => 'Computer Science',
                'phone' => '+1234567891',
            ]
        );

        // Create student user
        $student = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Alice Wilson',
                'password' => Hash::make('password'),
                'user_type' => 'student',
                'department' => 'Computer Science',
                'phone' => '+1234567892',
            ]
        );

        $this->command->info('Basic data seeded successfully!');
        $this->command->info('Login credentials:');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Teacher: <EMAIL> / password');
        $this->command->info('Student: <EMAIL> / password');
    }
}
