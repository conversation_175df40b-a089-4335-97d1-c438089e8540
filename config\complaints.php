<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Complaints System Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the complaints management
    | system including reference number generation, SLA settings, and
    | notification preferences.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Reference Number Configuration
    |--------------------------------------------------------------------------
    |
    | Configure how complaint reference numbers are generated.
    |
    */

    'reference' => [
        'prefix' => env('COMPLAINTS_REFERENCE_PREFIX', 'CMP'),
        'format' => '{prefix}-{year}{month}-{number}',
        'padding' => 4, // Number of digits for the sequential number
    ],

    /*
    |--------------------------------------------------------------------------
    | SLA Configuration
    |--------------------------------------------------------------------------
    |
    | Default SLA settings and priority multipliers.
    |
    */

    'sla' => [
        'default_days' => env('COMPLAINTS_DEFAULT_SLA_DAYS', 7),
        'priority_multipliers' => [
            'critical' => 0.25, // 25% of base time
            'high' => 0.5,      // 50% of base time
            'medium' => 1.0,    // 100% of base time (default)
            'low' => 1.5,       // 150% of base time
        ],
        'warning_threshold_hours' => 24, // Hours before SLA breach to send warning
    ],

    /*
    |--------------------------------------------------------------------------
    | Assignment Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for complaint assignment and workload management.
    |
    */

    'assignment' => [
        'auto_assignment' => env('COMPLAINTS_AUTO_ASSIGNMENT', true),
        'max_workload_per_user' => 20,
        'assignment_roles' => ['admin', 'teacher'],
        'escalation_levels' => [
            1 => 'Supervisor',
            2 => 'Manager',
            3 => 'Director',
            4 => 'Executive',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for file attachments.
    |
    */

    'attachments' => [
        'max_files' => 5,
        'max_file_size' => 10240, // KB (10MB)
        'allowed_types' => [
            'pdf', 'doc', 'docx', 'txt', 'rtf',
            'jpg', 'jpeg', 'png', 'gif', 'bmp',
            'xls', 'xlsx', 'csv',
            'zip', 'rar',
        ],
        'storage_disk' => env('FILESYSTEM_DISK', 'local'),
        'storage_path' => 'complaints/attachments',
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for various notification types.
    |
    */

    'notifications' => [
        'enabled' => env('NOTIFICATIONS_ENABLED', true),
        'channels' => [
            'email' => env('NOTIFICATIONS_EMAIL_ENABLED', true),
            'database' => env('NOTIFICATIONS_DATABASE_ENABLED', true),
        ],
        'events' => [
            'complaint_submitted' => true,
            'complaint_assigned' => true,
            'complaint_status_changed' => true,
            'complaint_escalated' => true,
            'sla_warning' => true,
            'sla_breached' => true,
            'comment_added' => true,
            'complaint_resolved' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for analytics and reporting.
    |
    */

    'analytics' => [
        'enabled' => env('ANALYTICS_ENABLED', true),
        'retention_days' => env('ANALYTICS_RETENTION_DAYS', 730), // 2 years
        'aggregation_schedule' => 'daily',
        'cost_calculation' => [
            'base_cost_per_hour' => 50,
            'priority_multipliers' => [
                'critical' => 2.0,
                'high' => 1.5,
                'medium' => 1.0,
                'low' => 0.8,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Status Configuration
    |--------------------------------------------------------------------------
    |
    | Available statuses and their transitions.
    |
    */

    'statuses' => [
        'new' => [
            'label' => 'New',
            'color' => 'gray',
            'transitions' => ['assigned', 'in_progress', 'closed'],
        ],
        'assigned' => [
            'label' => 'Assigned',
            'color' => 'blue',
            'transitions' => ['in_progress', 'on_hold', 'resolved', 'closed'],
        ],
        'in_progress' => [
            'label' => 'In Progress',
            'color' => 'indigo',
            'transitions' => ['on_hold', 'resolved', 'closed'],
        ],
        'on_hold' => [
            'label' => 'On Hold',
            'color' => 'yellow',
            'transitions' => ['in_progress', 'resolved', 'closed'],
        ],
        'resolved' => [
            'label' => 'Resolved',
            'color' => 'green',
            'transitions' => ['closed', 'reopened'],
        ],
        'closed' => [
            'label' => 'Closed',
            'color' => 'green',
            'transitions' => ['reopened'],
        ],
        'reopened' => [
            'label' => 'Reopened',
            'color' => 'red',
            'transitions' => ['assigned', 'in_progress', 'resolved', 'closed'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Priority Configuration
    |--------------------------------------------------------------------------
    |
    | Available priorities and their settings.
    |
    */

    'priorities' => [
        'low' => [
            'label' => 'Low',
            'color' => 'gray',
            'sla_multiplier' => 1.5,
        ],
        'medium' => [
            'label' => 'Medium',
            'color' => 'blue',
            'sla_multiplier' => 1.0,
        ],
        'high' => [
            'label' => 'High',
            'color' => 'yellow',
            'sla_multiplier' => 0.5,
        ],
        'critical' => [
            'label' => 'Critical',
            'color' => 'red',
            'sla_multiplier' => 0.25,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Report Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for report generation.
    |
    */

    'reports' => [
        'storage_path' => 'reports',
        'retention_days' => 90,
        'formats' => ['pdf', 'excel', 'csv'],
        'max_records_per_report' => 10000,
        'email_delivery' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | ISO 21001 Compliance
    |--------------------------------------------------------------------------
    |
    | Settings for ISO 21001 compliance features.
    |
    */

    'iso21001' => [
        'enabled' => true,
        'audit_trail_retention_years' => 3,
        'stakeholder_satisfaction_threshold' => 80, // Percentage
        'continuous_improvement_review_months' => 6,
        'compliance_reporting_schedule' => 'quarterly',
    ],

];
