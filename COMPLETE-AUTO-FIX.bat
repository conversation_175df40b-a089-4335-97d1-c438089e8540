@echo off
echo ========================================
echo COMPLETE AUTO FIX - No Manual Steps
echo ========================================

echo Running auto-fix...
php auto-fix.php

echo.
echo Running migrations...
php artisan migrate:fresh --force

echo.
echo Running seeders...
php artisan db:seed --force

echo.
echo Creating users...
php auto-create-users.php

echo.
echo Setting up scheduled tasks...
php artisan schedule:list

echo.
echo ========================================
echo SETUP COMPLETE - Week 1 Tasks Done!
echo ========================================
echo.
echo Access URLs:
echo • Admin Panel: http://localhost:8000/admin
echo • Teacher Portal: http://localhost:8000/teacher/dashboard
echo • Student Portal: http://localhost:8000/student/dashboard
echo.
echo Demo Credentials:
echo • Admin: <EMAIL> / 123456
echo • Teacher: <EMAIL> / 123456
echo • Student: <EMAIL> / 123456
echo.
echo Week 1 Features Completed:
echo ✅ Teacher Portal with complaint management
echo ✅ Enhanced notifications system
echo ✅ SLA monitoring and escalations
echo ✅ Analytics and reporting foundation
echo ✅ ISO 21001 compliance features
echo.
echo Starting server...
php artisan serve
