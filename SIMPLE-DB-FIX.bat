@echo off
echo ========================================
echo SIMPLE DATABASE CONNECTION FIX
echo ========================================

echo Fixing database connection issue...

echo Step 1: Delete old database...
if exist database\database.sqlite del database\database.sqlite

echo Step 2: Create fresh database...
if not exist database mkdir database
echo. > database\database.sqlite

echo Step 3: Fix .env database config...
(
echo APP_NAME="Complaints Management System"
echo APP_ENV=local
echo APP_KEY=base64:SGVsbG9Xb3JsZEhlbGxvV29ybGRIZWxsb1dvcmxkSGVsbG9Xb3JsZA==
echo APP_DEBUG=true
echo APP_URL=http://localhost
echo.
echo DB_CONNECTION=sqlite
echo.
echo SESSION_DRIVER=file
echo CACHE_DRIVER=file
echo QUEUE_CONNECTION=sync
echo.
echo MAIL_MAILER=log
) > .env

echo Step 4: Clear caches...
php artisan config:clear 2>nul
php artisan cache:clear 2>nul

echo Step 5: Test database connection...
php -r "
\$db = 'database/database.sqlite';
if (file_exists(\$db)) {
    try {
        \$pdo = new PDO('sqlite:' . \$db);
        \$pdo->exec('CREATE TABLE IF NOT EXISTS test (id INTEGER)');
        \$pdo->exec('DROP TABLE test');
        echo 'Database test: PASSED\n';
    } catch (Exception \$e) {
        echo 'Database test failed: ' . \$e->getMessage() . '\n';
    }
} else {
    echo 'Database file not found\n';
}
"

echo Step 6: Run migrations...
php artisan migrate:fresh --force

echo Step 7: Seed database...
php artisan db:seed --force

echo.
echo ========================================
echo DATABASE CONNECTION FIXED!
echo ========================================
echo.
echo Login: http://localhost:8000/login
echo Credentials: <EMAIL> / 123456
echo.
php artisan serve
