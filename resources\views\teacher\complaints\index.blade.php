<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Complaint Management') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('teacher.dashboard') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filter Tabs -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex flex-wrap gap-2 mb-4">
                        <a href="{{ route('teacher.complaints.index', ['filter' => 'assigned']) }}" 
                           class="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors
                           {{ request('filter') === 'assigned' || !request('filter') ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                            My Assigned ({{ $complaints->where('assigned_to', Auth::id())->count() }})
                        </a>
                        <a href="{{ route('teacher.complaints.index', ['filter' => 'unassigned']) }}" 
                           class="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors
                           {{ request('filter') === 'unassigned' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                            Unassigned ({{ $complaints->whereNull('assigned_to')->count() }})
                        </a>
                        <a href="{{ route('teacher.complaints.index', ['filter' => 'all']) }}" 
                           class="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors
                           {{ request('filter') === 'all' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                            All Complaints ({{ $complaints->count() }})
                        </a>
                    </div>

                    <!-- Advanced Filters -->
                    <form method="GET" action="{{ route('teacher.complaints.index') }}" class="flex flex-wrap gap-4">
                        <input type="hidden" name="filter" value="{{ request('filter', 'assigned') }}">
                        
                        <div class="flex-1 min-w-64">
                            <input type="text" 
                                   name="search" 
                                   value="{{ request('search') }}"
                                   placeholder="Search complaints..."
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <select name="status" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Statuses</option>
                                @foreach($statuses as $key => $label)
                                    <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <select name="priority" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Priorities</option>
                                @foreach($priorities as $key => $label)
                                    <option value="{{ $key }}" {{ request('priority') == $key ? 'selected' : '' }}>{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            Filter
                        </button>
                        @if(request()->hasAny(['search', 'status', 'priority']))
                            <a href="{{ route('teacher.complaints.index', ['filter' => request('filter', 'assigned')]) }}" 
                               class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Clear
                            </a>
                        @endif
                    </form>
                </div>
            </div>

            <!-- Bulk Actions -->
            @if($complaints->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <form id="bulk-action-form" action="{{ route('teacher.complaints.bulk-action') }}" method="POST">
                            @csrf
                            <div class="flex flex-wrap gap-4 items-end">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Bulk Actions</label>
                                    <select name="action" id="bulk-action" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Select Action</option>
                                        <option value="assign">Assign to User</option>
                                        <option value="status_change">Change Status</option>
                                        <option value="priority_change">Change Priority</option>
                                    </select>
                                </div>
                                
                                <div id="assign-user" class="hidden">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Assign To</label>
                                    <select name="assigned_to" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Select User</option>
                                        <option value="{{ Auth::id() }}">Myself</option>
                                        <!-- Add other teachers/admins here -->
                                    </select>
                                </div>
                                
                                <div id="status-change" class="hidden">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">New Status</label>
                                    <select name="status" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @foreach($statuses as $key => $label)
                                            <option value="{{ $key }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                
                                <div id="priority-change" class="hidden">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">New Priority</label>
                                    <select name="priority" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @foreach($priorities as $key => $label)
                                            <option value="{{ $key }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                
                                <button type="submit" 
                                        class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    Apply to Selected
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            @endif

            <!-- Complaints List -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                @if($complaints->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Reference
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Title
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Submitter
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Priority
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Assigned To
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Submitted
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($complaints as $complaint)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" name="complaint_ids[]" value="{{ $complaint->id }}" 
                                                   class="complaint-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $complaint->reference_number }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <div class="max-w-xs truncate">
                                                {{ $complaint->title }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $complaint->submitter->name ?? 'Anonymous' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                @if($complaint->status === 'new') bg-blue-100 text-blue-800
                                                @elseif($complaint->status === 'assigned') bg-yellow-100 text-yellow-800
                                                @elseif($complaint->status === 'in_progress') bg-purple-100 text-purple-800
                                                @elseif($complaint->status === 'resolved') bg-green-100 text-green-800
                                                @elseif($complaint->status === 'closed') bg-gray-100 text-gray-800
                                                @else bg-red-100 text-red-800
                                                @endif">
                                                {{ ucfirst(str_replace('_', ' ', $complaint->status)) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                @if($complaint->priority === 'low') bg-green-100 text-green-800
                                                @elseif($complaint->priority === 'medium') bg-yellow-100 text-yellow-800
                                                @elseif($complaint->priority === 'high') bg-orange-100 text-orange-800
                                                @else bg-red-100 text-red-800
                                                @endif">
                                                {{ ucfirst($complaint->priority) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $complaint->assignee->name ?? 'Unassigned' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div>{{ $complaint->created_at->format('M d, Y') }}</div>
                                            <div class="text-xs text-gray-400">{{ $complaint->created_at->format('H:i') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('teacher.complaints.show', $complaint) }}" 
                                                   class="text-blue-600 hover:text-blue-900">
                                                    View
                                                </a>
                                                @if(!$complaint->assigned_to)
                                                    <form action="{{ route('teacher.complaints.take-ownership', $complaint) }}" method="POST" class="inline">
                                                        @csrf
                                                        <button type="submit" class="text-green-600 hover:text-green-900">
                                                            Take
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        {{ $complaints->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">
                            @if(request()->hasAny(['search', 'status', 'priority']))
                                No complaints found matching your criteria
                            @else
                                No complaints available
                            @endif
                        </h3>
                        <p class="mt-1 text-sm text-gray-500">
                            @if(request()->hasAny(['search', 'status', 'priority']))
                                Try adjusting your search filters.
                            @else
                                Complaints will appear here when they are submitted.
                            @endif
                        </p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <script>
        // Bulk actions functionality
        document.addEventListener('DOMContentLoaded', function() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.complaint-checkbox');
            const bulkActionSelect = document.getElementById('bulk-action');
            const bulkForm = document.getElementById('bulk-action-form');

            // Select all functionality
            selectAll.addEventListener('change', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // Individual checkbox change
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const checkedCount = document.querySelectorAll('.complaint-checkbox:checked').length;
                    selectAll.checked = checkedCount === checkboxes.length;
                    selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
                });
            });

            // Show/hide bulk action fields
            bulkActionSelect.addEventListener('change', function() {
                const action = this.value;
                document.getElementById('assign-user').classList.toggle('hidden', action !== 'assign');
                document.getElementById('status-change').classList.toggle('hidden', action !== 'status_change');
                document.getElementById('priority-change').classList.toggle('hidden', action !== 'priority_change');
            });

            // Bulk form submission
            bulkForm.addEventListener('submit', function(e) {
                const checkedBoxes = document.querySelectorAll('.complaint-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one complaint.');
                    return;
                }

                if (!bulkActionSelect.value) {
                    e.preventDefault();
                    alert('Please select an action.');
                    return;
                }

                if (!confirm(`Are you sure you want to apply this action to ${checkedBoxes.length} complaint(s)?`)) {
                    e.preventDefault();
                }
            });
        });
    </script>
</x-app-layout>
