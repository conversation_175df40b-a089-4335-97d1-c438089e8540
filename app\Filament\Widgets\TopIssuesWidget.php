<?php

namespace App\Filament\Widgets;

use App\Models\ComplaintAnalytics;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class TopIssuesWidget extends BaseWidget
{
    protected static ?string $heading = 'Top Issues & Trends';

    protected static ?int $sort = 9;

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTopIssuesQuery())
            ->columns([
                Tables\Columns\TextColumn::make('category_name')
                    ->label('Category')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_complaints')
                    ->label('Total')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('this_month')
                    ->label('This Month')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('last_month')
                    ->label('Last Month')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('trend')
                    ->label('Trend')
                    ->formatStateUsing(function ($state) {
                        if ($state > 0) {
                            return '+' . number_format($state, 1) . '%';
                        } elseif ($state < 0) {
                            return number_format($state, 1) . '%';
                        }
                        return '0%';
                    })
                    ->color(fn ($state) => $state > 10 ? 'danger' : ($state > 0 ? 'warning' : 'success'))
                    ->icon(fn ($state) => $state > 0 ? 'heroicon-m-arrow-trending-up' : 
                                        ($state < 0 ? 'heroicon-m-arrow-trending-down' : 'heroicon-m-minus'))
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('avg_resolution_hours')
                    ->label('Avg Resolution')
                    ->formatStateUsing(fn ($state) => $state ? number_format($state, 1) . 'h' : 'N/A')
                    ->sortable()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('sla_breach_rate')
                    ->label('SLA Breach %')
                    ->formatStateUsing(fn ($state) => number_format($state, 1) . '%')
                    ->color(fn ($state) => $state > 20 ? 'danger' : ($state > 10 ? 'warning' : 'success'))
                    ->sortable()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('avg_satisfaction')
                    ->label('Satisfaction')
                    ->formatStateUsing(fn ($state) => $state ? number_format($state, 1) . '/5' : 'N/A')
                    ->color(fn ($state) => $state >= 4 ? 'success' : ($state >= 3 ? 'warning' : 'danger'))
                    ->sortable()
                    ->alignCenter(),
            ])
            ->defaultSort('total_complaints', 'desc')
            ->paginated(false);
    }

    private function getTopIssuesQuery()
    {
        $thisMonthStart = now()->startOfMonth();
        $lastMonthStart = now()->subMonth()->startOfMonth();
        $lastMonthEnd = now()->subMonth()->endOfMonth();

        return ComplaintAnalytics::query()
            ->join('complaint_categories', 'complaint_analytics.category_id', '=', 'complaint_categories.id')
            ->selectRaw("
                complaint_categories.name as category_name,
                COUNT(*) as total_complaints,
                SUM(CASE WHEN submitted_date >= '{$thisMonthStart}' THEN 1 ELSE 0 END) as this_month,
                SUM(CASE WHEN submitted_date >= '{$lastMonthStart}' AND submitted_date <= '{$lastMonthEnd}' THEN 1 ELSE 0 END) as last_month,
                AVG(resolution_time_hours) as avg_resolution_hours,
                AVG(CASE WHEN sla_breached = 1 THEN 100 ELSE 0 END) as sla_breach_rate,
                AVG(satisfaction_rating) as avg_satisfaction,
                CASE 
                    WHEN SUM(CASE WHEN submitted_date >= '{$lastMonthStart}' AND submitted_date <= '{$lastMonthEnd}' THEN 1 ELSE 0 END) > 0 
                    THEN ((SUM(CASE WHEN submitted_date >= '{$thisMonthStart}' THEN 1 ELSE 0 END) - SUM(CASE WHEN submitted_date >= '{$lastMonthStart}' AND submitted_date <= '{$lastMonthEnd}' THEN 1 ELSE 0 END)) / SUM(CASE WHEN submitted_date >= '{$lastMonthStart}' AND submitted_date <= '{$lastMonthEnd}' THEN 1 ELSE 0 END)) * 100
                    ELSE 0 
                END as trend
            ")
            ->where('complaint_analytics.submitted_date', '>=', now()->subMonths(6))
            ->groupBy('complaint_categories.id', 'complaint_categories.name')
            ->having('total_complaints', '>', 0)
            ->orderBy('total_complaints', 'desc')
            ->limit(10);
    }
}
