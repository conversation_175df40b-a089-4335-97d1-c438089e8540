<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>vel Complaint Management System - Practical Course</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 15px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.3em;
            margin-bottom: 25px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .course-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 25px;
            position: relative;
            z-index: 1;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }

        .stat-card h3 {
            font-size: 1.1em;
            margin-bottom: 5px;
            color: #fff;
        }

        .stat-card p {
            color: rgba(255,255,255,0.9);
            font-size: 0.9em;
        }

        .content {
            padding: 30px;
        }

        .overview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .overview h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.6em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
        }

        .project-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .feature-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #667eea;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .feature-card h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1em;
        }

        .feature-card p {
            color: #6c757d;
            font-size: 0.9em;
        }

        .module {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }

        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }

        .module-header h2 {
            font-size: 1.8em;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .module-header .duration {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 15px;
            display: inline-block;
            font-size: 0.85em;
            margin-top: 8px;
        }

        .module-content {
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 8px 8px;
            padding: 20px;
        }

        .objectives {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .objectives h3 {
            color: #0c5460;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .objectives ul {
            list-style-type: none;
            padding-left: 0;
        }

        .objectives li {
            padding: 5px 0;
            color: #0c5460;
            position: relative;
            padding-left: 20px;
            font-size: 0.9em;
        }

        .objectives li::before {
            content: "🎯";
            position: absolute;
            left: 0;
            top: 5px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            overflow-x: auto;
            position: relative;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: 5px;
            right: 10px;
            background: #4a5568;
            color: #e2e8f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            text-transform: uppercase;
        }

        .explanation {
            background: #f8f9fa;
            border-left: 3px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }

        .explanation h4 {
            color: #155724;
            margin-bottom: 8px;
            font-size: 1em;
        }

        .explanation p, .explanation ul {
            color: #155724;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .tip {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 12px;
            margin: 15px 0;
        }

        .tip h4 {
            color: #856404;
            margin-bottom: 5px;
            font-size: 0.95em;
        }

        .tip p {
            color: #856404;
            font-size: 0.85em;
        }

        .exercise {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .exercise h4 {
            color: #0056b3;
            margin-bottom: 10px;
            font-size: 1em;
        }

        .exercise-steps {
            list-style: none;
            padding-left: 0;
        }

        .exercise-steps li {
            padding: 6px 0;
            border-bottom: 1px solid #cce7ff;
            position: relative;
            padding-left: 25px;
            font-size: 0.9em;
        }

        .exercise-steps li:last-child {
            border-bottom: none;
        }

        .exercise-steps li::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 6px;
            background: #0056b3;
            color: white;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        .exercise-steps {
            counter-reset: step-counter;
        }

        .progress-tracker {
            position: fixed;
            top: 15px;
            right: 15px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 200px;
            max-height: 350px;
            overflow-y: auto;
        }

        .progress-tracker h4 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 1em;
        }

        .progress-bar {
            background: #e9ecef;
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .module-list {
            list-style: none;
            padding: 0;
        }

        .module-list li {
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            font-size: 0.8em;
        }

        .module-list li:last-child {
            border-bottom: none;
        }

        .module-checkbox {
            margin-right: 8px;
            transform: scale(0.9);
        }

        @media print {
            body {
                background: white;
                font-size: 11px;
            }

            .container {
                box-shadow: none;
            }

            .progress-tracker {
                display: none;
            }

            .module {
                page-break-before: always;
                margin-bottom: 20px;
            }

            .module:first-of-type {
                page-break-before: auto;
            }

            .code-block {
                font-size: 9px;
                padding: 10px;
            }

            .header {
                page-break-after: always;
            }
        }

        @media (max-width: 768px) {
            .progress-tracker {
                position: relative;
                top: auto;
                right: auto;
                margin: 15px;
                width: auto;
            }

            .header {
                padding: 25px 15px;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .content {
                padding: 15px;
            }

            .course-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Laravel Complaint Management System</h1>
            <p class="subtitle">Build a Complete Complaint & Feedback System in 8-12 Hours</p>

            <div class="course-stats">
                <div class="stat-card">
                    <h3>⏱️ Duration</h3>
                    <p>8-12 Hours Total</p>
                </div>
                <div class="stat-card">
                    <h3>🎯 Focus</h3>
                    <p>Single Project Build</p>
                </div>
                <div class="stat-card">
                    <h3>📚 Level</h3>
                    <p>Beginner to Intermediate</p>
                </div>
                <div class="stat-card">
                    <h3>🏆 Outcome</h3>
                    <p>Working Application</p>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="overview">
                <h2>🚀 What You'll Build</h2>
                <p style="margin-bottom: 20px; color: #6c757d; font-size: 1.05em;">
                    A complete, production-ready complaint and feedback management system with public submission forms,
                    user tracking, admin management panel, and email notifications. Perfect for businesses, schools,
                    or any organization that needs to manage customer feedback efficiently.
                </p>

                <div class="project-preview">
                    <div class="feature-card">
                        <h4>📝 Public Complaint Form</h4>
                        <p>Clean, user-friendly form for submitting complaints with file attachments</p>
                    </div>

                    <div class="feature-card">
                        <h4>👤 User Tracking</h4>
                        <p>Allow users to track their complaint status and receive updates</p>
                    </div>

                    <div class="feature-card">
                        <h4>⚡ Admin Panel</h4>
                        <p>Powerful Filament-based interface for managing complaints and responses</p>
                    </div>

                    <div class="feature-card">
                        <h4>📧 Email Notifications</h4>
                        <p>Automatic email updates when complaint status changes</p>
                    </div>

                    <div class="feature-card">
                        <h4>📊 Basic Analytics</h4>
                        <p>Dashboard with complaint statistics and trends</p>
                    </div>

                    <div class="feature-card">
                        <h4>🔒 Secure & Validated</h4>
                        <p>Proper validation, security measures, and file upload protection</p>
                    </div>
                </div>
            </div>

            <!-- Module 1: Essential Laravel Setup -->
            <div class="module" id="module1">
                <div class="module-header">
                    <h2>Module 1: Essential Laravel Setup</h2>
                    <p>Get your complaint system foundation ready in 2-3 hours</p>
                    <span class="duration">⏱️ Duration: 2-3 Hours</span>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Set up Laravel project specifically for complaint management</li>
                            <li>Create essential models: User, Complaint, Category, Response</li>
                            <li>Build basic controllers for complaint handling</li>
                            <li>Design initial views and layout structure</li>
                            <li>Configure routes for public and admin access</li>
                        </ul>
                    </div>

                    <div class="section">
                        <h3>1.1 Project Setup & Structure</h3>

                        <h4>Quick Laravel Installation</h4>
                        <div class="code-block" data-lang="bash">
# Create new Laravel project
composer create-project laravel/laravel complaint-system

# Navigate to project
cd complaint-system

# Install required packages
composer require filament/filament:"^3.0" spatie/laravel-permission

# Set up environment
cp .env.example .env
php artisan key:generate

# Create SQLite database (for quick setup)
touch database/database.sqlite
                        </div>

                        <div class="explanation">
                            <h4>🔍 Why This Setup?</h4>
                            <ul>
                                <li><strong>Filament</strong> - Provides instant admin panel for complaint management</li>
                                <li><strong>Spatie Permission</strong> - Simple role management (admin vs user)</li>
                                <li><strong>SQLite</strong> - Quick database setup without server configuration</li>
                            </ul>
                        </div>

                        <h4>Environment Configuration</h4>
                        <div class="code-block" data-lang="env">
APP_NAME="Complaint Management System"
APP_ENV=local
APP_KEY=base64:your-generated-key
APP_DEBUG=true
APP_URL=http://localhost

DB_CONNECTION=sqlite

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
                        </div>

                        <h4>Essential Models Creation</h4>
                        <div class="code-block" data-lang="bash">
# Create models with migrations
php artisan make:model Category -m
php artisan make:model Complaint -m
php artisan make:model ComplaintResponse -m

# Create controllers
php artisan make:controller ComplaintController
php artisan make:controller Admin/DashboardController
                        </div>

                        <h4>Category Model Setup</h4>
                        <div class="code-block" data-lang="php">
<?php
// app/Models/Category.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    protected $fillable = [
        'name', 'description', 'color', 'is_active', 'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
                        </div>

                        <h4>Complaint Model Setup</h4>
                        <div class="code-block" data-lang="php">
<?php
// app/Models/Complaint.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Complaint extends Model
{
    protected $fillable = [
        'reference_number', 'title', 'description', 'category_id',
        'priority', 'status', 'submitted_by', 'assigned_to',
        'contact_email', 'contact_phone', 'attachments'
    ];

    protected $casts = [
        'attachments' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($complaint) {
            if (empty($complaint->reference_number)) {
                $complaint->reference_number = 'CMP-' . strtoupper(Str::random(8));
            }
        });
    }

    // Relationships
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function submitter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_by');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function responses(): HasMany
    {
        return $this->hasMany(ComplaintResponse::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    // Accessors
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'new' => 'yellow',
            'in_progress' => 'blue',
            'resolved' => 'green',
            'closed' => 'gray',
            default => 'gray'
        };
    }

    public function getPriorityColorAttribute()
    {
        return match($this->priority) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'critical' => 'red',
            default => 'gray'
        };
    }
}
                        </div>

                        <div class="tip">
                            <h4>💡 Quick Tip</h4>
                            <p>The reference number is automatically generated when creating complaints. This gives users a unique identifier to track their submissions.</p>
                        </div>
                    </div>

                    <div class="exercise">
                        <h4>🏋️ Practical Exercise 1.1: Set Up Your Project</h4>
                        <ol class="exercise-steps">
                            <li>Create a new Laravel project called "complaint-system"</li>
                            <li>Install Filament and Spatie Permission packages</li>
                            <li>Configure your .env file for SQLite database</li>
                            <li>Create the Category and Complaint models with the code above</li>
                            <li>Test that your models are working by running php artisan tinker</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- Module 2: Database Design for Complaints -->
            <div class="module" id="module2">
                <div class="module-header">
                    <h2>Module 2: Database Design for Complaints</h2>
                    <p>Create the perfect database structure for complaint management</p>
                    <span class="duration">⏱️ Duration: 1-2 Hours</span>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Design efficient database schema for complaint system</li>
                            <li>Create essential migrations with proper relationships</li>
                            <li>Set up seeders for testing data</li>
                            <li>Understand complaint workflow and status management</li>
                        </ul>
                    </div>

                    <div class="section">
                        <h3>2.1 Essential Migrations</h3>

                        <h4>Categories Migration</h4>
                        <div class="code-block" data-lang="php">
<?php
// database/migrations/xxxx_create_categories_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('color')->default('#3B82F6');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
                        </div>

                        <h4>Enhanced Users Migration</h4>
                        <div class="code-block" data-lang="bash">
# Add fields to existing users table
php artisan make:migration add_fields_to_users_table --table=users
                        </div>

                        <div class="code-block" data-lang="php">
<?php
// database/migrations/xxxx_add_fields_to_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('user_type')->default('user'); // user, admin
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->timestamp('last_login_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['user_type', 'phone', 'address', 'last_login_at']);
        });
    }
};
                        </div>

                        <h4>Complaints Migration</h4>
                        <div class="code-block" data-lang="php">
<?php
// database/migrations/xxxx_create_complaints_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('complaints', function (Blueprint $table) {
            $table->id();
            $table->string('reference_number')->unique();
            $table->string('title');
            $table->text('description');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('status', ['new', 'in_progress', 'resolved', 'closed'])->default('new');
            $table->foreignId('submitted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->string('contact_email');
            $table->string('contact_phone')->nullable();
            $table->json('attachments')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['priority', 'status']);
            $table->index(['category_id', 'status']);
            $table->index('reference_number');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('complaints');
    }
};
                        </div>

                        <h4>Complaint Responses Migration</h4>
                        <div class="code-block" data-lang="php">
<?php
// database/migrations/xxxx_create_complaint_responses_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('complaint_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('complaint_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('response');
            $table->boolean('is_internal')->default(false);
            $table->timestamps();

            $table->index(['complaint_id', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('complaint_responses');
    }
};
                        </div>

                        <div class="explanation">
                            <h4>🔍 Database Design Explained</h4>
                            <ul>
                                <li><strong>Categories</strong> - Organize complaints by type (billing, technical, etc.)</li>
                                <li><strong>Complaints</strong> - Main entity with status tracking and assignments</li>
                                <li><strong>Responses</strong> - Communication thread between users and staff</li>
                                <li><strong>Indexes</strong> - Optimize queries for common filtering operations</li>
                                <li><strong>Foreign Keys</strong> - Maintain data integrity and relationships</li>
                            </ul>
                        </div>

                        <h3>2.2 Essential Seeders</h3>

                        <h4>Category Seeder</h4>
                        <div class="code-block" data-lang="bash">
php artisan make:seeder CategorySeeder
                        </div>

                        <div class="code-block" data-lang="php">
<?php
// database/seeders/CategorySeeder.php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Technical Issues',
                'description' => 'Problems with website, app, or technical services',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Billing & Payments',
                'description' => 'Issues related to billing, payments, and refunds',
                'color' => '#10B981',
                'sort_order' => 2,
            ],
            [
                'name' => 'Customer Service',
                'description' => 'General customer service and support issues',
                'color' => '#F59E0B',
                'sort_order' => 3,
            ],
            [
                'name' => 'Product Quality',
                'description' => 'Issues with product quality or defects',
                'color' => '#EF4444',
                'sort_order' => 4,
            ],
            [
                'name' => 'Other',
                'description' => 'General complaints and other issues',
                'color' => '#6B7280',
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
                        </div>

                        <h4>User Seeder</h4>
                        <div class="code-block" data-lang="bash">
php artisan make:seeder UserSeeder
                        </div>

                        <div class="code-block" data-lang="php">
<?php
// database/seeders/UserSeeder.php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'user_type' => 'admin',
        ]);

        // Create regular user for testing
        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'user_type' => 'user',
        ]);
    }
}
                        </div>

                        <h4>Update DatabaseSeeder</h4>
                        <div class="code-block" data-lang="php">
<?php
// database/seeders/DatabaseSeeder.php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            CategorySeeder::class,
            UserSeeder::class,
        ]);
    }
}
                        </div>

                        <h4>Run Migrations and Seeders</h4>
                        <div class="code-block" data-lang="bash">
# Run all migrations and seeders
php artisan migrate:fresh --seed

# Or run them separately
php artisan migrate
php artisan db:seed
                        </div>

                        <div class="tip">
                            <h4>💡 Quick Tip</h4>
                            <p>Use <code>migrate:fresh --seed</code> during development to reset your database and populate it with fresh test data quickly.</p>
                        </div>
                    </div>

                    <div class="exercise">
                        <h4>🏋️ Practical Exercise 2.1: Set Up Your Database</h4>
                        <ol class="exercise-steps">
                            <li>Create all the migrations shown above</li>
                            <li>Set up the Category and User seeders</li>
                            <li>Run migrations and seeders to populate your database</li>
                            <li>Use php artisan tinker to test relationships between models</li>
                            <li>Verify that foreign key constraints are working properly</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- Quick Implementation Guide -->
            <div class="module" id="implementation">
                <div class="module-header">
                    <h2>🚀 Quick Implementation Guide</h2>
                    <p>Complete the remaining modules to build your working system</p>
                    <span class="duration">⏱️ Duration: 4-6 Hours</span>
                </div>

                <div class="module-content">
                    <div class="section">
                        <h3>Remaining Modules Overview</h3>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; margin: 20px 0;">
                            <div style="background: #e8f4fd; padding: 15px; border-radius: 6px; border-left: 3px solid #3B82F6;">
                                <h4>Module 3: Filament Admin Panel (2-3 hours)</h4>
                                <ul style="font-size: 0.9em; margin: 10px 0; padding-left: 20px;">
                                    <li>Install and configure Filament</li>
                                    <li>Create Complaint resource with forms and tables</li>
                                    <li>Build dashboard widgets for statistics</li>
                                    <li>Set up user management interface</li>
                                </ul>
                            </div>

                            <div style="background: #f0f8f0; padding: 15px; border-radius: 6px; border-left: 3px solid #10B981;">
                                <h4>Module 4: Authentication & Access (1-2 hours)</h4>
                                <ul style="font-size: 0.9em; margin: 10px 0; padding-left: 20px;">
                                    <li>Set up Laravel Breeze authentication</li>
                                    <li>Create simple role-based access control</li>
                                    <li>Protect admin routes with middleware</li>
                                    <li>Allow users to track their complaints</li>
                                </ul>
                            </div>

                            <div style="background: #fff3cd; padding: 15px; border-radius: 6px; border-left: 3px solid #F59E0B;">
                                <h4>Module 5: Frontend & Forms (2-3 hours)</h4>
                                <ul style="font-size: 0.9em; margin: 10px 0; padding-left: 20px;">
                                    <li>Create public complaint submission form</li>
                                    <li>Build complaint tracking page</li>
                                    <li>Style with Tailwind CSS</li>
                                    <li>Add file upload functionality</li>
                                </ul>
                            </div>

                            <div style="background: #f8d7da; padding: 15px; border-radius: 6px; border-left: 3px solid #EF4444;">
                                <h4>Module 6: Security & Validation (1-2 hours)</h4>
                                <ul style="font-size: 0.9em; margin: 10px 0; padding-left: 20px;">
                                    <li>Add form validation and error handling</li>
                                    <li>Secure file uploads with validation</li>
                                    <li>Implement CSRF protection</li>
                                    <li>Set up email notifications</li>
                                </ul>
                            </div>
                        </div>

                        <h3>Essential Commands Reference</h3>
                        <div class="code-block" data-lang="bash">
# Install Filament
php artisan filament:install --panels

# Install Laravel Breeze
composer require laravel/breeze --dev
php artisan breeze:install blade

# Create Filament resources
php artisan make:filament-resource Complaint
php artisan make:filament-resource Category
php artisan make:filament-resource User

# Create controllers
php artisan make:controller ComplaintController
php artisan make:controller TrackingController

# Create form requests
php artisan make:request StoreComplaintRequest

# Create notifications
php artisan make:notification ComplaintStatusChanged

# Create middleware
php artisan make:middleware EnsureUserIsAdmin
                        </div>

                        <h3>Key Features to Implement</h3>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div>
                                    <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1em;">✅ Must Have</h4>
                                    <ul style="font-size: 0.85em; color: #495057;">
                                        <li>Public complaint form</li>
                                        <li>Admin complaint management</li>
                                        <li>Status tracking</li>
                                        <li>Email notifications</li>
                                        <li>File attachments</li>
                                        <li>Basic dashboard</li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1em;">🎯 Nice to Have</h4>
                                    <ul style="font-size: 0.85em; color: #495057;">
                                        <li>Advanced filtering</li>
                                        <li>Bulk actions</li>
                                        <li>Response templates</li>
                                        <li>Analytics charts</li>
                                        <li>Export functionality</li>
                                        <li>Mobile responsiveness</li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1em;">🚀 Future Enhancements</h4>
                                    <ul style="font-size: 0.85em; color: #495057;">
                                        <li>Real-time notifications</li>
                                        <li>API endpoints</li>
                                        <li>Advanced reporting</li>
                                        <li>Multi-language support</li>
                                        <li>Integration with CRM</li>
                                        <li>Automated workflows</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="tip">
                            <h4>💡 Development Tips</h4>
                            <p><strong>Focus on core functionality first:</strong> Get the basic complaint submission and management working before adding advanced features. Test each module thoroughly before moving to the next one.</p>
                        </div>
                    </div>

                    <div class="exercise">
                        <h4>🏗️ Final Project Checklist</h4>
                        <ol class="exercise-steps">
                            <li>Complete database setup and verify all relationships work</li>
                            <li>Install and configure Filament admin panel</li>
                            <li>Create complaint submission form with validation</li>
                            <li>Build admin interface for managing complaints</li>
                            <li>Add authentication and role-based access control</li>
                            <li>Implement email notifications for status changes</li>
                            <li>Test the complete workflow from submission to resolution</li>
                            <li>Add basic styling and ensure mobile responsiveness</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- Course Completion -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; margin: 30px 0; border-radius: 10px; text-align: center;">
                <h2 style="color: white; margin-bottom: 15px; font-size: 2em;">🎉 Ready to Build!</h2>
                <p style="font-size: 1.1em; margin-bottom: 20px; opacity: 0.9;">
                    You now have everything you need to build a complete complaint management system!
                </p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <h3 style="color: white; margin-bottom: 8px; font-size: 1.1em;">🏆 What You'll Have</h3>
                        <ul style="text-align: left; color: rgba(255,255,255,0.9); font-size: 0.9em;">
                            <li>Working complaint system</li>
                            <li>Admin management panel</li>
                            <li>User authentication</li>
                            <li>Email notifications</li>
                            <li>File upload support</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <h3 style="color: white; margin-bottom: 8px; font-size: 1.1em;">🚀 Next Steps</h3>
                        <ul style="text-align: left; color: rgba(255,255,255,0.9); font-size: 0.9em;">
                            <li>Deploy to production</li>
                            <li>Add advanced features</li>
                            <li>Customize for your needs</li>
                            <li>Scale and optimize</li>
                            <li>Build more Laravel apps</li>
                        </ul>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <button onclick="window.print()" style="background: #28a745; color: white; padding: 12px 25px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 8px; box-shadow: 0 3px 5px rgba(0,0,0,0.1);">
                        📄 Save Course as PDF
                    </button>
                    <button onclick="generateCertificate()" style="background: #ffc107; color: #212529; padding: 12px 25px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 8px; box-shadow: 0 3px 5px rgba(0,0,0,0.1);">
                        🏅 Generate Certificate
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="progress-tracker">
            <h4>📊 Course Progress</h4>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <p id="progress-text">0% Complete</p>

            <ul class="module-list">
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="1">
                    <span>Module 1: Laravel Setup</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="2">
                    <span>Module 2: Database Design</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="3">
                    <span>Module 3: Filament Admin</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="4">
                    <span>Module 4: Authentication</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="5">
                    <span>Module 5: Frontend</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="6">
                    <span>Module 6: Security</span>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // Progress tracking functionality
        function updateProgress() {
            const checkboxes = document.querySelectorAll('.module-checkbox');
            const checked = document.querySelectorAll('.module-checkbox:checked').length;
            const total = checkboxes.length;
            const percentage = Math.round((checked / total) * 100);

            document.getElementById('progress-fill').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = `${percentage}% Complete (${checked}/${total})`;

            // Save progress to localStorage
            const progress = Array.from(checkboxes).map(cb => cb.checked);
            localStorage.setItem('complaint-course-progress', JSON.stringify(progress));
        }

        // Load saved progress
        function loadProgress() {
            const saved = localStorage.getItem('complaint-course-progress');
            if (saved) {
                const progress = JSON.parse(saved);
                const checkboxes = document.querySelectorAll('.module-checkbox');
                progress.forEach((checked, index) => {
                    if (checkboxes[index]) {
                        checkboxes[index].checked = checked;
                    }
                });
                updateProgress();
            }
        }

        // Certificate generation
        function generateCertificate() {
            const name = prompt('Enter your name for the certificate:');
            if (name) {
                const completedModules = document.querySelectorAll('.module-checkbox:checked').length;
                const totalModules = document.querySelectorAll('.module-checkbox').length;

                if (completedModules === totalModules) {
                    alert(`🎉 Congratulations ${name}! You've completed the Laravel Complaint Management System course. Your certificate is ready!`);
                } else {
                    alert(`Complete all modules to receive your certificate. Progress: ${completedModules}/${totalModules}`);
                }
            }
        }

        // Copy code functionality
        document.querySelectorAll('.code-block').forEach(block => {
            const button = document.createElement('button');
            button.textContent = 'Copy';
            button.style.cssText = 'position: absolute; top: 8px; right: 12px; background: #4a5568; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 11px; z-index: 10;';
            block.style.position = 'relative';
            block.appendChild(button);

            button.addEventListener('click', () => {
                const text = block.textContent.replace('Copy', '').trim();
                navigator.clipboard.writeText(text).then(() => {
                    button.textContent = 'Copied!';
                    button.style.background = '#28a745';
                    setTimeout(() => {
                        button.textContent = 'Copy';
                        button.style.background = '#4a5568';
                    }, 2000);
                });
            });
        });

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            loadProgress();

            document.querySelectorAll('.module-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateProgress);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>