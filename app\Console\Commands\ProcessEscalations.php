<?php

namespace App\Console\Commands;

use App\Jobs\ProcessComplaintEscalations;
use Illuminate\Console\Command;

class ProcessEscalations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'complaints:process-escalations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process automatic complaint escalations based on SLA rules';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing complaint escalations...');
        
        ProcessComplaintEscalations::dispatch();
        
        $this->info('Escalation job dispatched successfully.');
        
        return 0;
    }
}
