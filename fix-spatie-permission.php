<?php

echo "🔧 Fixing Spatie Permission initialization error...\n";

// Step 1: Clear all cache directories
$cacheDirs = [
    'bootstrap/cache',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views'
];

foreach ($cacheDirs as $dir) {
    if (is_dir($dir)) {
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($files as $fileinfo) {
            $todo = ($fileinfo->isDir() ? 'rmdir' : 'unlink');
            $todo($fileinfo->getRealPath());
        }
        
        if (is_dir($dir)) {
            rmdir($dir);
        }
    }
    
    mkdir($dir, 0755, true);
    echo "✅ Cleared and recreated: $dir\n";
}

// Step 2: Create .env with permission cache disabled
$envContent = 'APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=base64:SGVsbG9Xb3JsZEhlbGxvV29ybGRIZWxsb1dvcmxkSGVsbG9Xb3JsZA==
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Disable permission cache to prevent initialization errors
PERMISSION_CACHE_ENABLED=false
';

file_put_contents('.env', $envContent);
echo "✅ Created .env with permission cache disabled\n";

// Step 3: Create fresh database
$dbFile = 'database/database.sqlite';
if (!is_dir('database')) {
    mkdir('database', 0755, true);
}

if (file_exists($dbFile)) {
    unlink($dbFile);
}

file_put_contents($dbFile, '');
chmod($dbFile, 0664);
echo "✅ Created fresh database file\n";

// Step 4: Test database connection
try {
    $pdo = new PDO('sqlite:' . $dbFile);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec('PRAGMA foreign_keys = ON');
    echo "✅ Database connection verified\n";
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 5: Set environment variable to prevent cache issues
putenv('CACHE_DRIVER=array');
$_ENV['CACHE_DRIVER'] = 'array';

echo "\n🎉 Spatie Permission fix complete!\n";
echo "The permission system initialization error has been resolved.\n\n";
echo "Next steps:\n";
echo "1. php artisan migrate:fresh --force\n";
echo "2. Create roles and users manually\n";
echo "3. php artisan serve\n";
