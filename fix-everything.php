<?php

/**
 * Complete System Fix Script
 * This script will fix all database and authentication issues
 */

echo "🔧 Starting Complete System Fix...\n\n";

// Step 1: Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "❌ Error: Please run this script from the Laravel project root directory.\n";
    exit(1);
}

// Step 2: Create .env file if it doesn't exist
echo "📝 Step 1: Setting up environment file...\n";
if (!file_exists('.env')) {
    if (file_exists('.env.example')) {
        copy('.env.example', '.env');
        echo "✅ Created .env file from .env.example\n";
    } else {
        echo "❌ Error: .env.example file not found\n";
        exit(1);
    }
} else {
    echo "✅ .env file already exists\n";
}

// Step 3: Update .env to use SQLite
echo "\n📝 Step 2: Configuring database for SQLite...\n";
$envContent = file_get_contents('.env');

// Replace database configuration
$envContent = preg_replace('/^DB_CONNECTION=.*$/m', 'DB_CONNECTION=sqlite', $envContent);
$envContent = preg_replace('/^DB_HOST=.*$/m', '# DB_HOST=127.0.0.1', $envContent);
$envContent = preg_replace('/^DB_PORT=.*$/m', '# DB_PORT=3306', $envContent);
$envContent = preg_replace('/^DB_DATABASE=.*$/m', '# DB_DATABASE=complaints_management', $envContent);
$envContent = preg_replace('/^DB_USERNAME=.*$/m', '# DB_USERNAME=root', $envContent);
$envContent = preg_replace('/^DB_PASSWORD=.*$/m', '# DB_PASSWORD=', $envContent);

file_put_contents('.env', $envContent);
echo "✅ Updated .env for SQLite database\n";

// Step 4: Create SQLite database file
echo "\n📝 Step 3: Creating SQLite database...\n";
$dbPath = 'database/database.sqlite';
if (!file_exists($dbPath)) {
    if (!is_dir('database')) {
        mkdir('database', 0755, true);
    }
    touch($dbPath);
    echo "✅ Created SQLite database file\n";
} else {
    echo "✅ SQLite database file already exists\n";
}

// Step 5: Run Artisan commands
echo "\n📝 Step 4: Running Laravel setup commands...\n";

$commands = [
    'php artisan key:generate --force',
    'php artisan config:clear',
    'php artisan cache:clear',
    'php artisan route:clear',
    'php artisan view:clear',
    'php artisan migrate:fresh --force',
    'php artisan db:seed --force',
    'php artisan storage:link',
];

foreach ($commands as $command) {
    echo "Running: $command\n";
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ Success\n";
    } else {
        echo "⚠️  Command output:\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
    }
    echo "\n";
}

// Step 6: Verify users were created
echo "📝 Step 5: Verifying demo users...\n";
$output = [];
exec('php artisan tinker --execute="echo App\Models\User::count();"', $output);
$userCount = intval(trim(implode('', $output)));

if ($userCount >= 3) {
    echo "✅ Demo users created successfully ($userCount users found)\n";
} else {
    echo "⚠️  Creating demo users manually...\n";
    
    // Create users manually using artisan tinker
    $createUsersScript = "
        use App\Models\User;
        use Spatie\Permission\Models\Role;
        use Illuminate\Support\Facades\Hash;
        
        // Create roles
        Role::firstOrCreate(['name' => 'admin']);
        Role::firstOrCreate(['name' => 'teacher']);
        Role::firstOrCreate(['name' => 'student']);
        
        // Create admin user
        \$admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'IT',
                'user_type' => 'admin',
            ]
        );
        \$admin->assignRole('admin');
        
        // Create teacher user
        \$teacher = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Teacher',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'Academic',
                'user_type' => 'teacher',
            ]
        );
        \$teacher->assignRole('teacher');
        
        // Create student user
        \$student = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Jane Student',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'Computer Science',
                'user_type' => 'student',
            ]
        );
        \$student->assignRole('student');
        
        echo 'Users created: ' . User::count();
    ";
    
    // Write script to temporary file
    file_put_contents('temp_create_users.php', "<?php\n" . $createUsersScript);
    
    // Execute the script
    exec('php artisan tinker temp_create_users.php 2>&1', $output);
    
    // Clean up
    if (file_exists('temp_create_users.php')) {
        unlink('temp_create_users.php');
    }
    
    echo "✅ Demo users created manually\n";
}

// Step 7: Final verification
echo "\n📝 Step 6: Final verification...\n";
$output = [];
exec('php artisan tinker --execute="App\Models\User::all([\'name\', \'email\'])->each(function(\$user) { echo \$user->name . \' - \' . \$user->email . PHP_EOL; });"', $output);

echo "✅ Users in database:\n";
foreach ($output as $line) {
    if (trim($line)) {
        echo "   $line\n";
    }
}

// Step 8: Clear cache one more time
echo "\n📝 Step 7: Final cleanup...\n";
exec('php artisan config:clear');
exec('php artisan cache:clear');
echo "✅ Cache cleared\n";

echo "\n🎉 SETUP COMPLETE!\n";
echo "==========================================\n";
echo "Your Complaints Management System is ready!\n\n";
echo "🌐 Access URLs:\n";
echo "   • Admin Panel: http://localhost:8000/admin\n";
echo "   • Student Portal: http://localhost:8000/student/dashboard\n\n";
echo "🔑 Demo Login Credentials:\n";
echo "   • Admin: <EMAIL> / password\n";
echo "   • Teacher: <EMAIL> / password\n";
echo "   • Student: <EMAIL> / password\n\n";
echo "🚀 To start the development server:\n";
echo "   php artisan serve\n\n";
echo "📚 If you encounter issues, check TROUBLESHOOTING.md\n";
echo "==========================================\n";
