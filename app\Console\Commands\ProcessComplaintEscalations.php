<?php

namespace App\Console\Commands;

use App\Models\Complaint;
use App\Models\ComplaintEscalation;
use App\Notifications\ComplaintEscalated;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessComplaintEscalations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'complaints:process-escalations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process complaint escalations based on SLA rules';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing complaint escalations...');

        // Get complaints that need escalation
        $complaintsToEscalate = Complaint::where('status', '!=', 'resolved')
            ->where('status', '!=', 'closed')
            ->where('due_date', '<', now())
            ->whereDoesntHave('escalations', function ($query) {
                $query->where('escalated_at', '>', now()->subHours(24));
            })
            ->with(['category', 'submitter', 'assignedTo'])
            ->get();

        $escalatedCount = 0;

        foreach ($complaintsToEscalate as $complaint) {
            try {
                // Create escalation record
                $escalation = ComplaintEscalation::create([
                    'complaint_id' => $complaint->id,
                    'escalated_from' => $complaint->assigned_to,
                    'escalated_to' => $this->getEscalationTarget($complaint),
                    'escalation_level' => $this->getNextEscalationLevel($complaint),
                    'reason' => 'SLA breach - complaint overdue',
                    'escalated_by' => null, // System escalation
                    'escalated_at' => now(),
                ]);

                // Update complaint priority if needed
                if ($complaint->priority !== 'critical') {
                    $newPriority = $this->escalatePriority($complaint->priority);
                    $complaint->update(['priority' => $newPriority]);
                }

                // Send notifications
                $this->sendEscalationNotifications($complaint, $escalation);

                $escalatedCount++;
                $this->line("Escalated complaint: {$complaint->reference_number}");

            } catch (\Exception $e) {
                $this->error("Failed to escalate complaint {$complaint->reference_number}: {$e->getMessage()}");
                Log::error("Complaint escalation failed", [
                    'complaint_id' => $complaint->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info("Processed {$escalatedCount} complaint escalations.");

        return 0;
    }

    /**
     * Get the escalation target for a complaint
     */
    private function getEscalationTarget(Complaint $complaint): ?int
    {
        // Simple escalation logic - escalate to admin
        // In a real system, this would be more sophisticated
        $admin = \App\Models\User::where('user_type', 'admin')->first();
        return $admin?->id;
    }

    /**
     * Get the next escalation level
     */
    private function getNextEscalationLevel(Complaint $complaint): int
    {
        $lastEscalation = $complaint->escalations()->latest()->first();
        return $lastEscalation ? $lastEscalation->escalation_level + 1 : 1;
    }

    /**
     * Escalate complaint priority
     */
    private function escalatePriority(string $currentPriority): string
    {
        return match($currentPriority) {
            'low' => 'medium',
            'medium' => 'high',
            'high' => 'critical',
            default => 'critical'
        };
    }

    /**
     * Send escalation notifications
     */
    private function sendEscalationNotifications(Complaint $complaint, ComplaintEscalation $escalation): void
    {
        // Notify the person it's escalated to
        if ($escalation->escalatedTo) {
            $escalation->escalatedTo->notify(new ComplaintEscalated($complaint, $escalation));
        }

        // Notify the original submitter
        $complaint->submitter->notify(new ComplaintEscalated($complaint, $escalation));

        // Notify the original assignee if different
        if ($complaint->assignedTo && $complaint->assignedTo->id !== $escalation->escalated_to) {
            $complaint->assignedTo->notify(new ComplaintEscalated($complaint, $escalation));
        }
    }
}
