<x-student-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Complaint Details') }}
            </h2>
            <a href="{{ route('student.dashboard') }}" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded">
                Back to Dashboard
            </a>
        </div>
    </x-slot>

    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
        <div class="p-6 bg-white border-b border-gray-200">
            <div class="flex justify-between items-start">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{{ $complaint->title }}</h3>
                    <p class="text-sm text-gray-500 mb-2">Reference: {{ $complaint->reference_number }}</p>
                    <p class="text-sm text-gray-500 mb-2">Category: {{ $complaint->category->name }}</p>
                    <p class="text-sm text-gray-500 mb-2">Submitted: {{ $complaint->created_at->format('M d, Y H:i') }}</p>
                </div>
                <div>
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                        @if($complaint->status == 'new') bg-gray-100 text-gray-800
                        @elseif($complaint->status == 'assigned' || $complaint->status == 'in_progress') bg-blue-100 text-blue-800
                        @elseif($complaint->status == 'on_hold') bg-red-100 text-red-800
                        @elseif($complaint->status == 'resolved' || $complaint->status == 'closed') bg-green-100 text-green-800
                        @elseif($complaint->status == 'reopened') bg-yellow-100 text-yellow-800
                        @endif">
                        Status: {{ ucfirst($complaint->status) }}
                    </span>
                    
                    <span class="ml-2 px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                        @if($complaint->priority == 'low') bg-gray-100 text-gray-800
                        @elseif($complaint->priority == 'medium') bg-blue-100 text-blue-800
                        @elseif($complaint->priority == 'high') bg-yellow-100 text-yellow-800
                        @elseif($complaint->priority == 'critical') bg-red-100 text-red-800
                        @endif">
                        Priority: {{ ucfirst($complaint->priority) }}
                    </span>
                </div>
            </div>
            
            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-md font-semibold mb-2">Description</h4>
                <p class="whitespace-pre-line">{{ $complaint->description }}</p>
            </div>
            
            @if($complaint->resolution)
                <div class="mt-4 p-4 bg-green-50 rounded-lg">
                    <h4 class="text-md font-semibold mb-2">Resolution</h4>
                    <p class="whitespace-pre-line">{{ $complaint->resolution }}</p>
                    <p class="text-sm text-gray-500 mt-2">Resolved on: {{ $complaint->resolution_date->format('M d, Y H:i') }}</p>
                </div>
            @endif
            
            @if($complaint->status === 'resolved' && !$complaint->satisfaction_rating)
                <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                    <h4 class="text-md font-semibold mb-2">Provide Feedback</h4>
                    <form action="{{ route('student.complaints.rating', $complaint) }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Satisfaction Rating</label>
                            <div class="flex space-x-2">
                                @for($i = 1; $i <= 5; $i++)
                                    <label class="flex items-center">
                                        <input type="radio" name="satisfaction_rating" value="{{ $i }}" class="mr-1" required>
                                        <span>{{ $i }}</span>
                                    </label>
                                @endfor
                            </div>
                            <p class="text-xs text-gray-500 mt-1">1 = Very Dissatisfied, 5 = Very Satisfied</p>
                        </div>
                        
                        <div class="mb-3">
                            <label for="satisfaction_comment" class="block text-sm font-medium text-gray-700 mb-1">Comments (Optional)</label>
                            <textarea name="satisfaction_comment" id="satisfaction_comment" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                        </div>
                        
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Submit Feedback
                        </button>
                    </form>
                </div>
            @endif
            
            @if(in_array($complaint->status, ['resolved', 'closed']) && !in_array($complaint->status, ['reopened']))
                <div class="mt-4">
                    <form action="{{ route('student.complaints.reopen', $complaint) }}" method="POST" onsubmit="return confirm('Are you sure you want to reopen this complaint?');">
                        @csrf
                        <button type="submit" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                            Reopen Complaint
                        </button>
                    </form>
                </div>
            @endif
        </div>
    </div>
    
    <!-- Attachments -->
    @if($attachments->count() > 0)
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
            <div class="p-6 bg-white border-b border-gray-200">
                <h3 class="text-lg font-semibold mb-4">Attachments</h3>
                <ul class="divide-y divide-gray-200">
                    @foreach($attachments as $attachment)
                        <li class="py-3 flex justify-between items-center">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                                </svg>
                                <span>{{ $attachment->file_name }}</span>
                            </div>
                            <a href="{{ Storage::url($attachment->file_path) }}" target="_blank" class="text-blue-600 hover:text-blue-900">
                                Download
                            </a>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    @endif
    
    <!-- Comments -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
        <div class="p-6 bg-white border-b border-gray-200">
            <h3 class="text-lg font-semibold mb-4">Comments</h3>
            
            @if($comments->count() > 0)
                <div class="space-y-4 mb-6">
                    @foreach($comments as $comment)
                        <div class="p-4 @if($comment->user_id === Auth::id()) bg-blue-50 @else bg-gray-50 @endif rounded-lg">
                            <div class="flex justify-between items-start">
                                <span class="font-semibold">{{ $comment->user->name }}</span>
                                <span class="text-sm text-gray-500">{{ $comment->created_at->format('M d, Y H:i') }}</span>
                            </div>
                            <p class="mt-2 whitespace-pre-line">{{ $comment->comment }}</p>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 mb-4">No comments yet.</p>
            @endif
            
            @if(!in_array($complaint->status, ['closed']))
                <form action="{{ route('student.complaints.comments.store', $complaint) }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="comment" class="block text-sm font-medium text-gray-700 mb-1">Add a Comment</label>
                        <textarea name="comment" id="comment" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" required></textarea>
                    </div>
                    
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Post Comment
                    </button>
                </form>
            @endif
        </div>
    </div>
    
    <!-- Status History -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6 bg-white border-b border-gray-200">
            <h3 class="text-lg font-semibold mb-4">Status History</h3>
            
            @if($statusHistory->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Date
                                </th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Changed By
                                </th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Notes
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($statusHistory as $history)
                                <tr>
                                    <td class="py-2 px-4 border-b border-gray-200">
                                        {{ $history->created_at->format('M d, Y H:i') }}
                                    </td>
                                    <td class="py-2 px-4 border-b border-gray-200">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            @if($history->status == 'new') bg-gray-100 text-gray-800
                                            @elseif($history->status == 'assigned' || $history->status == 'in_progress') bg-blue-100 text-blue-800
                                            @elseif($history->status == 'on_hold') bg-red-100 text-red-800
                                            @elseif($history->status == 'resolved' || $history->status == 'closed') bg-green-100 text-green-800
                                            @elseif($history->status == 'reopened') bg-yellow-100 text-yellow-800
                                            @endif">
                                            {{ ucfirst($history->status) }}
                                        </span>
                                    </td>
                                    <td class="py-2 px-4 border-b border-gray-200">
                                        {{ $history->user->name }}
                                    </td>
                                    <td class="py-2 px-4 border-b border-gray-200">
                                        {{ $history->notes }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <p class="text-gray-500">No status history available.</p>
            @endif
        </div>
    </div>
</x-student-layout>
