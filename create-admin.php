<?php

// Direct database manipulation to create admin user
echo "Creating admin user directly...\n";

// Database file path
$dbFile = 'database/database.sqlite';

if (!file_exists($dbFile)) {
    echo "Error: Database file not found. Run migrations first.\n";
    exit(1);
}

try {
    // Connect to SQLite database
    $pdo = new PDO('sqlite:' . $dbFile);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Clear users table
    $pdo->exec("DELETE FROM users");
    echo "✅ Cleared users table\n";
    
    // Clear roles and permissions
    $pdo->exec("DELETE FROM roles");
    $pdo->exec("DELETE FROM permissions");
    $pdo->exec("DELETE FROM model_has_roles");
    echo "✅ Cleared roles and permissions\n";
    
    // Create roles
    $pdo->exec("INSERT INTO roles (id, name, guard_name, created_at, updated_at) VALUES 
        (1, 'admin', 'web', datetime('now'), datetime('now')),
        (2, 'teacher', 'web', datetime('now'), datetime('now')),
        (3, 'student', 'web', datetime('now'), datetime('now'))");
    echo "✅ Created roles\n";
    
    // Create admin user
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, email_verified_at, department, user_type, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))");
    $stmt->execute(['Admin User', '<EMAIL>', $hashedPassword, date('Y-m-d H:i:s'), 'IT', 'admin']);
    $adminId = $pdo->lastInsertId();
    echo "✅ Created admin user\n";
    
    // Assign admin role
    $pdo->exec("INSERT INTO model_has_roles (role_id, model_type, model_id) VALUES (1, 'App\\Models\\User', $adminId)");
    echo "✅ Assigned admin role\n";
    
    // Create student user
    $hashedPassword = password_hash('student123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, email_verified_at, department, user_type, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))");
    $stmt->execute(['Student User', '<EMAIL>', $hashedPassword, date('Y-m-d H:i:s'), 'CS', 'student']);
    $studentId = $pdo->lastInsertId();
    echo "✅ Created student user\n";
    
    // Assign student role
    $pdo->exec("INSERT INTO model_has_roles (role_id, model_type, model_id) VALUES (3, 'App\\Models\\User', $studentId)");
    echo "✅ Assigned student role\n";
    
    echo "\n🎉 SUCCESS!\n";
    echo "Login credentials:\n";
    echo "• Admin: <EMAIL> / admin123\n";
    echo "• Student: <EMAIL> / student123\n";
    echo "\nURLs:\n";
    echo "• Admin: http://localhost:8000/admin\n";
    echo "• Student: http://localhost:8000/student/dashboard\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}
