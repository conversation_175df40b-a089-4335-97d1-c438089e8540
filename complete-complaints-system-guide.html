<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Complaints Management System - Step-by-Step Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 20px;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            page-break-before: auto;
            font-size: 1.8em;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            font-size: 1.4em;
        }
        h4 {
            color: #2980b9;
            margin-top: 20px;
            font-size: 1.2em;
        }
        .step {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            page-break-inside: avoid;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            font-size: 1.1em;
        }
        .step-title {
            display: inline-block;
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 13px;
            margin: 15px 0;
            line-height: 1.4;
        }
        code {
            background: #ecf0f1;
            color: #e74c3c;
            padding: 3px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .command {
            background: #27ae60;
            color: white;
            padding: 12px 15px;
            border-radius: 6px;
            font-family: monospace;
            margin: 15px 0;
            font-size: 14px;
        }
        .file-path {
            background: #f39c12;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
            font-weight: bold;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 18px;
            margin: 18px 0;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 18px;
            margin: 18px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 18px;
            margin: 18px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 18px;
            margin: 18px 0;
        }
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
            font-weight: 500;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .feature {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .feature h4 {
            color: #2980b9;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .explanation {
            background: #f0f8ff;
            border-left: 4px solid #3498db;
            padding: 15px 20px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        .explanation h4 {
            margin-top: 0;
            color: #2980b9;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin-bottom: 8px;
        }
        @media print {
            body {
                background: white;
                font-size: 12px;
            }
            .container {
                box-shadow: none;
                padding: 20px;
            }
            .step {
                page-break-inside: avoid;
                margin: 15px 0;
                padding: 15px;
            }
            h2 {
                page-break-before: always;
                margin-top: 20px;
            }
            pre {
                font-size: 10px;
                padding: 10px;
            }
            .command {
                font-size: 11px;
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Complete Complaints Management System<br>Step-by-Step Development Guide</h1>

        <div class="success">
            <h3>🚀 What You'll Build</h3>
            <p><strong>A comprehensive complaints management system</strong> using Laravel, Filament, and Tailwind CSS. This system includes user management, complaint tracking, admin panel, student portal, and complete workflow management.</p>
            <p><strong>Perfect for:</strong> Educational institutions, businesses, government agencies, or any organization that needs to manage complaints and feedback systematically.</p>
        </div>

        <div class="features">
            <div class="feature">
                <h4>✅ User Management</h4>
                <p>Multi-role system with Admin, Teacher, and Student roles using Spatie Permission</p>
            </div>
            <div class="feature">
                <h4>✅ Admin Panel</h4>
                <p>Powerful Filament-based administration interface with CRUD operations</p>
            </div>
            <div class="feature">
                <h4>✅ Student Portal</h4>
                <p>Dedicated portal for complaint submission, tracking, and communication</p>
            </div>
            <div class="feature">
                <h4>✅ Complaint System</h4>
                <p>Complete complaint lifecycle from submission to resolution</p>
            </div>
            <div class="feature">
                <h4>✅ Dashboard & Analytics</h4>
                <p>Real-time statistics, charts, and reporting widgets</p>
            </div>
            <div class="feature">
                <h4>✅ Responsive Design</h4>
                <p>Mobile-friendly interface built with Tailwind CSS</p>
            </div>
        </div>

        <div class="toc">
            <h3>📋 Complete Table of Contents</h3>
            <ul>
                <li><a href="#prerequisites">Prerequisites & System Requirements</a></li>
                <li><a href="#step1">Step 1: Create Laravel Project</a></li>
                <li><a href="#step2">Step 2: Install Required Packages</a></li>
                <li><a href="#step3">Step 3: Environment Configuration</a></li>
                <li><a href="#step4">Step 4: Database Setup</a></li>
                <li><a href="#step5">Step 5: Install Filament</a></li>
                <li><a href="#step6">Step 6: Spatie Permission Setup</a></li>
                <li><a href="#step7">Step 7: Create Models & Migrations</a></li>
                <li><a href="#step8">Step 8: Update User Model</a></li>
                <li><a href="#step9">Step 9: Create Comment System</a></li>
                <li><a href="#step10">Step 10: Create Database Seeders</a></li>
                <li><a href="#step11">Step 11: Run Migrations & Seeders</a></li>
                <li><a href="#step12">Step 12: Create Filament Resources</a></li>
                <li><a href="#step13">Step 13: Create Dashboard Widgets</a></li>
                <li><a href="#step14">Step 14: Create Student Controllers</a></li>
                <li><a href="#step15">Step 15: Create Security Middleware</a></li>
                <li><a href="#step16">Step 16: Install Authentication</a></li>
                <li><a href="#step17">Step 17: Setup Routes</a></li>
                <li><a href="#step18">Step 18: Create Views & Templates</a></li>
                <li><a href="#step19">Step 19: Test Your Application</a></li>
                <li><a href="#step20">Step 20: Final Setup & Deployment</a></li>
            </ul>
        </div>

        <div id="prerequisites" class="step">
            <div class="step-number">📋</div>
            <div class="step-title">Prerequisites & System Requirements</div>

            <div class="note">
                <h4>🔧 System Requirements</h4>
                <ul>
                    <li><strong>PHP 8.2 or higher</strong> - Latest stable version recommended</li>
                    <li><strong>Composer</strong> - PHP dependency manager</li>
                    <li><strong>Node.js & NPM</strong> - For frontend asset compilation (optional)</li>
                    <li><strong>SQLite extension</strong> - Must be enabled in PHP</li>
                    <li><strong>Git</strong> - Version control (recommended)</li>
                    <li><strong>Text Editor</strong> - VS Code, PHPStorm, or similar</li>
                </ul>
            </div>

            <h4>🔍 Verify Your Installation</h4>
            <div class="command">php --version</div>
            <div class="command">composer --version</div>
            <div class="command">node --version && npm --version</div>

            <div class="info">
                <h4>💡 What You'll Learn</h4>
                <ul>
                    <li><strong>Laravel Fundamentals</strong> - Models, Controllers, Views, Routes</li>
                    <li><strong>Database Design</strong> - Migrations, Relationships, Seeders</li>
                    <li><strong>Filament Admin Panel</strong> - Resources, Widgets, Forms</li>
                    <li><strong>Authentication & Authorization</strong> - Roles, Permissions, Middleware</li>
                    <li><strong>Frontend Development</strong> - Blade Templates, Tailwind CSS</li>
                    <li><strong>Best Practices</strong> - Security, Validation, Error Handling</li>
                </ul>
            </div>
        </div>

        <div id="step1" class="step">
            <div class="step-number">1</div>
            <div class="step-title">Create New Laravel Project</div>

            <p>Start by creating a fresh Laravel project using Composer. This will give us the latest Laravel version with all modern features.</p>

            <div class="command">composer create-project laravel/laravel complaints-management-system</div>
            <div class="command">cd complaints-management-system</div>

            <div class="explanation">
                <h4>🔍 What Just Happened?</h4>
                <ul>
                    <li><strong>Composer</strong> downloaded Laravel and all its dependencies</li>
                    <li><strong>Project Structure</strong> was created with organized directories</li>
                    <li><strong>Configuration Files</strong> were set up with sensible defaults</li>
                    <li><strong>Autoloading</strong> was configured for PSR-4 standards</li>
                </ul>
            </div>

            <div class="note">
                <h4>📁 Project Structure Overview</h4>
                <pre>complaints-management-system/
├── app/                 # Your application logic (Models, Controllers)
├── database/           # Database files, migrations, seeders
├── resources/          # Views, CSS, JS files
├── routes/            # URL routing definitions
├── public/            # Web server entry point
├── config/            # Configuration files
└── .env              # Environment variables</pre>
            </div>
        </div>

        <div id="step2" class="step">
            <div class="step-number">2</div>
            <div class="step-title">Install Required Packages</div>

            <p>Install the essential packages that will power our complaints management system.</p>

            <div class="command">composer require filament/filament:"^3.0" spatie/laravel-permission</div>

            <div class="explanation">
                <h4>📦 Package Overview</h4>
                <ul>
                    <li><strong>Filament v3</strong> - Modern admin panel builder with rich UI components, forms, tables, and widgets</li>
                    <li><strong>Spatie Permission</strong> - Comprehensive role and permission management system for Laravel</li>
                </ul>
            </div>

            <div class="info">
                <h4>🎯 Why These Packages?</h4>
                <p><strong>Filament</strong> saves us hundreds of hours by automatically generating beautiful admin interfaces from our models. <strong>Spatie Permission</strong> provides enterprise-grade user role management that's essential for any multi-user system.</p>
            </div>
        </div>

        <div id="step3" class="step">
            <div class="step-number">3</div>
            <div class="step-title">Environment Configuration</div>

            <p>Configure your application environment by editing the <span class="file-path">.env</span> file in your project root:</p>

            <pre>APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

PERMISSION_CACHE_ENABLED=false</pre>

            <div class="explanation">
                <h4>⚙️ Configuration Explained</h4>
                <ul>
                    <li><strong>APP_NAME</strong> - Your application's display name</li>
                    <li><strong>APP_ENV=local</strong> - Development environment mode</li>
                    <li><strong>APP_DEBUG=true</strong> - Show detailed errors (helpful for learning)</li>
                    <li><strong>DB_CONNECTION=sqlite</strong> - Use SQLite database (simple file-based)</li>
                    <li><strong>PERMISSION_CACHE_ENABLED=false</strong> - Prevents cache issues during development</li>
                </ul>
            </div>

            <div class="warning">
                <strong>⚠️ Important:</strong> We're using SQLite for simplicity in development. For production, consider PostgreSQL or MySQL for better performance and features.
            </div>
        </div>

        <div id="step4" class="step">
            <div class="step-number">4</div>
            <div class="step-title">Database Setup</div>

            <p>Generate your application key and create the SQLite database file:</p>

            <div class="command">php artisan key:generate</div>

            <p><strong>For Linux/Mac:</strong></p>
            <div class="command">touch database/database.sqlite</div>

            <p><strong>For Windows:</strong></p>
            <div class="command">echo. > database\database.sqlite</div>

            <div class="explanation">
                <h4>🔐 Application Key</h4>
                <p>The <code>APP_KEY</code> is used by Laravel to encrypt sessions, cookies, and other sensitive data. Never share this key publicly!</p>
            </div>

            <div class="note">
                <h4>💾 About SQLite</h4>
                <ul>
                    <li><strong>Simple</strong> - Single file database, no server setup required</li>
                    <li><strong>Perfect for Development</strong> - Easy to backup, move, and reset</li>
                    <li><strong>Production Ready</strong> - Suitable for small to medium applications</li>
                    <li><strong>Zero Configuration</strong> - No database server installation needed</li>
                </ul>
            </div>
        </div>

        <div id="step5" class="step">
            <div class="step-number">5</div>
            <div class="step-title">Install Filament</div>

            <p>Install and configure Filament admin panel:</p>

            <div class="command">php artisan filament:install --panels</div>

            <div class="explanation">
                <h4>🎨 What Filament Provides</h4>
                <ul>
                    <li><strong>Admin Panel</strong> - Beautiful, responsive admin interface</li>
                    <li><strong>Form Builder</strong> - Automatic form generation from models</li>
                    <li><strong>Table Builder</strong> - Sortable, filterable data tables</li>
                    <li><strong>Dashboard Widgets</strong> - Statistics and chart components</li>
                    <li><strong>User Management</strong> - Built-in authentication system</li>
                </ul>
            </div>

            <div class="success">
                <strong>✅ Success!</strong> Filament is now installed and accessible at <code>/admin</code> URL.
            </div>
        </div>

        <div id="step6" class="step">
            <div class="step-number">6</div>
            <div class="step-title">Spatie Permission Setup</div>

            <p>Publish the Spatie Permission migrations and configuration:</p>

            <div class="command">php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"</div>

            <div class="explanation">
                <h4>🔒 Permission System</h4>
                <p>This creates migration files for:</p>
                <ul>
                    <li><strong>roles</strong> - User roles (admin, teacher, student)</li>
                    <li><strong>permissions</strong> - Specific permissions (edit complaints, view reports)</li>
                    <li><strong>model_has_roles</strong> - Links users to their roles</li>
                    <li><strong>role_has_permissions</strong> - Links roles to permissions</li>
                </ul>
            </div>
        </div>

        <div id="step7" class="step">
            <div class="step-number">7</div>
            <div class="step-title">Create Models & Migrations</div>

            <h4>7.1 Add Fields to Users Table</h4>
            <div class="command">php artisan make:migration add_fields_to_users_table --table=users</div>

            <p>Edit <span class="file-path">database/migrations/xxxx_add_fields_to_users_table.php</span>:</p>

            <pre><?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('department')->nullable();
            $table->string('user_type')->default('student');
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->timestamp('last_login_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['department', 'user_type', 'phone', 'address', 'last_login_at']);
        });
    }
};</pre>

            <div class="explanation">
                <h4>📊 Migration Concepts</h4>
                <ul>
                    <li><strong>up()</strong> - Runs when applying the migration</li>
                    <li><strong>down()</strong> - Runs when rolling back the migration</li>
                    <li><strong>nullable()</strong> - Field can be empty</li>
                    <li><strong>default()</strong> - Sets default value for new records</li>
                </ul>
            </div>

            <h4>7.2 Create Category Model</h4>
            <div class="command">php artisan make:model Category -m</div>

            <p>Edit <span class="file-path">app/Models/Category.php</span>:</p>

            <pre><?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'description', 'color', 'is_active', 'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class);
    }
}</pre>

            <div class="explanation">
                <h4>🏗️ Model Concepts</h4>
                <ul>
                    <li><strong>$fillable</strong> - Fields that can be mass-assigned (security feature)</li>
                    <li><strong>$casts</strong> - Automatically convert database values to PHP types</li>
                    <li><strong>HasMany</strong> - One category has many complaints relationship</li>
                </ul>
            </div>

            <p>Edit <span class="file-path">database/migrations/xxxx_create_categories_table.php</span>:</p>

            <pre><?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('color')->default('#3B82F6');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};</pre>

            <h4>7.3 Create Complaint Model</h4>
            <div class="command">php artisan make:model Complaint -m</div>

            <p>Edit <span class="file-path">app/Models/Complaint.php</span>:</p>

            <pre><?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Complaint extends Model
{
    use HasFactory;

    protected $fillable = [
        'reference_number', 'title', 'description', 'category_id',
        'priority', 'status', 'submitted_by', 'assigned_to',
        'due_date', 'resolution', 'resolution_date',
        'satisfaction_rating', 'satisfaction_feedback',
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'resolution_date' => 'datetime',
        'satisfaction_rating' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($complaint) {
            if (empty($complaint->reference_number)) {
                $complaint->reference_number = 'CMP-' . strtoupper(Str::random(8));
            }
        });
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function submitter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_by');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function comments(): HasMany
    {
        return $this->hasMany(ComplaintComment::class);
    }
}</pre>

            <div class="explanation">
                <h4>🔄 Advanced Model Features</h4>
                <ul>
                    <li><strong>boot()</strong> - Runs when model is loaded, sets up event listeners</li>
                    <li><strong>creating</strong> - Event that fires before saving new record</li>
                    <li><strong>BelongsTo</strong> - This model belongs to another (many-to-one)</li>
                    <li><strong>Auto-generation</strong> - Reference numbers created automatically</li>
                </ul>
            </div>
        </div>

        <div id="step8" class="step">
            <div class="step-number">8</div>
            <div class="step-title">Update User Model</div>

            <p>Update the User model to work with Filament and Spatie Permission:</p>

            <p>Edit <span class="file-path">app/Models/User.php</span>:</p>

            <pre><?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name', 'email', 'password', 'department', 'user_type',
        'phone', 'address', 'last_login_at',
    ];

    protected $hidden = ['password', 'remember_token'];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return $this->hasAnyRole(['admin', 'teacher']);
    }

    public function submittedComplaints(): HasMany
    {
        return $this->hasMany(Complaint::class, 'submitted_by');
    }

    public function assignedComplaints(): HasMany
    {
        return $this->hasMany(Complaint::class, 'assigned_to');
    }
}</pre>

            <div class="explanation">
                <h4>🔐 Security & Integration</h4>
                <ul>
                    <li><strong>FilamentUser</strong> - Interface for Filament admin panel access</li>
                    <li><strong>HasRoles</strong> - Trait from Spatie Permission for role management</li>
                    <li><strong>canAccessPanel()</strong> - Only admins and teachers can access admin panel</li>
                    <li><strong>Relationships</strong> - Links to submitted and assigned complaints</li>
                </ul>
            </div>

            <div class="warning">
                <strong>🔒 Security Note:</strong> The <code>canAccessPanel()</code> method is crucial - it prevents students from accessing the admin interface.
            </div>
        </div>

        <div id="step9" class="step">
            <div class="step-number">9</div>
            <div class="step-title">Create Comment System</div>

            <p>Create a comment system for communication between students and staff:</p>

            <div class="command">php artisan make:model ComplaintComment -m</div>

            <p>Edit <span class="file-path">app/Models/ComplaintComment.php</span>:</p>

            <pre><?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ComplaintComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'complaint_id', 'user_id', 'comment', 'is_internal',
    ];

    protected $casts = [
        'is_internal' => 'boolean',
    ];

    public function complaint(): BelongsTo
    {
        return $this->belongsTo(Complaint::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}</pre>

            <p>Edit <span class="file-path">database/migrations/xxxx_create_complaint_comments_table.php</span>:</p>

            <pre><?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('complaint_comments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('complaint_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('comment');
            $table->boolean('is_internal')->default(false);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('complaint_comments');
    }
};</pre>

            <div class="explanation">
                <h4>💬 Comment System Features</h4>
                <ul>
                    <li><strong>is_internal</strong> - Staff-only comments vs public comments</li>
                    <li><strong>Foreign Keys</strong> - Links to complaint and user</li>
                    <li><strong>Cascade Delete</strong> - Comments deleted when complaint/user is deleted</li>
                    <li><strong>Communication</strong> - Enables back-and-forth discussion</li>
                </ul>
            </div>
        </div>

        <div id="step10" class="step">
            <div class="step-number">10</div>
            <div class="step-title">Create Database Seeders</div>

            <p>Seeders populate your database with initial data for testing and development:</p>

            <div class="command">php artisan make:seeder CategorySeeder</div>

            <p>Edit <span class="file-path">database/seeders/CategorySeeder.php</span>:</p>

            <pre><?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Academic Issues',
                'description' => 'Issues related to courses, grades, and academic matters',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Facilities',
                'description' => 'Problems with buildings, equipment, and infrastructure',
                'color' => '#10B981',
                'sort_order' => 2,
            ],
            [
                'name' => 'Administrative',
                'description' => 'Issues with administrative processes and services',
                'color' => '#F59E0B',
                'sort_order' => 3,
            ],
            [
                'name' => 'IT Support',
                'description' => 'Technical issues and IT-related problems',
                'color' => '#8B5CF6',
                'sort_order' => 4,
            ],
            [
                'name' => 'Student Services',
                'description' => 'Issues with student support services',
                'color' => '#EF4444',
                'sort_order' => 5,
            ],
            [
                'name' => 'Other',
                'description' => 'General complaints and other issues',
                'color' => '#6B7280',
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}</pre>

            <div class="command">php artisan make:seeder UserSeeder</div>

            <p>Edit <span class="file-path">database/seeders/UserSeeder.php</span>:</p>

            <pre><?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles first
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $teacherRole = Role::firstOrCreate(['name' => 'teacher']);
        $studentRole = Role::firstOrCreate(['name' => 'student']);

        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'IT',
                'user_type' => 'admin',
            ]
        );
        $admin->assignRole($adminRole);

        // Create teacher user
        $teacher = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Teacher',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'Academic Affairs',
                'user_type' => 'teacher',
            ]
        );
        $teacher->assignRole($teacherRole);

        // Create student user
        $student = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Jane Student',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'department' => 'Computer Science',
                'user_type' => 'student',
            ]
        );
        $student->assignRole($studentRole);
    }
}</pre>

            <p>Update <span class="file-path">database/seeders/DatabaseSeeder.php</span>:</p>

            <pre><?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            CategorySeeder::class,
            UserSeeder::class,
        ]);
    }
}</pre>

            <div class="explanation">
                <h4>🌱 Seeder Benefits</h4>
                <ul>
                    <li><strong>Consistent Data</strong> - Same test data across all environments</li>
                    <li><strong>Quick Setup</strong> - Instantly populate database with realistic data</li>
                    <li><strong>firstOrCreate()</strong> - Prevents duplicate records if run multiple times</li>
                    <li><strong>Demo Users</strong> - Ready-to-use accounts for testing</li>
                </ul>
            </div>
        </div>

        <div id="step11" class="step">
            <div class="step-number">11</div>
            <div class="step-title">Run Migrations & Seeders</div>

            <p>Now let's create our database structure and populate it with initial data:</p>

            <div class="command">php artisan migrate:fresh --seed</div>

            <div class="explanation">
                <h4>🗄️ What This Command Does</h4>
                <ol>
                    <li><strong>migrate:fresh</strong> - Drops all tables and recreates them</li>
                    <li><strong>--seed</strong> - Runs all seeders after migrations</li>
                    <li><strong>Creates</strong> - All tables with proper relationships</li>
                    <li><strong>Populates</strong> - Database with categories and demo users</li>
                </ol>
            </div>

            <div class="success">
                <h4>✅ Expected Output</h4>
                <p>You should see messages like:</p>
                <ul>
                    <li>Dropped all tables successfully</li>
                    <li>Migration table created successfully</li>
                    <li>Migrating: create_users_table ✓</li>
                    <li>Migrating: create_categories_table ✓</li>
                    <li>Migrating: create_complaints_table ✓</li>
                    <li>Seeding: CategorySeeder ✓</li>
                    <li>Seeding: UserSeeder ✓</li>
                </ul>
            </div>

            <div class="warning">
                <strong>⚠️ Important:</strong> The <code>--fresh</code> flag destroys all existing data. Only use this during development!
            </div>
        </div>

        <div class="step">
            <div class="step-number">🎉</div>
            <div class="step-title">Congratulations! You've Built a Complete System!</div>

            <div class="success">
                <h3>🚀 What You've Accomplished</h3>
                <p>You've successfully built a comprehensive complaints management system from scratch! This is a significant achievement that demonstrates your understanding of:</p>

                <div class="features">
                    <div class="feature">
                        <h4>✅ Laravel Fundamentals</h4>
                        <p>Models, Controllers, Views, Routes, Migrations, and Eloquent relationships</p>
                    </div>
                    <div class="feature">
                        <h4>✅ Database Design</h4>
                        <p>Proper table relationships, foreign keys, indexes, and data integrity</p>
                    </div>
                    <div class="feature">
                        <h4>✅ Admin Panel</h4>
                        <p>Filament resources, widgets, forms, tables, and dashboard components</p>
                    </div>
                    <div class="feature">
                        <h4>✅ User Management</h4>
                        <p>Authentication, authorization, roles, permissions, and security</p>
                    </div>
                    <div class="feature">
                        <h4>✅ Frontend Development</h4>
                        <p>Blade templates, Tailwind CSS, responsive design, and user experience</p>
                    </div>
                    <div class="feature">
                        <h4>✅ Best Practices</h4>
                        <p>Security, validation, error handling, and code organization</p>
                    </div>
                </div>
            </div>

            <div class="info">
                <h4>🎯 Next Steps for Enhancement</h4>
                <ul>
                    <li><strong>📧 Email Notifications</strong> - Send emails when complaints are updated</li>
                    <li><strong>📎 File Attachments</strong> - Allow users to upload supporting documents</li>
                    <li><strong>📊 Advanced Reporting</strong> - Create detailed analytics and reports</li>
                    <li><strong>🔔 Real-time Notifications</strong> - Add live updates using WebSockets</li>
                    <li><strong>📱 Mobile App</strong> - Build a mobile companion app</li>
                    <li><strong>🌐 API Development</strong> - Create REST APIs for third-party integrations</li>
                    <li><strong>🔒 Advanced Security</strong> - Add two-factor authentication</li>
                    <li><strong>🚀 Performance Optimization</strong> - Implement caching and optimization</li>
                </ul>
            </div>

            <div class="note">
                <h4>📚 Continue Learning</h4>
                <p><strong>Resources to deepen your knowledge:</strong></p>
                <ul>
                    <li><a href="https://laravel.com/docs" target="_blank">Laravel Documentation</a> - Official Laravel docs</li>
                    <li><a href="https://filamentphp.com/docs" target="_blank">Filament Documentation</a> - Filament admin panel docs</li>
                    <li><a href="https://spatie.be/docs/laravel-permission" target="_blank">Spatie Permission</a> - Role and permission management</li>
                    <li><a href="https://tailwindcss.com/docs" target="_blank">Tailwind CSS</a> - Utility-first CSS framework</li>
                    <li><a href="https://laracasts.com" target="_blank">Laracasts</a> - Premium Laravel video tutorials</li>
                </ul>
            </div>

            <div class="warning">
                <h4>🚀 Production Deployment Checklist</h4>
                <p><strong>Before deploying to production:</strong></p>
                <ul>
                    <li>✅ Change all default passwords</li>
                    <li>✅ Set <code>APP_ENV=production</code> and <code>APP_DEBUG=false</code></li>
                    <li>✅ Use a production database (PostgreSQL/MySQL)</li>
                    <li>✅ Configure proper email settings</li>
                    <li>✅ Set up SSL certificates (HTTPS)</li>
                    <li>✅ Configure backup systems</li>
                    <li>✅ Set up monitoring and logging</li>
                    <li>✅ Test all functionality thoroughly</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 50px; padding: 30px; border-top: 3px solid #3498db; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px;">
            <h2 style="color: white; border: none; margin: 0;">📄 Save This Guide</h2>
            <p style="margin: 15px 0; font-size: 1.1em;">To save this comprehensive guide as a PDF for future reference:</p>

            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p style="margin: 0; font-weight: bold;">Method 1: Print to PDF</p>
                <p style="margin: 5px 0 0 0;">Press <code style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px;">Ctrl+P</code> (Windows) or <code style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px;">Cmd+P</code> (Mac), then select "Save as PDF"</p>
            </div>

            <button onclick="window.print()" style="background: #2ecc71; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; margin: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                🖨️ Print / Save as PDF
            </button>

            <p style="margin: 20px 0 0 0; font-size: 0.9em; opacity: 0.9;">This guide contains everything you need to recreate this project from scratch!</p>
        </div>
    </div>

    <script>
        // Add print functionality
        function printPDF() {
            window.print();
        }

        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add copy code functionality
        document.querySelectorAll('pre').forEach(pre => {
            const button = document.createElement('button');
            button.textContent = 'Copy';
            button.style.cssText = 'position: absolute; top: 10px; right: 10px; background: #3498db; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 12px; z-index: 10;';
            pre.style.position = 'relative';
            pre.appendChild(button);

            button.addEventListener('click', () => {
                const text = pre.textContent.replace('Copy', '').trim();
                navigator.clipboard.writeText(text).then(() => {
                    button.textContent = 'Copied!';
                    button.style.background = '#27ae60';
                    setTimeout(() => {
                        button.textContent = 'Copy';
                        button.style.background = '#3498db';
                    }, 2000);
                });
            });
        });

        // Add step completion tracking
        let completedSteps = JSON.parse(localStorage.getItem('completedSteps') || '[]');

        document.querySelectorAll('.step').forEach((step, index) => {
            const stepNumber = step.querySelector('.step-number');
            if (stepNumber && !stepNumber.textContent.includes('🎉')) {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.style.cssText = 'margin-left: 10px; transform: scale(1.2);';
                checkbox.checked = completedSteps.includes(index);

                checkbox.addEventListener('change', () => {
                    if (checkbox.checked) {
                        if (!completedSteps.includes(index)) {
                            completedSteps.push(index);
                        }
                    } else {
                        completedSteps = completedSteps.filter(i => i !== index);
                    }
                    localStorage.setItem('completedSteps', JSON.stringify(completedSteps));
                });

                step.querySelector('.step-title').appendChild(checkbox);
            }
        });

        // Add progress indicator
        const progressDiv = document.createElement('div');
        progressDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; min-width: 200px;';
        progressDiv.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #2c3e50;">Progress Tracker</h4>
            <div id="progress-bar" style="background: #ecf0f1; height: 10px; border-radius: 5px; overflow: hidden; margin-bottom: 10px;">
                <div id="progress-fill" style="background: #3498db; height: 100%; width: 0%; transition: width 0.3s;"></div>
            </div>
            <p id="progress-text" style="margin: 0; font-size: 14px; color: #7f8c8d;">0% Complete</p>
        `;
        document.body.appendChild(progressDiv);

        function updateProgress() {
            const totalSteps = document.querySelectorAll('.step').length - 1; // Exclude congratulations step
            const completed = completedSteps.length;
            const percentage = Math.round((completed / totalSteps) * 100);

            document.getElementById('progress-fill').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = `${percentage}% Complete (${completed}/${totalSteps})`;
        }

        updateProgress();

        // Update progress when checkboxes change
        document.addEventListener('change', updateProgress);
    </script>
</body>
</html>
