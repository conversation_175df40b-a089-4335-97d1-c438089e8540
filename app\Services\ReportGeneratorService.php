<?php

namespace App\Services;

use App\Models\Report;
use App\Models\ComplaintAnalytics;
use App\Models\Complaint;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Carbon;

class ReportGeneratorService
{
    /**
     * Generate a report based on its type and parameters
     */
    public function generate(Report $report): string
    {
        $data = $this->generateReportData($report);
        
        return match ($report->format) {
            'pdf' => $this->generatePDF($report, $data),
            'excel' => $this->generateExcel($report, $data),
            'csv' => $this->generateCSV($report, $data),
            default => throw new \InvalidArgumentException("Unsupported format: {$report->format}"),
        };
    }

    /**
     * Generate report data based on type
     */
    private function generateReportData(Report $report): array
    {
        return match ($report->type) {
            'complaint_summary' => $this->generateComplaintSummaryData($report),
            'sla_compliance' => $this->generateSLAComplianceData($report),
            'satisfaction_analysis' => $this->generateSatisfactionAnalysisData($report),
            'staff_performance' => $this->generateStaffPerformanceData($report),
            'category_analysis' => $this->generateCategoryAnalysisData($report),
            'trend_analysis' => $this->generateTrendAnalysisData($report),
            'cost_analysis' => $this->generateCostAnalysisData($report),
            'iso_audit' => $this->generateISOAuditData($report),
            default => throw new \InvalidArgumentException("Unsupported report type: {$report->type}"),
        };
    }

    /**
     * Generate complaint summary data
     */
    private function generateComplaintSummaryData(Report $report): array
    {
        $dateRange = $report->parameters['date_range'] ?? '30days';
        [$startDate, $endDate] = $this->getDateRange($dateRange);

        $complaints = ComplaintAnalytics::whereBetween('submitted_date', [$startDate, $endDate])->get();

        return [
            'title' => 'Complaint Summary Report',
            'period' => "{$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}",
            'total_complaints' => $complaints->count(),
            'status_breakdown' => $complaints->groupBy('status')->map->count(),
            'priority_breakdown' => $complaints->groupBy('priority')->map->count(),
            'category_breakdown' => $complaints->groupBy('category.name')->map->count(),
            'avg_resolution_time' => $complaints->whereNotNull('resolution_time_hours')->avg('resolution_time_hours'),
            'sla_compliance_rate' => $complaints->where('sla_breached', false)->count() / max($complaints->count(), 1) * 100,
            'complaints' => $complaints->take(100), // Limit for performance
        ];
    }

    /**
     * Generate SLA compliance data
     */
    private function generateSLAComplianceData(Report $report): array
    {
        $dateRange = $report->parameters['date_range'] ?? '30days';
        [$startDate, $endDate] = $this->getDateRange($dateRange);

        $analytics = ComplaintAnalytics::whereBetween('submitted_date', [$startDate, $endDate])->get();

        $groupBy = $report->parameters['group_by'] ?? 'category';
        
        if ($groupBy === 'category') {
            $grouped = $analytics->groupBy('category.name');
        } else {
            $grouped = $analytics->groupBy($groupBy);
        }

        $complianceData = $grouped->map(function ($items, $key) {
            $total = $items->count();
            $compliant = $items->where('sla_breached', false)->count();
            return [
                'name' => $key,
                'total' => $total,
                'compliant' => $compliant,
                'compliance_rate' => $total > 0 ? ($compliant / $total) * 100 : 0,
                'avg_resolution_time' => $items->whereNotNull('resolution_time_hours')->avg('resolution_time_hours'),
            ];
        });

        return [
            'title' => 'SLA Compliance Report',
            'period' => "{$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}",
            'overall_compliance' => $analytics->where('sla_breached', false)->count() / max($analytics->count(), 1) * 100,
            'compliance_data' => $complianceData,
        ];
    }

    /**
     * Generate satisfaction analysis data
     */
    private function generateSatisfactionAnalysisData(Report $report): array
    {
        $dateRange = $report->parameters['date_range'] ?? '90days';
        [$startDate, $endDate] = $this->getDateRange($dateRange);

        $analytics = ComplaintAnalytics::whereBetween('submitted_date', [$startDate, $endDate])
            ->whereNotNull('satisfaction_rating')
            ->get();

        $ratingDistribution = $analytics->groupBy('satisfaction_rating')->map->count();
        $avgRating = $analytics->avg('satisfaction_rating');

        return [
            'title' => 'Satisfaction Analysis Report',
            'period' => "{$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}",
            'total_responses' => $analytics->count(),
            'average_rating' => $avgRating,
            'rating_distribution' => $ratingDistribution,
            'satisfaction_rate' => $analytics->whereIn('satisfaction_rating', [4, 5])->count() / max($analytics->count(), 1) * 100,
            'category_satisfaction' => $analytics->groupBy('category.name')->map(function ($items) {
                return [
                    'count' => $items->count(),
                    'avg_rating' => $items->avg('satisfaction_rating'),
                ];
            }),
        ];
    }

    /**
     * Generate staff performance data
     */
    private function generateStaffPerformanceData(Report $report): array
    {
        $dateRange = $report->parameters['date_range'] ?? '30days';
        [$startDate, $endDate] = $this->getDateRange($dateRange);

        $staffPerformance = User::role(['admin', 'teacher'])
            ->with(['resolvedComplaints' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('resolution_date', [$startDate, $endDate]);
            }])
            ->get()
            ->map(function ($user) {
                $resolved = $user->resolvedComplaints;
                return [
                    'name' => $user->name,
                    'total_resolved' => $resolved->count(),
                    'avg_resolution_time' => $resolved->avg('resolution_time_hours'),
                    'sla_compliance' => $resolved->where('sla_breached', false)->count() / max($resolved->count(), 1) * 100,
                    'avg_satisfaction' => $resolved->whereNotNull('satisfaction_rating')->avg('satisfaction_rating'),
                ];
            });

        return [
            'title' => 'Staff Performance Report',
            'period' => "{$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}",
            'staff_performance' => $staffPerformance,
        ];
    }

    /**
     * Generate category analysis data
     */
    private function generateCategoryAnalysisData(Report $report): array
    {
        $dateRange = $report->parameters['date_range'] ?? '90days';
        [$startDate, $endDate] = $this->getDateRange($dateRange);

        $analytics = ComplaintAnalytics::whereBetween('submitted_date', [$startDate, $endDate])->get();
        
        $categoryData = $analytics->groupBy('category.name')->map(function ($items, $categoryName) {
            return [
                'name' => $categoryName,
                'total_complaints' => $items->count(),
                'avg_resolution_time' => $items->whereNotNull('resolution_time_hours')->avg('resolution_time_hours'),
                'sla_compliance' => $items->where('sla_breached', false)->count() / max($items->count(), 1) * 100,
                'avg_satisfaction' => $items->whereNotNull('satisfaction_rating')->avg('satisfaction_rating'),
                'priority_breakdown' => $items->groupBy('priority')->map->count(),
            ];
        });

        return [
            'title' => 'Category Analysis Report',
            'period' => "{$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}",
            'category_data' => $categoryData,
        ];
    }

    /**
     * Generate trend analysis data
     */
    private function generateTrendAnalysisData(Report $report): array
    {
        $dateRange = $report->parameters['date_range'] ?? '12months';
        [$startDate, $endDate] = $this->getDateRange($dateRange);

        $analyticsService = new AnalyticsService();
        $trendData = $analyticsService->getTrendAnalysis($dateRange);

        return [
            'title' => 'Trend Analysis Report',
            'period' => "{$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}",
            'trend_data' => $trendData,
        ];
    }

    /**
     * Generate cost analysis data
     */
    private function generateCostAnalysisData(Report $report): array
    {
        $dateRange = $report->parameters['date_range'] ?? '12months';
        
        $analyticsService = new AnalyticsService();
        $costData = $analyticsService->getCostAnalysis($dateRange);

        return [
            'title' => 'Cost Analysis Report',
            'cost_data' => $costData,
        ];
    }

    /**
     * Generate ISO audit data
     */
    private function generateISOAuditData(Report $report): array
    {
        $dateRange = $report->parameters['date_range'] ?? '12months';
        [$startDate, $endDate] = $this->getDateRange($dateRange);

        // This would include comprehensive ISO 21001 compliance metrics
        return [
            'title' => 'ISO 21001 Audit Report',
            'period' => "{$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}",
            'compliance_metrics' => [
                'complaint_handling_process' => 'Compliant',
                'stakeholder_satisfaction' => 'Compliant',
                'continuous_improvement' => 'Compliant',
                'documentation' => 'Compliant',
            ],
            // Add more ISO-specific metrics here
        ];
    }

    /**
     * Get date range based on period string
     */
    private function getDateRange(string $period): array
    {
        return match ($period) {
            '7days' => [now()->subDays(7), now()],
            '30days' => [now()->subDays(30), now()],
            '90days' => [now()->subDays(90), now()],
            '6months' => [now()->subMonths(6), now()],
            '12months' => [now()->subMonths(12), now()],
            'ytd' => [now()->startOfYear(), now()],
            default => [now()->subDays(30), now()],
        };
    }

    /**
     * Generate PDF report (placeholder - would use a PDF library like TCPDF or DomPDF)
     */
    private function generatePDF(Report $report, array $data): string
    {
        $filename = "reports/{$report->type}_{$report->id}_" . now()->format('Y-m-d_H-i-s') . '.pdf';
        
        // For now, create a simple text file as placeholder
        $content = "PDF Report: {$data['title']}\n";
        $content .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n\n";
        $content .= json_encode($data, JSON_PRETTY_PRINT);
        
        Storage::put($filename, $content);
        
        return $filename;
    }

    /**
     * Generate Excel report (placeholder - would use PhpSpreadsheet)
     */
    private function generateExcel(Report $report, array $data): string
    {
        $filename = "reports/{$report->type}_{$report->id}_" . now()->format('Y-m-d_H-i-s') . '.xlsx';
        
        // For now, create a simple CSV file as placeholder
        $content = "Excel Report: {$data['title']}\n";
        $content .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n\n";
        $content .= json_encode($data, JSON_PRETTY_PRINT);
        
        Storage::put($filename, $content);
        
        return $filename;
    }

    /**
     * Generate CSV report
     */
    private function generateCSV(Report $report, array $data): string
    {
        $filename = "reports/{$report->type}_{$report->id}_" . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $content = "CSV Report: {$data['title']}\n";
        $content .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n\n";
        
        // Convert data to CSV format (simplified)
        if (isset($data['complaints'])) {
            $content .= "Reference,Title,Status,Priority,Category,Submitted Date\n";
            foreach ($data['complaints'] as $complaint) {
                $content .= "\"{$complaint->complaint->reference_number}\",";
                $content .= "\"{$complaint->complaint->title}\",";
                $content .= "\"{$complaint->status}\",";
                $content .= "\"{$complaint->priority}\",";
                $content .= "\"{$complaint->category->name}\",";
                $content .= "\"{$complaint->submitted_date}\"\n";
            }
        }
        
        Storage::put($filename, $content);
        
        return $filename;
    }
}
