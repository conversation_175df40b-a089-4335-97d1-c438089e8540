<?php

namespace App\Filament\Widgets;

use App\Models\ComplaintAnalytics;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SatisfactionRatingWidget extends BaseWidget
{
    protected static ?int $sort = 8;

    protected function getHeading(): string
    {
        return 'Customer Satisfaction Metrics';
    }

    protected function getStats(): array
    {
        $satisfactionData = $this->getSatisfactionData();

        return [
            Stat::make('Average Rating', number_format($satisfactionData['average'], 1) . '/5')
                ->description($satisfactionData['total'] . ' responses')
                ->descriptionIcon('heroicon-m-star')
                ->color($this->getRatingColor($satisfactionData['average']))
                ->chart($this->generateRatingChart($satisfactionData['distribution'])),

            Stat::make('Satisfaction Rate', number_format($satisfactionData['satisfactionRate'], 1) . '%')
                ->description('Ratings 4-5 stars')
                ->descriptionIcon('heroicon-m-face-smile')
                ->color($satisfactionData['satisfactionRate'] >= 80 ? 'success' :
                       ($satisfactionData['satisfactionRate'] >= 60 ? 'warning' : 'danger')),

            Stat::make('Very Satisfied', $satisfactionData['distribution'][5] ?? 0)
                ->description('5-star ratings')
                ->descriptionIcon('heroicon-m-heart')
                ->color('success'),

            Stat::make('Dissatisfied', ($satisfactionData['distribution'][1] ?? 0) + ($satisfactionData['distribution'][2] ?? 0))
                ->description('1-2 star ratings')
                ->descriptionIcon('heroicon-m-face-frown')
                ->color('danger'),

            Stat::make('Response Rate', number_format($satisfactionData['responseRate'], 1) . '%')
                ->description('Of resolved complaints')
                ->descriptionIcon('heroicon-m-chat-bubble-left-right')
                ->color($satisfactionData['responseRate'] >= 70 ? 'success' :
                       ($satisfactionData['responseRate'] >= 50 ? 'warning' : 'danger')),

            Stat::make('NPS Score', $satisfactionData['nps'])
                ->description('Net Promoter Score')
                ->descriptionIcon('heroicon-m-chart-bar-square')
                ->color($satisfactionData['nps'] >= 50 ? 'success' :
                       ($satisfactionData['nps'] >= 0 ? 'warning' : 'danger')),
        ];
    }

    private function getSatisfactionData(): array
    {
        // Get satisfaction data for the last 3 months
        $startDate = now()->subMonths(3);

        // Get all resolved complaints in the period
        $resolvedComplaints = ComplaintAnalytics::where('submitted_date', '>=', $startDate)
            ->whereIn('status', ['resolved', 'closed'])
            ->count();

        // Get satisfaction ratings
        $satisfactionRatings = ComplaintAnalytics::where('submitted_date', '>=', $startDate)
            ->whereNotNull('satisfaction_rating')
            ->get();

        $total = $satisfactionRatings->count();
        $average = $total > 0 ? $satisfactionRatings->avg('satisfaction_rating') : 0;

        // Calculate distribution
        $distribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $distribution[$i] = $satisfactionRatings->where('satisfaction_rating', $i)->count();
        }

        // Calculate satisfaction rate (4-5 stars)
        $satisfied = ($distribution[4] ?? 0) + ($distribution[5] ?? 0);
        $satisfactionRate = $total > 0 ? ($satisfied / $total) * 100 : 0;

        // Calculate response rate
        $responseRate = $resolvedComplaints > 0 ? ($total / $resolvedComplaints) * 100 : 0;

        // Calculate NPS (Net Promoter Score)
        // Promoters (5 stars) - Detractors (1-2 stars)
        $promoters = $distribution[5] ?? 0;
        $detractors = ($distribution[1] ?? 0) + ($distribution[2] ?? 0);
        $nps = $total > 0 ? (($promoters - $detractors) / $total) * 100 : 0;

        return [
            'average' => $average,
            'total' => $total,
            'distribution' => $distribution,
            'satisfactionRate' => $satisfactionRate,
            'responseRate' => $responseRate,
            'nps' => round($nps),
        ];
    }

    private function getRatingColor(float $rating): string
    {
        if ($rating >= 4.5) return 'success';
        if ($rating >= 3.5) return 'warning';
        return 'danger';
    }

    private function generateRatingChart(array $distribution): array
    {
        // Generate a simple chart showing rating distribution
        $chart = [];
        for ($i = 1; $i <= 5; $i++) {
            $chart[] = $distribution[$i] ?? 0;
        }
        return $chart;
    }
}
