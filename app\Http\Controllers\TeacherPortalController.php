<?php

namespace App\Http\Controllers;

use App\Models\Complaint;
use App\Models\ComplaintComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TeacherPortalController extends Controller
{
    /**
     * Display teacher dashboard
     */
    public function dashboard()
    {
        $teacher = Auth::user();
        
        // Get assigned complaints
        $assignedComplaints = Complaint::where('assigned_to', $teacher->id)
            ->with(['category', 'submitter', 'comments'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        // Get statistics
        $stats = [
            'total_assigned' => Complaint::where('assigned_to', $teacher->id)->count(),
            'pending' => Complaint::where('assigned_to', $teacher->id)
                ->whereIn('status', ['assigned', 'in_progress'])->count(),
            'resolved_this_month' => Complaint::where('assigned_to', $teacher->id)
                ->where('status', 'resolved')
                ->whereMonth('resolution_date', now()->month)
                ->count(),
            'overdue' => Complaint::where('assigned_to', $teacher->id)
                ->where('due_date', '<', now())
                ->whereNotIn('status', ['resolved', 'closed'])
                ->count(),
        ];
        
        return view('teacher.dashboard', compact('assignedComplaints', 'stats'));
    }
    
    /**
     * Show specific complaint details
     */
    public function show(Complaint $complaint)
    {
        // Ensure teacher can only view assigned complaints
        if ($complaint->assigned_to !== Auth::id()) {
            abort(403, 'You can only view complaints assigned to you.');
        }
        
        $complaint->load(['category', 'submitter', 'comments.user', 'attachments', 'statusHistory.changedBy']);
        
        return view('teacher.complaints.show', compact('complaint'));
    }
    
    /**
     * Update complaint status
     */
    public function updateStatus(Request $request, Complaint $complaint)
    {
        // Ensure teacher can only update assigned complaints
        if ($complaint->assigned_to !== Auth::id()) {
            abort(403, 'You can only update complaints assigned to you.');
        }
        
        $request->validate([
            'status' => 'required|in:assigned,in_progress,on_hold,resolved,closed',
            'notes' => 'nullable|string|max:1000',
            'resolution' => 'required_if:status,resolved|nullable|string',
        ]);
        
        $oldStatus = $complaint->status;
        $complaint->status = $request->status;
        
        if ($request->status === 'resolved') {
            $complaint->resolution = $request->resolution;
            $complaint->resolution_date = now();
        }
        
        $complaint->save();
        
        // Record status change
        $complaint->statusHistory()->create([
            'status' => $request->status,
            'changed_by' => Auth::id(),
            'notes' => $request->notes,
        ]);
        
        // Send notification to student
        $complaint->submitter->notify(new \App\Notifications\ComplaintStatusChanged($complaint, $oldStatus));
        
        return redirect()->back()->with('success', 'Complaint status updated successfully.');
    }
    
    /**
     * Add comment to complaint
     */
    public function addComment(Request $request, Complaint $complaint)
    {
        // Ensure teacher can only comment on assigned complaints
        if ($complaint->assigned_to !== Auth::id()) {
            abort(403, 'You can only comment on complaints assigned to you.');
        }
        
        $request->validate([
            'comment' => 'required|string|max:2000',
            'is_internal' => 'boolean',
        ]);
        
        $comment = $complaint->comments()->create([
            'user_id' => Auth::id(),
            'comment' => $request->comment,
            'is_internal' => $request->boolean('is_internal', false),
        ]);
        
        // Notify student if comment is not internal
        if (!$comment->is_internal) {
            $complaint->submitter->notify(new \App\Notifications\NewCommentAdded($complaint, $comment));
        }
        
        return redirect()->back()->with('success', 'Comment added successfully.');
    }
    
    /**
     * Get complaints assigned to teacher (AJAX)
     */
    public function getAssignedComplaints(Request $request)
    {
        $query = Complaint::where('assigned_to', Auth::id())
            ->with(['category', 'submitter']);
        
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }
        
        if ($request->has('priority') && $request->priority !== 'all') {
            $query->where('priority', $request->priority);
        }
        
        $complaints = $query->orderBy('created_at', 'desc')->paginate(10);
        
        return response()->json($complaints);
    }
    
    /**
     * Bulk update complaints
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'complaint_ids' => 'required|array',
            'complaint_ids.*' => 'exists:complaints,id',
            'action' => 'required|in:mark_in_progress,mark_resolved,mark_closed',
        ]);
        
        $complaints = Complaint::whereIn('id', $request->complaint_ids)
            ->where('assigned_to', Auth::id())
            ->get();
        
        $status = match($request->action) {
            'mark_in_progress' => 'in_progress',
            'mark_resolved' => 'resolved',
            'mark_closed' => 'closed',
        };
        
        foreach ($complaints as $complaint) {
            $oldStatus = $complaint->status;
            $complaint->status = $status;
            
            if ($status === 'resolved') {
                $complaint->resolution_date = now();
            }
            
            $complaint->save();
            
            // Record status change
            $complaint->statusHistory()->create([
                'status' => $status,
                'changed_by' => Auth::id(),
                'notes' => "Bulk update: {$request->action}",
            ]);
            
            // Send notification
            $complaint->submitter->notify(new \App\Notifications\ComplaintStatusChanged($complaint, $oldStatus));
        }
        
        return redirect()->back()->with('success', count($complaints) . ' complaints updated successfully.');
    }
}
