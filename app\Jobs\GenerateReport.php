<?php

namespace App\Jobs;

use App\Models\Report;
use App\Services\ReportGeneratorService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class GenerateReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        private Report $report
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting report generation for: {$this->report->name}");

        try {
            $reportGenerator = new ReportGeneratorService();
            $filePath = $reportGenerator->generate($this->report);

            // Mark report as generated
            $this->report->markAsGenerated($filePath);

            // Send email if recipients are configured
            if ($this->report->recipients && count($this->report->recipients) > 0) {
                $this->sendReportEmail($filePath);
            }

            Log::info("Report generated successfully: {$this->report->name}");

        } catch (\Exception $e) {
            Log::error("Failed to generate report {$this->report->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Send report via email
     */
    private function sendReportEmail(string $filePath): void
    {
        try {
            Mail::send('emails.report', [
                'reportName' => $this->report->name,
                'reportDescription' => $this->report->description,
                'generatedAt' => now()->format('Y-m-d H:i:s'),
            ], function ($message) use ($filePath) {
                $message->to($this->report->recipients)
                    ->subject("Report: {$this->report->name}")
                    ->attach(storage_path('app/' . $filePath));
            });

            Log::info("Report email sent for: {$this->report->name}");
        } catch (\Exception $e) {
            Log::error("Failed to send report email for {$this->report->name}: " . $e->getMessage());
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Report generation job failed for {$this->report->name}: " . $exception->getMessage());
    }
}
