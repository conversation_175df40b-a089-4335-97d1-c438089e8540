<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complaint_analytics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('complaint_id')->unique()->constrained('complaints')->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained('complaint_categories');
            $table->string('priority');
            $table->string('status');
            $table->timestamp('submitted_date');
            $table->timestamp('assigned_date')->nullable();
            $table->timestamp('first_response_date')->nullable();
            $table->timestamp('resolution_date')->nullable();
            $table->timestamp('closed_date')->nullable();
            $table->timestamp('sla_due_date')->nullable();
            $table->decimal('response_time_hours', 8, 2)->nullable();
            $table->decimal('resolution_time_hours', 8, 2)->nullable();
            $table->decimal('total_time_hours', 8, 2)->nullable();
            $table->integer('escalation_count')->default(0);
            $table->integer('reassignment_count')->default(0);
            $table->integer('comment_count')->default(0);
            $table->integer('satisfaction_rating')->nullable();
            $table->boolean('sla_breached')->default(false);
            $table->decimal('cost_estimate', 10, 2)->nullable();
            $table->string('submitter_type')->nullable();
            $table->foreignId('resolver_id')->nullable()->constrained('users');
            $table->timestamps();

            // Indexes for analytics queries
            $table->index('submitted_date');
            $table->index('resolution_date');
            $table->index('priority');
            $table->index('status');
            $table->index('category_id');
            $table->index('sla_breached');
            $table->index('satisfaction_rating');
            $table->index(['submitted_date', 'category_id']);
            $table->index(['submitted_date', 'priority']);
            $table->index(['submitted_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complaint_analytics');
    }
};
