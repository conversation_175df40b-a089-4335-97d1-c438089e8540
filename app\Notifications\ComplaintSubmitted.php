<?php

namespace App\Notifications;

use App\Models\Complaint;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintSubmitted extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Complaint $complaint
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Complaint Submitted Successfully')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your complaint has been submitted successfully.')
            ->line('**Reference Number:** ' . $this->complaint->reference_number)
            ->line('**Title:** ' . $this->complaint->title)
            ->line('**Category:** ' . $this->complaint->category->name)
            ->line('**Priority:** ' . ucfirst($this->complaint->priority))
            ->line('We will review your complaint and respond within the specified timeframe.')
            ->action('View Complaint', url('/student/complaints/' . $this->complaint->id))
            ->line('Thank you for bringing this matter to our attention.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'complaint_id' => $this->complaint->id,
            'reference_number' => $this->complaint->reference_number,
            'title' => $this->complaint->title,
            'message' => 'Your complaint has been submitted successfully.',
            'action_url' => url('/student/complaints/' . $this->complaint->id),
        ];
    }
}
