<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SLASetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'priority',
        'resolution_days',
        'first_response_hours',
        'escalation_days',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get SLA setting for a specific priority
     */
    public static function getForPriority(string $priority): ?self
    {
        return self::where('priority', $priority)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Calculate due date based on this SLA setting
     */
    public function calculateDueDate(\Carbon\Carbon $startDate): \Carbon\Carbon
    {
        return $startDate->addDays($this->resolution_days);
    }

    /**
     * Calculate first response due date
     */
    public function calculateFirstResponseDue(\Carbon\Carbon $startDate): \Carbon\Carbon
    {
        return $startDate->addHours($this->first_response_hours);
    }

    /**
     * Calculate escalation date
     */
    public function calculateEscalationDate(\Carbon\Carbon $startDate): \Carbon\Carbon
    {
        return $startDate->addDays($this->escalation_days);
    }

    /**
     * Check if SLA is breached for a given date
     */
    public function isBreached(\Carbon\Carbon $startDate, ?\Carbon\Carbon $completedDate = null): bool
    {
        $dueDate = $this->calculateDueDate($startDate);
        $checkDate = $completedDate ?? now();

        return $checkDate->isAfter($dueDate);
    }

    /**
     * Get time remaining until SLA breach
     */
    public function getTimeRemaining(\Carbon\Carbon $startDate): array
    {
        $dueDate = $this->calculateDueDate($startDate);
        $now = now();

        if ($now->isAfter($dueDate)) {
            return [
                'overdue' => true,
                'days' => $now->diffInDays($dueDate),
                'hours' => $now->diffInHours($dueDate) % 24,
                'minutes' => $now->diffInMinutes($dueDate) % 60,
            ];
        }

        return [
            'overdue' => false,
            'days' => $dueDate->diffInDays($now),
            'hours' => $dueDate->diffInHours($now) % 24,
            'minutes' => $dueDate->diffInMinutes($now) % 60,
        ];
    }
}
