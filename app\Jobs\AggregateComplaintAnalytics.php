<?php

namespace App\Jobs;

use App\Models\Complaint;
use App\Models\ComplaintAnalytics;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AggregateComplaintAnalytics implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        private ?int $complaintId = null
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting complaint analytics aggregation...');

        if ($this->complaintId) {
            // Update analytics for specific complaint
            $complaint = Complaint::find($this->complaintId);
            if ($complaint) {
                ComplaintAnalytics::updateFromComplaint($complaint);
                Log::info("Updated analytics for complaint {$complaint->reference_number}");
            }
        } else {
            // Update analytics for all complaints modified in the last 24 hours
            $complaints = Complaint::where('updated_at', '>=', now()->subDay())
                ->with(['category', 'submitter', 'assignee', 'comments', 'escalations', 'assignments'])
                ->get();

            $updatedCount = 0;
            foreach ($complaints as $complaint) {
                try {
                    ComplaintAnalytics::updateFromComplaint($complaint);
                    $updatedCount++;
                } catch (\Exception $e) {
                    Log::error("Failed to update analytics for complaint {$complaint->reference_number}: " . $e->getMessage());
                }
            }

            Log::info("Updated analytics for {$updatedCount} complaints");
        }

        // Clean up old analytics data (older than 2 years)
        $this->cleanupOldData();

        Log::info('Complaint analytics aggregation completed');
    }

    /**
     * Clean up old analytics data
     */
    private function cleanupOldData(): void
    {
        $deletedCount = ComplaintAnalytics::where('submitted_date', '<', now()->subYears(2))->delete();
        
        if ($deletedCount > 0) {
            Log::info("Cleaned up {$deletedCount} old analytics records");
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Complaint analytics aggregation failed: ' . $exception->getMessage());
    }
}
