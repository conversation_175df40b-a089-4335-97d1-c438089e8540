<?php

namespace App\Filament\Admin\Resources\SLASettingResource\Pages;

use App\Filament\Admin\Resources\SLASettingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSLASetting extends EditRecord
{
    protected static string $resource = SLASettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
