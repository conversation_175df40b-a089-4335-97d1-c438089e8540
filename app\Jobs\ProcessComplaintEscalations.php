<?php

namespace App\Jobs;

use App\Models\Complaint;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessComplaintEscalations implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Processing complaint escalations...');
        
        // Get complaints that should be auto-escalated
        $complaintsToEscalate = Complaint::whereNotIn('status', ['resolved', 'closed'])
            ->where(function ($query) {
                $query->where('due_date', '<', now()->subDays(1))
                      ->orWhere(function ($subQuery) {
                          $subQuery->whereNull('assigned_to')
                                   ->where('created_at', '<', now()->subHours(4));
                      });
            })
            ->get();

        $escalatedCount = 0;
        
        foreach ($complaintsToEscalate as $complaint) {
            if ($this->shouldEscalateComplaint($complaint)) {
                $this->escalateComplaint($complaint);
                $escalatedCount++;
            }
        }
        
        Log::info("Escalated {$escalatedCount} complaints");
    }

    /**
     * Check if complaint should be escalated
     */
    private function shouldEscalateComplaint(Complaint $complaint): bool
    {
        // Don't escalate if already at max level
        if ($complaint->getCurrentEscalationLevel() >= 4) {
            return false;
        }
        
        // Don't escalate if recently escalated (within 24 hours)
        $latestEscalation = $complaint->getLatestEscalation();
        if ($latestEscalation && $latestEscalation->escalated_at->isAfter(now()->subDay())) {
            return false;
        }
        
        // Escalate if overdue
        if ($complaint->isOverdue()) {
            return true;
        }
        
        // Escalate if unassigned for too long
        if (!$complaint->assigned_to && $complaint->created_at->isBefore(now()->subHours(4))) {
            return true;
        }
        
        // Escalate critical priority complaints if not resolved within 2 hours
        if ($complaint->priority === 'critical' && 
            $complaint->created_at->isBefore(now()->subHours(2)) &&
            $complaint->status !== 'in_progress') {
            return true;
        }
        
        return false;
    }

    /**
     * Escalate a complaint
     */
    private function escalateComplaint(Complaint $complaint): void
    {
        $currentLevel = $complaint->getCurrentEscalationLevel();
        $nextLevel = $currentLevel + 1;
        
        // Find appropriate escalation target
        $escalationTarget = $this->findEscalationTarget($nextLevel, $complaint);
        
        if (!$escalationTarget) {
            Log::warning("No escalation target found for complaint {$complaint->reference_number} at level {$nextLevel}");
            return;
        }
        
        // Determine escalation reason
        $reason = $this->getEscalationReason($complaint);
        
        try {
            $complaint->escalate(
                escalatedTo: $escalationTarget->id,
                reason: $reason,
                escalatedBy: null, // System escalation
                level: $nextLevel
            );
            
            Log::info("Auto-escalated complaint {$complaint->reference_number} to level {$nextLevel}");
        } catch (\Exception $e) {
            Log::error("Failed to escalate complaint {$complaint->reference_number}: " . $e->getMessage());
        }
    }

    /**
     * Find appropriate escalation target based on level
     */
    private function findEscalationTarget(int $level, Complaint $complaint): ?User
    {
        // Define escalation hierarchy
        $escalationRoles = [
            1 => ['teacher', 'admin'],
            2 => ['admin'],
            3 => ['admin'], // Could be manager role if implemented
            4 => ['admin'], // Could be director role if implemented
        ];
        
        $roles = $escalationRoles[$level] ?? ['admin'];
        
        // Find user with least current workload
        return User::role($roles)
            ->get()
            ->sortBy(function ($user) {
                return $user->assignedComplaints()
                    ->whereNotIn('status', ['resolved', 'closed'])
                    ->count();
            })
            ->first();
    }

    /**
     * Get escalation reason based on complaint state
     */
    private function getEscalationReason(Complaint $complaint): string
    {
        if ($complaint->isOverdue()) {
            $daysPastDue = abs($complaint->getDaysUntilDue());
            return "Automatic escalation: Complaint is {$daysPastDue} day(s) overdue";
        }
        
        if (!$complaint->assigned_to) {
            $hoursUnassigned = $complaint->created_at->diffInHours(now());
            return "Automatic escalation: Complaint unassigned for {$hoursUnassigned} hours";
        }
        
        if ($complaint->priority === 'critical') {
            return "Automatic escalation: Critical priority complaint requires immediate attention";
        }
        
        return "Automatic escalation: SLA threshold exceeded";
    }
}
