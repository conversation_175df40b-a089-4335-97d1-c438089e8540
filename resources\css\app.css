@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Status badges */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-badge-new {
    @apply bg-gray-100 text-gray-800;
  }

  .status-badge-assigned {
    @apply bg-blue-100 text-blue-800;
  }

  .status-badge-in-progress {
    @apply bg-indigo-100 text-indigo-800;
  }

  .status-badge-on-hold {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-badge-resolved {
    @apply bg-green-100 text-green-800;
  }

  .status-badge-closed {
    @apply bg-green-100 text-green-800;
  }

  .status-badge-reopened {
    @apply bg-red-100 text-red-800;
  }

  /* Priority badges */
  .priority-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .priority-badge-low {
    @apply bg-gray-100 text-gray-800;
  }

  .priority-badge-medium {
    @apply bg-blue-100 text-blue-800;
  }

  .priority-badge-high {
    @apply bg-yellow-100 text-yellow-800;
  }

  .priority-badge-critical {
    @apply bg-red-100 text-red-800;
  }

  /* Card components */
  .complaint-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow duration-300;
  }

  .complaint-card-header {
    @apply p-4 border-b border-gray-200;
  }

  .complaint-card-footer {
    @apply px-4 py-3 bg-gray-50;
  }

  /* Form components */
  .form-input {
    @apply shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md;
  }

  .form-select {
    @apply mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md;
  }

  .form-textarea {
    @apply shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border border-gray-300 rounded-md;
  }

  .form-checkbox {
    @apply focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded;
  }

  .form-radio {
    @apply focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300;
  }

  /* Button components */
  .btn {
    @apply inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500;
  }

  .btn-success {
    @apply border-transparent text-white bg-green-600 hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
  }

  .btn-warning {
    @apply border-transparent text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }
}
