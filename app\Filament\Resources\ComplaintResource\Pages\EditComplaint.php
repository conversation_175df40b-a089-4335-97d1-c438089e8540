<?php

namespace App\Filament\Resources\ComplaintResource\Pages;

use App\Filament\Resources\ComplaintResource;
use App\Models\ComplaintAttachment;
use App\Models\ComplaintStatusHistory;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditComplaint extends EditRecord
{
    protected static string $resource = ComplaintResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\Action::make('view')
                ->url(fn () => ComplaintResource::getUrl('view', ['record' => $this->record]))
                ->color('success')
                ->icon('heroicon-o-eye'),
        ];
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $oldStatus = $record->status;
        $newStatus = $data['status'];
        
        // Update the record
        $record = parent::handleRecordUpdate($record, $data);
        
        // Record status change if status has changed
        if ($oldStatus !== $newStatus) {
            ComplaintStatusHistory::create([
                'complaint_id' => $record->id,
                'status' => $newStatus,
                'changed_by' => auth()->id(),
                'notes' => "Status changed from {$oldStatus} to {$newStatus}",
            ]);
        }
        
        // Handle attachments if any
        if (isset($data['attachments']) && is_array($data['attachments'])) {
            foreach ($data['attachments'] as $attachment) {
                ComplaintAttachment::create([
                    'complaint_id' => $record->id,
                    'file_name' => $attachment->getClientOriginalName(),
                    'file_path' => $attachment->store('complaint-attachments'),
                    'file_type' => $attachment->getMimeType(),
                    'file_size' => $attachment->getSize(),
                    'uploaded_by' => auth()->id(),
                ]);
            }
        }
        
        return $record;
    }
}
