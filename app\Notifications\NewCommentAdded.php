<?php

namespace App\Notifications;

use App\Models\Complaint;
use App\Models\ComplaintComment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewCommentAdded extends Notification implements ShouldQueue
{
    use Queueable;

    protected $complaint;
    protected $comment;

    /**
     * Create a new notification instance.
     */
    public function __construct(Complaint $complaint, ComplaintComment $comment)
    {
        $this->complaint = $complaint;
        $this->comment = $comment;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        $channels = ['database'];
        
        if (config('notifications.email_enabled', true)) {
            $channels[] = 'mail';
        }
        
        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("New Comment on Your Complaint - {$this->complaint->reference_number}")
            ->greeting("Hello {$notifiable->name},")
            ->line("A new comment has been added to your complaint.")
            ->line("**Complaint:** {$this->complaint->title}")
            ->line("**Reference:** {$this->complaint->reference_number}")
            ->line("**Comment by:** {$this->comment->user->name}")
            ->line("**Comment:** {$this->comment->comment}")
            ->action('View Complaint', route('student.complaints.show', $this->complaint))
            ->line('Thank you for using our complaints management system!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'complaint_id' => $this->complaint->id,
            'complaint_reference' => $this->complaint->reference_number,
            'complaint_title' => $this->complaint->title,
            'comment_id' => $this->comment->id,
            'commenter_name' => $this->comment->user->name,
            'comment' => $this->comment->comment,
            'message' => "{$this->comment->user->name} added a comment to your complaint '{$this->complaint->title}'",
            'action_url' => route('student.complaints.show', $this->complaint),
        ];
    }
}
