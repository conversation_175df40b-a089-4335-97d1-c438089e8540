<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Complaint;
use App\Models\ComplaintCategory;
use App\Models\ComplaintComment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ComplaintController extends Controller
{
    /**
     * Display a listing of complaints for teachers.
     */
    public function index(Request $request)
    {
        $query = Complaint::with(['category', 'submitter', 'assignee']);

        // Filter by assignment
        if ($request->filter === 'assigned') {
            $query->where('assigned_to', Auth::id());
        } elseif ($request->filter === 'unassigned') {
            $query->whereNull('assigned_to');
        } elseif ($request->filter === 'all') {
            // Show all complaints for teachers
        } else {
            // Default: show assigned complaints
            $query->where('assigned_to', Auth::id());
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->priority) {
            $query->where('priority', $request->priority);
        }

        // Search
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('reference_number', 'like', '%' . $request->search . '%');
            });
        }

        $complaints = $query->latest()->paginate(15);

        $categories = ComplaintCategory::where('is_active', true)->get();
        $statuses = Complaint::STATUSES;
        $priorities = Complaint::PRIORITIES;

        return view('teacher.complaints.index', compact(
            'complaints', 'categories', 'statuses', 'priorities'
        ));
    }

    /**
     * Display the specified complaint.
     */
    public function show(Complaint $complaint)
    {
        $complaint->load([
            'category', 
            'submitter', 
            'assignee', 
            'comments.user',
            'statusHistory.changedBy',
            'escalations.escalatedBy',
            'escalations.escalatedTo'
        ]);

        return view('teacher.complaints.show', compact('complaint'));
    }

    /**
     * Assign complaint to self or another user
     */
    public function assign(Request $request, Complaint $complaint)
    {
        $request->validate([
            'assigned_to' => 'required|exists:users,id',
            'reason' => 'nullable|string|max:500',
        ]);

        $assignedTo = $request->assigned_to;
        $reason = $request->reason ?? 'Complaint assigned by teacher';

        // Check if user has permission to assign
        $assignee = User::find($assignedTo);
        if (!$assignee->hasAnyRole(['teacher', 'admin'])) {
            return back()->withErrors(['assigned_to' => 'Can only assign to teachers or admins.']);
        }

        $complaint->assignTo($assignedTo, $reason, Auth::id());

        return back()->with('success', 'Complaint assigned successfully.');
    }

    /**
     * Update complaint status
     */
    public function updateStatus(Request $request, Complaint $complaint)
    {
        $request->validate([
            'status' => 'required|in:' . implode(',', array_keys(Complaint::STATUSES)),
            'notes' => 'nullable|string|max:1000',
        ]);

        if (!$complaint->canTransitionTo($request->status)) {
            return back()->withErrors(['status' => 'Invalid status transition.']);
        }

        $complaint->transitionTo($request->status, Auth::id(), $request->notes);

        return back()->with('success', 'Status updated successfully.');
    }

    /**
     * Add comment to complaint
     */
    public function addComment(Request $request, Complaint $complaint)
    {
        $request->validate([
            'comment' => 'required|string|max:2000',
            'is_internal' => 'boolean',
        ]);

        ComplaintComment::create([
            'complaint_id' => $complaint->id,
            'user_id' => Auth::id(),
            'comment' => $request->comment,
            'is_internal' => $request->boolean('is_internal'),
        ]);

        // Notify relevant parties
        if (!$request->boolean('is_internal')) {
            // Notify submitter if comment is not internal
            if ($complaint->submitter && $complaint->submitter->id !== Auth::id()) {
                $complaint->submitter->notify(
                    new \App\Notifications\ComplaintCommentAdded($complaint, $request->comment)
                );
            }
        }

        return back()->with('success', 'Comment added successfully.');
    }

    /**
     * Resolve complaint
     */
    public function resolve(Request $request, Complaint $complaint)
    {
        $request->validate([
            'resolution' => 'required|string|max:2000',
        ]);

        $complaint->update([
            'status' => 'resolved',
            'resolution' => $request->resolution,
            'resolved_at' => now(),
        ]);

        // Add resolution as a comment
        ComplaintComment::create([
            'complaint_id' => $complaint->id,
            'user_id' => Auth::id(),
            'comment' => 'Resolution: ' . $request->resolution,
            'is_internal' => false,
        ]);

        // Notify submitter
        if ($complaint->submitter) {
            $complaint->submitter->notify(
                new \App\Notifications\ComplaintStatusChanged($complaint, $complaint->status, 'resolved')
            );
        }

        return back()->with('success', 'Complaint resolved successfully.');
    }

    /**
     * Escalate complaint
     */
    public function escalate(Request $request, Complaint $complaint)
    {
        $request->validate([
            'escalated_to' => 'required|exists:users,id',
            'reason' => 'required|string|max:500',
        ]);

        $escalatedTo = User::find($request->escalated_to);
        if (!$escalatedTo->hasRole('admin')) {
            return back()->withErrors(['escalated_to' => 'Can only escalate to administrators.']);
        }

        $complaint->escalate($request->escalated_to, $request->reason, Auth::id());

        return back()->with('success', 'Complaint escalated successfully.');
    }

    /**
     * Take ownership of unassigned complaint
     */
    public function takeOwnership(Complaint $complaint)
    {
        if ($complaint->assigned_to) {
            return back()->withErrors(['error' => 'Complaint is already assigned.']);
        }

        $complaint->assignTo(Auth::id(), 'Teacher took ownership of complaint', Auth::id());

        return back()->with('success', 'You have taken ownership of this complaint.');
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:assign,status_change,priority_change',
            'complaint_ids' => 'required|array',
            'complaint_ids.*' => 'exists:complaints,id',
            'assigned_to' => 'required_if:action,assign|exists:users,id',
            'status' => 'required_if:action,status_change|in:' . implode(',', array_keys(Complaint::STATUSES)),
            'priority' => 'required_if:action,priority_change|in:' . implode(',', array_keys(Complaint::PRIORITIES)),
        ]);

        $complaints = Complaint::whereIn('id', $request->complaint_ids)->get();
        $successCount = 0;

        foreach ($complaints as $complaint) {
            try {
                switch ($request->action) {
                    case 'assign':
                        $complaint->assignTo($request->assigned_to, 'Bulk assignment', Auth::id());
                        $successCount++;
                        break;
                    case 'status_change':
                        if ($complaint->canTransitionTo($request->status)) {
                            $complaint->transitionTo($request->status, Auth::id(), 'Bulk status update');
                            $successCount++;
                        }
                        break;
                    case 'priority_change':
                        $complaint->update(['priority' => $request->priority]);
                        $successCount++;
                        break;
                }
            } catch (\Exception $e) {
                // Log error but continue with other complaints
                \Log::error('Bulk action failed for complaint ' . $complaint->id . ': ' . $e->getMessage());
            }
        }

        return back()->with('success', "Successfully processed {$successCount} complaints.");
    }
}
