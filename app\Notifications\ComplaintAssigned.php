<?php

namespace App\Notifications;

use App\Models\Complaint;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintAssigned extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Complaint $complaint
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Complaint Assigned to You')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('A complaint has been assigned to you for review and resolution.')
            ->line('**Reference Number:** ' . $this->complaint->reference_number)
            ->line('**Title:** ' . $this->complaint->title)
            ->line('**Category:** ' . $this->complaint->category->name)
            ->line('**Priority:** ' . ucfirst($this->complaint->priority))
            ->line('**Submitted by:** ' . $this->complaint->submitter->name)
            ->line('**Due Date:** ' . $this->complaint->due_date?->format('M d, Y'))
            ->action('View Complaint', url('/admin/complaints/' . $this->complaint->id))
            ->line('Please review and take appropriate action on this complaint.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'complaint_id' => $this->complaint->id,
            'reference_number' => $this->complaint->reference_number,
            'title' => $this->complaint->title,
            'message' => 'A complaint has been assigned to you.',
            'action_url' => url('/admin/complaints/' . $this->complaint->id),
        ];
    }
}
