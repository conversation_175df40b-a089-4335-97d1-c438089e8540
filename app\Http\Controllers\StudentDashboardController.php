<?php

namespace App\Http\Controllers;

use App\Models\Complaint;
use App\Models\ComplaintCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StudentDashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        
        // Get student's complaint statistics
        $totalComplaints = Complaint::where('submitted_by', $user->id)->count();
        $newComplaints = Complaint::where('submitted_by', $user->id)->where('status', 'new')->count();
        $inProgressComplaints = Complaint::where('submitted_by', $user->id)->where('status', 'in_progress')->count();
        $resolvedComplaints = Complaint::where('submitted_by', $user->id)->where('status', 'resolved')->count();
        
        // Get recent complaints
        $recentComplaints = Complaint::where('submitted_by', $user->id)
            ->with(['category', 'assignee'])
            ->latest()
            ->limit(5)
            ->get();
        
        // Get unread notifications
        $unreadNotifications = $user->unreadNotifications()->limit(5)->get();
        
        // Get active categories for quick submission
        $categories = ComplaintCategory::where('is_active', true)->get();
        
        return view('student.dashboard', compact(
            'totalComplaints',
            'newComplaints', 
            'inProgressComplaints',
            'resolvedComplaints',
            'recentComplaints',
            'unreadNotifications',
            'categories'
        ));
    }
    
    public function quickSubmit(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'category_id' => 'required|exists:complaint_categories,id',
            'description' => 'required|string|min:10',
        ]);

        $complaint = Complaint::create([
            'reference_number' => Complaint::generateReferenceNumber(),
            'title' => $request->title,
            'description' => $request->description,
            'category_id' => $request->category_id,
            'submitted_by' => Auth::id(),
            'status' => 'new',
            'priority' => 'medium',
        ]);

        // Record status history
        $complaint->statusHistory()->create([
            'status' => 'new',
            'changed_by' => Auth::id(),
            'notes' => 'Complaint submitted via quick form',
        ]);

        // Send notification
        $complaint->submitter->notify(new \App\Notifications\ComplaintSubmitted($complaint));

        return redirect()->route('student.dashboard')
            ->with('success', 'Complaint submitted successfully. Reference: ' . $complaint->reference_number);
    }
    
    public function markNotificationAsRead($id)
    {
        $notification = Auth::user()->notifications()->find($id);
        
        if ($notification) {
            $notification->markAsRead();
        }
        
        return response()->json(['success' => true]);
    }
    
    public function markAllNotificationsAsRead()
    {
        Auth::user()->unreadNotifications->markAsRead();
        
        return response()->json(['success' => true]);
    }
}
