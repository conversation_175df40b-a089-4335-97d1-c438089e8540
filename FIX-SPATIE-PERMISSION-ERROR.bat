@echo off
echo ========================================
echo FIXING SPATIE PERMISSION ERROR
echo ========================================

echo Step 1: Stopping all PHP processes...
taskkill /f /im php.exe 2>nul

echo Step 2: Clearing ALL cache and config files...
if exist bootstrap\cache rmdir /s /q bootstrap\cache 2>nul
if exist storage\framework\cache rmdir /s /q storage\framework\cache 2>nul
if exist storage\framework\sessions rmdir /s /q storage\framework\sessions 2>nul
if exist storage\framework\views rmdir /s /q storage\framework\views 2>nul

echo Step 3: Recreating directories...
mkdir bootstrap\cache 2>nul
mkdir storage\framework\cache 2>nul
mkdir storage\framework\cache\data 2>nul
mkdir storage\framework\sessions 2>nul
mkdir storage\framework\views 2>nul
mkdir storage\logs 2>nul

echo Step 4: Creating minimal .env without cache...
(
echo APP_NAME="Complaints Management System"
echo APP_ENV=local
echo APP_KEY=base64:SGVsbG9Xb3JsZEhlbGxvV29ybGRIZWxsb1dvcmxkSGVsbG9Xb3JsZA==
echo APP_DEBUG=true
echo APP_URL=http://localhost
echo.
echo LOG_CHANNEL=stack
echo LOG_LEVEL=debug
echo.
echo DB_CONNECTION=sqlite
echo.
echo BROADCAST_DRIVER=log
echo CACHE_DRIVER=file
echo FILESYSTEM_DISK=local
echo QUEUE_CONNECTION=sync
echo SESSION_DRIVER=file
echo SESSION_LIFETIME=120
echo.
echo MAIL_MAILER=log
echo MAIL_FROM_ADDRESS="<EMAIL>"
echo MAIL_FROM_NAME="${APP_NAME}"
echo.
echo PERMISSION_CACHE_ENABLED=false
) > .env

echo Step 5: Creating fresh database...
if exist database\database.sqlite del database\database.sqlite
echo. > database\database.sqlite

echo Step 6: Running migrations WITHOUT cache...
set CACHE_DRIVER=array
php artisan migrate:fresh --force --no-interaction

echo Step 7: Creating roles and permissions manually...
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

use Spatie\\Permission\\Models\\Role;
use Spatie\\Permission\\Models\\Permission;

try {
    // Clear permission cache
    app()['cache']->forget('spatie.permission.cache');
    
    // Create roles
    Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
    Role::firstOrCreate(['name' => 'teacher', 'guard_name' => 'web']);
    Role::firstOrCreate(['name' => 'student', 'guard_name' => 'web']);
    
    echo 'Roles created successfully!\n';
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . '\n';
}
"

echo Step 8: Creating users...
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

use App\\Models\\User;
use Illuminate\\Support\\Facades\\Hash;

try {
    // Create admin
    \$admin = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Admin User',
            'password' => Hash::make('123456'),
            'email_verified_at' => now(),
            'department' => 'IT',
            'user_type' => 'admin',
        ]
    );
    \$admin->assignRole('admin');
    
    // Create student
    \$student = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Student User',
            'password' => Hash::make('123456'),
            'email_verified_at' => now(),
            'department' => 'Computer Science',
            'user_type' => 'student',
        ]
    );
    \$student->assignRole('student');
    
    echo 'Users created successfully!\n';
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . '\n';
}
"

echo Step 9: Final cleanup...
php artisan config:clear 2>nul
php artisan cache:clear 2>nul

echo.
echo ========================================
echo SPATIE PERMISSION ERROR FIXED!
echo ========================================
echo.
echo The permission system has been properly initialized.
echo.
echo Access: http://localhost:8000/login
echo.
echo Credentials:
echo • Admin: <EMAIL> / 123456
echo • Student: <EMAIL> / 123456
echo.
echo Starting server...
php artisan serve
