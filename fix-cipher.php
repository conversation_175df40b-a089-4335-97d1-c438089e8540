<?php

echo "🔧 Fixing cipher and encryption key issues...\n";

// Step 1: Create a proper .env file with correct structure
$envContent = 'APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
';

// Backup existing .env if it exists
if (file_exists('.env')) {
    copy('.env', '.env.backup');
    echo "✅ Backed up existing .env file\n";
}

// Write new .env
file_put_contents('.env', $envContent);
echo "✅ Created fresh .env file\n";

// Step 2: Generate a proper <PERSON><PERSON> encryption key
// <PERSON><PERSON> uses AES-256-CBC by default, so we need a 32-byte key
$key = random_bytes(32);
$encodedKey = 'base64:' . base64_encode($key);

// Update the .env file with the new key
$envContent = str_replace('APP_KEY=', "APP_KEY={$encodedKey}", $envContent);
file_put_contents('.env', $envContent);
echo "✅ Generated proper AES-256-CBC encryption key\n";

// Step 3: Verify the key format
echo "🔍 Generated key: {$encodedKey}\n";
echo "🔍 Key length: " . strlen($key) . " bytes (should be 32)\n";

// Step 4: Create required directories
$dirs = [
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    }
}

// Step 5: Remove problematic cache files
$cacheFiles = [
    'bootstrap/cache/config.php',
    'bootstrap/cache/routes-v7.php', 
    'bootstrap/cache/services.php',
    'bootstrap/cache/packages.php'
];

foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "✅ Removed cache file: $file\n";
    }
}

// Step 6: Create database file
$dbFile = 'database/database.sqlite';
if (!is_dir('database')) {
    mkdir('database', 0755, true);
}

if (file_exists($dbFile)) {
    unlink($dbFile);
}
file_put_contents($dbFile, '');
echo "✅ Created fresh database file\n";

// Step 7: Test the encryption key
try {
    // Simulate Laravel's encryption test
    $cipher = 'AES-256-CBC';
    $testData = 'test encryption';
    $iv = random_bytes(16);
    
    $encrypted = openssl_encrypt($testData, $cipher, $key, 0, $iv);
    $decrypted = openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
    
    if ($decrypted === $testData) {
        echo "✅ Encryption key test passed\n";
    } else {
        echo "❌ Encryption key test failed\n";
    }
} catch (Exception $e) {
    echo "❌ Encryption test error: " . $e->getMessage() . "\n";
}

echo "\n🎉 Cipher fix complete!\n";
echo "The APP_KEY has been properly generated for AES-256-CBC cipher.\n";
echo "\nNext steps:\n";
echo "1. php artisan config:clear\n";
echo "2. php artisan migrate:fresh --seed\n";
echo "3. php artisan serve\n";
