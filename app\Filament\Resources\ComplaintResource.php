<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ComplaintResource\Pages;
use App\Filament\Resources\ComplaintResource\RelationManagers;
use App\Models\Complaint;
use App\Models\ComplaintCategory;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Notifications\Notification;

class ComplaintResource extends Resource
{
    protected static ?string $model = Complaint::class;

    protected static ?string $navigationIcon = 'heroicon-o-exclamation-triangle';

    protected static ?string $navigationGroup = 'Complaint Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Complaint Information')
                            ->schema([
                                Forms\Components\TextInput::make('reference_number')
                                    ->default(fn () => Complaint::generateReferenceNumber())
                                    ->disabled()
                                    ->dehydrated()
                                    ->required(),
                                Forms\Components\TextInput::make('title')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Select::make('category_id')
                                    ->label('Category')
                                    ->options(ComplaintCategory::where('is_active', true)->pluck('name', 'id'))
                                    ->required()
                                    ->searchable(),
                                Forms\Components\Select::make('priority')
                                    ->options(Complaint::PRIORITIES)
                                    ->default('medium')
                                    ->required(),
                                Forms\Components\Textarea::make('description')
                                    ->required()
                                    ->rows(5),
                                Forms\Components\FileUpload::make('attachments')
                                    ->label('Attachments')
                                    ->multiple()
                                    ->directory('complaint-attachments')
                                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                                    ->maxSize(10240) // 10MB
                                    ->helperText('Upload files (JPG, PNG, PDF, DOC, DOCX) up to 10MB')
                                    ->columnSpanFull(),
                            ])
                            ->columns(2),

                        Forms\Components\Section::make('Assignment & Status')
                            ->schema([
                                Forms\Components\Select::make('status')
                                    ->options(Complaint::STATUSES)
                                    ->default('new')
                                    ->required(),
                                Forms\Components\Select::make('submitted_by')
                                    ->label('Submitted By')
                                    ->options(User::role('student')->pluck('name', 'id'))
                                    ->required()
                                    ->searchable(),
                                Forms\Components\Select::make('assigned_to')
                                    ->label('Assigned To')
                                    ->options(User::role(['admin', 'teacher'])->pluck('name', 'id'))
                                    ->searchable(),
                                Forms\Components\DateTimePicker::make('due_date')
                                    ->label('Due Date')
                                    ->default(function () {
                                        // Default to 7 days from now
                                        return now()->addDays(7);
                                    }),
                            ])
                            ->columns(2),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Resolution')
                            ->schema([
                                Forms\Components\Textarea::make('resolution')
                                    ->rows(5),
                                Forms\Components\DateTimePicker::make('resolution_date'),
                            ]),

                        Forms\Components\Section::make('Satisfaction')
                            ->schema([
                                Forms\Components\Radio::make('satisfaction_rating')
                                    ->options([
                                        1 => 'Very Dissatisfied',
                                        2 => 'Dissatisfied',
                                        3 => 'Neutral',
                                        4 => 'Satisfied',
                                        5 => 'Very Satisfied',
                                    ])
                                    ->inline(),
                                Forms\Components\Textarea::make('satisfaction_comment')
                                    ->rows(3),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('reference_number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                Tables\Columns\TextColumn::make('category.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'new' => 'gray',
                        'assigned' => 'blue',
                        'in_progress' => 'indigo',
                        'on_hold' => 'yellow',
                        'resolved' => 'green',
                        'closed' => 'green',
                        'reopened' => 'red',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('priority')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'low' => 'gray',
                        'medium' => 'blue',
                        'high' => 'yellow',
                        'critical' => 'red',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('submitter.name')
                    ->label('Submitted By')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('assignee.name')
                    ->label('Assigned To')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Unassigned'),
                Tables\Columns\TextColumn::make('due_date')
                    ->dateTime()
                    ->sortable()
                    ->color(fn (Complaint $record) =>
                        $record->due_date && $record->due_date < now() && !in_array($record->status, ['resolved', 'closed'])
                            ? 'danger'
                            : 'success'
                    ),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(Complaint::STATUSES),
                Tables\Filters\SelectFilter::make('priority')
                    ->options(Complaint::PRIORITIES),
                Tables\Filters\SelectFilter::make('category')
                    ->relationship('category', 'name'),
                Tables\Filters\SelectFilter::make('assigned_to')
                    ->relationship('assignee', 'name')
                    ->label('Assigned To'),
                Tables\Filters\Filter::make('due_date')
                    ->form([
                        Forms\Components\DatePicker::make('due_from'),
                        Forms\Components\DatePicker::make('due_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['due_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('due_date', '>=', $date),
                            )
                            ->when(
                                $data['due_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('due_date', '<=', $date),
                            );
                    }),
                Tables\Filters\Filter::make('overdue')
                    ->query(fn (Builder $query): Builder => $query->where('due_date', '<', now())->whereNotIn('status', ['resolved', 'closed']))
                    ->label('Overdue Complaints')
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('assign')
                    ->icon('heroicon-o-user-plus')
                    ->form([
                        Forms\Components\Select::make('assigned_to')
                            ->label('Assign To')
                            ->options(User::role(['admin', 'teacher'])->pluck('name', 'id'))
                            ->required()
                            ->searchable(),
                    ])
                    ->action(function (Complaint $record, array $data): void {
                        $record->update([
                            'assigned_to' => $data['assigned_to'],
                            'status' => 'assigned',
                        ]);

                        // Record status change
                        $record->statusHistory()->create([
                            'status' => 'assigned',
                            'changed_by' => auth()->id(),
                            'notes' => 'Complaint assigned to staff member',
                        ]);

                        Notification::make()
                            ->title('Complaint assigned successfully')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('change_status')
                    ->icon('heroicon-o-arrow-path')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->label('New Status')
                            ->options(Complaint::STATUSES)
                            ->required(),
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3),
                    ])
                    ->action(function (Complaint $record, array $data): void {
                        $oldStatus = $record->status;
                        $newStatus = $data['status'];

                        $record->update([
                            'status' => $newStatus,
                            'resolution_date' => in_array($newStatus, ['resolved', 'closed']) ? now() : $record->resolution_date,
                        ]);

                        // Record status change
                        $record->statusHistory()->create([
                            'status' => $newStatus,
                            'changed_by' => auth()->id(),
                            'notes' => $data['notes'] ?? "Status changed from {$oldStatus} to {$newStatus}",
                        ]);

                        Notification::make()
                            ->title('Complaint status updated')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('bulk_assign')
                        ->label('Assign Selected')
                        ->icon('heroicon-o-user-plus')
                        ->form([
                            Forms\Components\Select::make('assigned_to')
                                ->label('Assign To')
                                ->options(User::role(['admin', 'teacher'])->pluck('name', 'id'))
                                ->required()
                                ->searchable(),
                        ])
                        ->action(function ($records, array $data): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'assigned_to' => $data['assigned_to'],
                                    'status' => 'assigned',
                                ]);

                                // Record status change
                                $record->statusHistory()->create([
                                    'status' => 'assigned',
                                    'changed_by' => auth()->id(),
                                    'notes' => 'Complaint assigned to staff member (bulk action)',
                                ]);
                            }

                            Notification::make()
                                ->title('Complaints assigned successfully')
                                ->success()
                                ->send();
                        }),
                    Tables\Actions\BulkAction::make('bulk_change_status')
                        ->label('Change Status')
                        ->icon('heroicon-o-arrow-path')
                        ->form([
                            Forms\Components\Select::make('status')
                                ->label('New Status')
                                ->options(Complaint::STATUSES)
                                ->required(),
                            Forms\Components\Textarea::make('notes')
                                ->label('Notes')
                                ->rows(3),
                        ])
                        ->action(function ($records, array $data): void {
                            foreach ($records as $record) {
                                $oldStatus = $record->status;
                                $newStatus = $data['status'];

                                $record->update([
                                    'status' => $newStatus,
                                    'resolution_date' => in_array($newStatus, ['resolved', 'closed']) ? now() : $record->resolution_date,
                                ]);

                                // Record status change
                                $record->statusHistory()->create([
                                    'status' => $newStatus,
                                    'changed_by' => auth()->id(),
                                    'notes' => $data['notes'] ?? "Status changed from {$oldStatus} to {$newStatus} (bulk action)",
                                ]);
                            }

                            Notification::make()
                                ->title('Complaint statuses updated')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CommentsRelationManager::class,
            RelationManagers\AttachmentsRelationManager::class,
            RelationManagers\StatusHistoryRelationManager::class,
            RelationManagers\EscalationsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListComplaints::route('/'),
            'create' => Pages\CreateComplaint::route('/create'),
            'edit' => Pages\EditComplaint::route('/{record}/edit'),
            'view' => Pages\ViewComplaint::route('/{record}'),
        ];
    }
}
