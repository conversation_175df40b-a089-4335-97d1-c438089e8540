<x-filament-panels::page>
    <x-filament-panels::form wire:submit="import">
        {{ $this->form }}

        <div class="mt-6">
            <x-filament::button type="submit" color="success">
                Import Users
            </x-filament::button>
        </div>
    </x-filament-panels::form>

    <div class="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 class="text-lg font-medium text-gray-900">CSV Format Instructions</h3>
        <p class="mt-1 text-sm text-gray-500">
            Your CSV file should include the following columns:
        </p>
        <ul class="mt-2 list-disc list-inside text-sm text-gray-500">
            <li><strong>name</strong> - Full name of the user (required)</li>
            <li><strong>email</strong> - Email address (required, must be unique)</li>
            <li><strong>password</strong> - Password (optional, will be set to "password" if not provided)</li>
            <li><strong>role</strong> - Role name (optional, will use the default role if not provided)</li>
        </ul>
        <div class="mt-4">
            <h4 class="text-md font-medium text-gray-900">Example CSV:</h4>
            <pre class="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">name,email,password,role
John Doe,<EMAIL>,secret123,admin
Jane Smith,<EMAIL>,password456,teacher
Student User,<EMAIL>,student789,student</pre>
        </div>
    </div>
</x-filament-panels::page>
