<?php

namespace App\Filament\Widgets;

use App\Models\ComplaintAnalytics;
use App\Models\ComplaintCategory;
use Filament\Widgets\ChartWidget;

class CategoryDistributionWidget extends ChartWidget
{
    protected static ?string $heading = 'Complaints by Category';

    protected static ?int $sort = 7;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $maxHeight = '400px';

    public ?string $filter = '30days';

    protected function getData(): array
    {
        $data = $this->getCategoryDistributionData();

        return [
            'datasets' => [
                [
                    'data' => array_values($data['counts']),
                    'backgroundColor' => $this->generateColors(count($data['counts'])),
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => array_keys($data['counts']),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getFilters(): ?array
    {
        return [
            '7days' => 'Last 7 days',
            '30days' => 'Last 30 days',
            '3months' => 'Last 3 months',
            '6months' => 'Last 6 months',
            '12months' => 'Last 12 months',
            'ytd' => 'Year to date',
        ];
    }

    private function getCategoryDistributionData(): array
    {
        $period = $this->filter ?? '30days';
        $startDate = $this->getStartDate($period);

        $analytics = ComplaintAnalytics::where('submitted_date', '>=', $startDate)
            ->join('complaint_categories', 'complaint_analytics.category_id', '=', 'complaint_categories.id')
            ->selectRaw('
                complaint_categories.name as category_name,
                COUNT(*) as count,
                AVG(resolution_time_hours) as avg_resolution_time
            ')
            ->groupBy('complaint_categories.id', 'complaint_categories.name')
            ->orderBy('count', 'desc')
            ->get();

        $counts = [];
        $resolutionTimes = [];

        foreach ($analytics as $item) {
            $counts[$item->category_name] = $item->count;
            $resolutionTimes[$item->category_name] = round($item->avg_resolution_time ?? 0, 1);
        }

        return [
            'counts' => $counts,
            'resolutionTimes' => $resolutionTimes,
        ];
    }

    private function getStartDate(string $period): \Carbon\Carbon
    {
        return match ($period) {
            '7days' => now()->subDays(7),
            '30days' => now()->subDays(30),
            '3months' => now()->subMonths(3),
            '6months' => now()->subMonths(6),
            '12months' => now()->subMonths(12),
            'ytd' => now()->startOfYear(),
            default => now()->subDays(30),
        };
    }

    private function generateColors(int $count): array
    {
        $colors = [
            '#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6',
            '#f97316', '#06b6d4', '#84cc16', '#ec4899', '#6366f1',
            '#14b8a6', '#f59e0b', '#ef4444', '#8b5cf6', '#10b981',
        ];

        return array_slice($colors, 0, $count);
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'right',
                    'labels' => [
                        'usePointStyle' => true,
                        'padding' => 20,
                    ],
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const label = context.label || "";
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return label + ": " + value + " (" + percentage + "%)";
                        }',
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
