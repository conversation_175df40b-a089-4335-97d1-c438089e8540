<?php

namespace App\Filament\Resources\ComplaintResource\RelationManagers;

use App\Models\ComplaintEscalation;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EscalationsRelationManager extends RelationManager
{
    protected static string $relationship = 'escalations';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('escalated_to')
                    ->label('Escalate To')
                    ->options(User::role(['admin', 'teacher'])->pluck('name', 'id'))
                    ->required()
                    ->searchable(),
                Forms\Components\Select::make('escalation_level')
                    ->label('Escalation Level')
                    ->options(ComplaintEscalation::ESCALATION_LEVELS)
                    ->required()
                    ->default(1),
                Forms\Components\Textarea::make('reason')
                    ->required()
                    ->rows(3)
                    ->placeholder('Explain why this complaint needs to be escalated'),
                Forms\Components\DateTimePicker::make('escalated_at')
                    ->label('Escalation Date')
                    ->default(now())
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('escalation_level')
            ->columns([
                Tables\Columns\BadgeColumn::make('escalation_level')
                    ->label('Level')
                    ->formatStateUsing(fn (int $state): string => "Level {$state}")
                    ->colors([
                        'primary' => 1,
                        'warning' => 2,
                        'danger' => [3, 4],
                    ]),
                Tables\Columns\TextColumn::make('escalatedBy.name')
                    ->label('Escalated By')
                    ->sortable(),
                Tables\Columns\TextColumn::make('escalatedTo.name')
                    ->label('Escalated To')
                    ->sortable(),
                Tables\Columns\TextColumn::make('reason')
                    ->limit(50)
                    ->wrap(),
                Tables\Columns\TextColumn::make('escalated_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\IconColumn::make('resolved_at')
                    ->label('Resolved')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !is_null($record->resolved_at)),
                Tables\Columns\TextColumn::make('duration')
                    ->label('Duration')
                    ->getStateUsing(function ($record) {
                        $hours = $record->getDurationInHours();
                        if ($hours < 24) {
                            return number_format($hours, 1) . ' hours';
                        }
                        return number_format($hours / 24, 1) . ' days';
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('escalation_level')
                    ->options(ComplaintEscalation::ESCALATION_LEVELS),
                Tables\Filters\Filter::make('unresolved')
                    ->query(fn (Builder $query): Builder => $query->whereNull('resolved_at'))
                    ->label('Unresolved Only'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['escalated_by'] = auth()->id();
                        return $data;
                    })
                    ->after(function ($record): void {
                        // Update the complaint assignment
                        $record->complaint->update(['assigned_to' => $record->escalated_to]);
                        
                        // Record status change
                        $record->complaint->statusHistory()->create([
                            'status' => $record->complaint->status,
                            'changed_by' => auth()->id(),
                            'notes' => "Complaint escalated to level {$record->escalation_level}: {$record->reason}",
                        ]);
                        
                        // Send notifications
                        $record->complaint->sendEscalationNotifications($record);
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('resolve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn ($record) => !$record->isResolved())
                    ->form([
                        Forms\Components\Textarea::make('resolution_notes')
                            ->label('Resolution Notes')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function ($record, array $data): void {
                        $record->resolve($data['resolution_notes']);
                    }),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => !$record->isResolved()),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => !$record->isResolved()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('resolve_selected')
                        ->label('Resolve Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->form([
                            Forms\Components\Textarea::make('resolution_notes')
                                ->label('Resolution Notes')
                                ->required()
                                ->rows(3),
                        ])
                        ->action(function ($records, array $data): void {
                            foreach ($records as $record) {
                                if (!$record->isResolved()) {
                                    $record->resolve($data['resolution_notes']);
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('escalated_at', 'desc');
    }
}
