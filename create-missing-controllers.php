<?php

echo "🔧 Creating missing auth controllers...\n";

$authDir = 'app/Http/Controllers/Auth';
if (!is_dir($authDir)) {
    mkdir($authDir, 0755, true);
}

$controllers = [
    'ConfirmablePasswordController.php' => '<?php
namespace App\Http\Controllers\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
class ConfirmablePasswordController extends Controller
{
    public function show() { return view("auth.confirm-password"); }
    public function store(Request $request) { return redirect()->intended(); }
}',

    'EmailVerificationNotificationController.php' => '<?php
namespace App\Http\Controllers\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
class EmailVerificationNotificationController extends Controller
{
    public function store(Request $request) { return back()->with("status", "verification-link-sent"); }
}',

    'EmailVerificationPromptController.php' => '<?php
namespace App\Http\Controllers\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
class EmailVerificationPromptController extends Controller
{
    public function __invoke(Request $request) { return view("auth.verify-email"); }
}',

    'NewPasswordController.php' => '<?php
namespace App\Http\Controllers\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
class NewPasswordController extends Controller
{
    public function create(Request $request) { return view("auth.reset-password", ["request" => $request]); }
    public function store(Request $request) { return redirect()->route("login"); }
}',

    'PasswordController.php' => '<?php
namespace App\Http\Controllers\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
class PasswordController extends Controller
{
    public function update(Request $request) { return back()->with("status", "password-updated"); }
}',

    'PasswordResetLinkController.php' => '<?php
namespace App\Http\Controllers\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
class PasswordResetLinkController extends Controller
{
    public function create() { return view("auth.forgot-password"); }
    public function store(Request $request) { return back()->with("status", "password-reset-link-sent"); }
}',

    'VerifyEmailController.php' => '<?php
namespace App\Http\Controllers\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
class VerifyEmailController extends Controller
{
    public function __invoke(Request $request) { return redirect()->intended("/dashboard"); }
}'
];

foreach ($controllers as $filename => $content) {
    $filePath = $authDir . '/' . $filename;
    if (!file_exists($filePath)) {
        file_put_contents($filePath, $content);
        echo "✅ Created $filename\n";
    }
}

echo "✅ All auth controllers created!\n";
