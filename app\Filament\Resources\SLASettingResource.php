<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SLASettingResource\Pages;
use App\Models\SLASetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SLASettingResource extends Resource
{
    protected static ?string $model = SLASetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationGroup = 'System Configuration';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'SLA Settings';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('SLA Configuration')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Critical Priority SLA'),
                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->placeholder('Description of this SLA setting'),
                        Forms\Components\Select::make('priority')
                            ->options([
                                'critical' => 'Critical',
                                'high' => 'High',
                                'medium' => 'Medium',
                                'low' => 'Low',
                            ])
                            ->required()
                            ->unique(ignoreRecord: true),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('first_response_hours')
                                    ->label('First Response (Hours)')
                                    ->numeric()
                                    ->required()
                                    ->default(4)
                                    ->helperText('Hours to provide first response'),
                                Forms\Components\TextInput::make('resolution_days')
                                    ->label('Resolution (Days)')
                                    ->numeric()
                                    ->required()
                                    ->default(7)
                                    ->helperText('Days to resolve the complaint'),
                                Forms\Components\TextInput::make('escalation_days')
                                    ->label('Escalation (Days)')
                                    ->numeric()
                                    ->required()
                                    ->default(5)
                                    ->helperText('Days before escalation'),
                            ]),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->helperText('Only active SLA settings will be used'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('priority')
                    ->colors([
                        'danger' => 'critical',
                        'warning' => 'high',
                        'primary' => 'medium',
                        'secondary' => 'low',
                    ]),
                Tables\Columns\TextColumn::make('first_response_hours')
                    ->label('First Response')
                    ->formatStateUsing(fn (string $state): string => $state . ' hours')
                    ->sortable(),
                Tables\Columns\TextColumn::make('resolution_days')
                    ->label('Resolution')
                    ->formatStateUsing(fn (string $state): string => $state . ' days')
                    ->sortable(),
                Tables\Columns\TextColumn::make('escalation_days')
                    ->label('Escalation')
                    ->formatStateUsing(fn (string $state): string => $state . ' days')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('priority')
                    ->options([
                        'critical' => 'Critical',
                        'high' => 'High',
                        'medium' => 'Medium',
                        'low' => 'Low',
                    ]),
                Tables\Filters\Filter::make('active')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true))
                    ->label('Active Only')
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('toggle_status')
                    ->label(fn (SLASetting $record): string => $record->is_active ? 'Deactivate' : 'Activate')
                    ->icon(fn (SLASetting $record): string => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (SLASetting $record): string => $record->is_active ? 'danger' : 'success')
                    ->action(function (SLASetting $record): void {
                        $record->update(['is_active' => !$record->is_active]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->action(function ($records): void {
                            foreach ($records as $record) {
                                $record->update(['is_active' => true]);
                            }
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->action(function ($records): void {
                            foreach ($records as $record) {
                                $record->update(['is_active' => false]);
                            }
                        }),
                ]),
            ])
            ->defaultSort('priority', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSLASettings::route('/'),
            'create' => Pages\CreateSLASetting::route('/create'),
            'edit' => Pages\EditSLASetting::route('/{record}/edit'),
        ];
    }
}
