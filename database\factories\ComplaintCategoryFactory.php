<?php

namespace Database\Factories;

use App\Models\ComplaintCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ComplaintCategory>
 */
class ComplaintCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ComplaintCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'Academic Issues' => 'Issues related to courses, curriculum, and academic programs',
            'Administrative Services' => 'Problems with administrative processes and services',
            'Facilities & Infrastructure' => 'Issues with campus facilities, buildings, and infrastructure',
            'Student Services' => 'Problems with student support services and activities',
            'Technology & IT' => 'Issues with technology systems, software, and IT support',
            'Financial Services' => 'Problems with fees, payments, and financial aid',
            'Library Services' => 'Issues with library resources and services',
            'Food Services' => 'Problems with cafeteria and food service quality',
            'Transportation' => 'Issues with campus transportation and parking',
            'Health & Safety' => 'Health and safety concerns on campus',
            'Discrimination & Harassment' => 'Reports of discrimination or harassment',
            'Other' => 'Other complaints not covered by specific categories',
        ];

        $categoryName = $this->faker->randomElement(array_keys($categories));
        
        return [
            'name' => $categoryName,
            'description' => $categories[$categoryName],
            'color' => $this->faker->hexColor(),
            'icon' => $this->getIconForCategory($categoryName),
            'sla_days' => $this->faker->numberBetween(3, 14),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'sort_order' => $this->faker->numberBetween(1, 100),
        ];
    }

    /**
     * Get appropriate icon for category
     */
    private function getIconForCategory(string $categoryName): string
    {
        return match($categoryName) {
            'Academic Issues' => 'heroicon-o-academic-cap',
            'Administrative Services' => 'heroicon-o-building-office',
            'Facilities & Infrastructure' => 'heroicon-o-building-office-2',
            'Student Services' => 'heroicon-o-users',
            'Technology & IT' => 'heroicon-o-computer-desktop',
            'Financial Services' => 'heroicon-o-currency-dollar',
            'Library Services' => 'heroicon-o-book-open',
            'Food Services' => 'heroicon-o-cake',
            'Transportation' => 'heroicon-o-truck',
            'Health & Safety' => 'heroicon-o-shield-check',
            'Discrimination & Harassment' => 'heroicon-o-exclamation-triangle',
            'Other' => 'heroicon-o-question-mark-circle',
            default => 'heroicon-o-folder',
        };
    }

    /**
     * Indicate that the category is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the category is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the category has urgent SLA.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'sla_days' => $this->faker->numberBetween(1, 3),
        ]);
    }

    /**
     * Indicate that the category has standard SLA.
     */
    public function standard(): static
    {
        return $this->state(fn (array $attributes) => [
            'sla_days' => $this->faker->numberBetween(5, 10),
        ]);
    }

    /**
     * Indicate that the category has extended SLA.
     */
    public function extended(): static
    {
        return $this->state(fn (array $attributes) => [
            'sla_days' => $this->faker->numberBetween(10, 21),
        ]);
    }
}
