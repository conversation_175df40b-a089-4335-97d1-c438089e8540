<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>vel Full-Stack Mastery Course - Complete Curriculum</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.4em;
            margin-bottom: 30px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .course-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }

        .stat-card h3 {
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #fff;
        }

        .stat-card p {
            color: rgba(255,255,255,0.9);
        }

        .content {
            padding: 40px;
        }

        .course-overview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
        }

        .course-overview h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2em;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .learning-path {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .path-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .path-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .path-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .path-card p {
            color: #6c757d;
            margin-bottom: 15px;
        }

        .path-card .duration {
            background: #e8f4fd;
            color: #0c5460;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            display: inline-block;
        }

        .module {
            margin-bottom: 60px;
            page-break-inside: avoid;
        }

        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            margin-bottom: 0;
        }

        .module-header h2 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .module-header .module-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .meta-item {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .module-content {
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 15px 15px;
            padding: 30px;
        }

        .objectives {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .objectives h3 {
            color: #0c5460;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .objectives ul {
            list-style-type: none;
            padding-left: 0;
        }

        .objectives li {
            padding: 8px 0;
            color: #0c5460;
            position: relative;
            padding-left: 30px;
        }

        .objectives li::before {
            content: "🎯";
            position: absolute;
            left: 0;
            top: 8px;
        }

        .lesson {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .lesson-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .lesson-title {
            color: #2c3e50;
            font-size: 1.3em;
            font-weight: 600;
            margin: 0;
        }

        .lesson-duration {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            white-space: nowrap;
        }

        .lesson-description {
            color: #495057;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .lesson-topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .topic-tag {
            background: #e9ecef;
            color: #495057;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            position: relative;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .code-example::before {
            content: attr(data-lang);
            position: absolute;
            top: 8px;
            right: 15px;
            background: #4a5568;
            color: #e2e8f0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            text-transform: uppercase;
        }

        .diagram {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 30px;
            margin: 25px 0;
            text-align: center;
        }

        .diagram h4 {
            color: #495057;
            margin-bottom: 15px;
        }

        .diagram-content {
            font-family: monospace;
            color: #6c757d;
            white-space: pre-line;
        }

        .best-practices {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .best-practices h4 {
            color: #155724;
            margin-bottom: 10px;
        }

        .pitfalls {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .pitfalls h4 {
            color: #721c24;
            margin-bottom: 10px;
        }

        .exercise {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .exercise h4 {
            color: #856404;
            margin-bottom: 15px;
        }

        .resources {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .resources h4 {
            color: #0056b3;
            margin-bottom: 15px;
        }

        .resources ul {
            list-style-type: none;
            padding-left: 0;
        }

        .resources li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }

        .resources li::before {
            content: "🔗";
            position: absolute;
            left: 0;
            top: 5px;
        }

        .resources a {
            color: #0056b3;
            text-decoration: none;
        }

        .resources a:hover {
            text-decoration: underline;
        }

        .progress-tracker {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 250px;
            max-height: 400px;
            overflow-y: auto;
        }

        .progress-tracker h4 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .progress-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .module-list {
            list-style: none;
            padding: 0;
        }

        .module-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            font-size: 0.9em;
        }

        .module-list li:last-child {
            border-bottom: none;
        }

        .module-checkbox {
            margin-right: 10px;
            transform: scale(1.1);
        }

        @media print {
            body {
                background: white;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
            }

            .progress-tracker {
                display: none;
            }

            .module {
                page-break-before: always;
                margin-bottom: 30px;
            }

            .module:first-of-type {
                page-break-before: auto;
            }

            .code-example {
                font-size: 10px;
                padding: 15px;
            }

            .header {
                page-break-after: always;
            }
        }

        @media (max-width: 768px) {
            .progress-tracker {
                position: relative;
                top: auto;
                right: auto;
                margin: 20px;
                width: auto;
            }

            .header {
                padding: 40px 20px;
            }

            .header h1 {
                font-size: 2.5em;
            }

            .content {
                padding: 20px;
            }

            .course-stats {
                grid-template-columns: 1fr;
            }

            .lesson-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Laravel Full-Stack Mastery</h1>
            <p class="subtitle">Complete Course Curriculum for Intermediate Web Developers</p>

            <div class="course-stats">
                <div class="stat-card">
                    <h3>📚 Total Modules</h3>
                    <p>6 Comprehensive Modules</p>
                </div>
                <div class="stat-card">
                    <h3>⏱️ Course Duration</h3>
                    <p>80-100 Hours</p>
                </div>
                <div class="stat-card">
                    <h3>🎯 Skill Level</h3>
                    <p>Intermediate to Advanced</p>
                </div>
                <div class="stat-card">
                    <h3>🏆 Projects</h3>
                    <p>6 Real-World Applications</p>
                </div>
                <div class="stat-card">
                    <h3>📖 Lessons</h3>
                    <p>45+ Detailed Lessons</p>
                </div>
                <div class="stat-card">
                    <h3>🔧 Tools</h3>
                    <p>Laravel, Filament, Tailwind</p>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="course-overview">
                <h2>📋 Course Overview & Learning Path</h2>
                <p style="margin-bottom: 25px; color: #6c757d; font-size: 1.1em;">
                    This comprehensive course transforms intermediate web developers into Laravel full-stack experts through
                    hands-on projects, real-world scenarios, and industry best practices. Each module builds progressively,
                    ensuring deep understanding and practical application of advanced Laravel concepts.
                </p>

                <div class="learning-path">
                    <div class="path-card">
                        <h3>🏗️ Foundation Building</h3>
                        <p>Master Laravel fundamentals, MVC architecture, and core concepts that form the backbone of modern web applications.</p>
                        <span class="duration">Modules 1-2 | 25-30 Hours</span>
                    </div>

                    <div class="path-card">
                        <h3>⚡ Advanced Development</h3>
                        <p>Build sophisticated admin interfaces, implement security, and create dynamic frontend experiences.</p>
                        <span class="duration">Modules 3-4 | 30-35 Hours</span>
                    </div>

                    <div class="path-card">
                        <h3>🎨 Professional Polish</h3>
                        <p>Add modern frontend styling, implement best practices, and prepare applications for production deployment.</p>
                        <span class="duration">Modules 5-6 | 25-30 Hours</span>
                    </div>
                </div>
            </div>

            <!-- Module 1: Laravel Fundamentals -->
            <div class="module" id="module1">
                <div class="module-header">
                    <h2>Module 1: Laravel Fundamentals</h2>
                    <p>Master the core concepts of Laravel's MVC architecture and build solid foundations</p>
                    <div class="module-meta">
                        <span class="meta-item">⏱️ Duration: 15-18 Hours</span>
                        <span class="meta-item">📚 8 Lessons</span>
                        <span class="meta-item">🏗️ Project: Blog System</span>
                        <span class="meta-item">📊 Difficulty: Intermediate</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Understand Laravel's MVC architecture and request lifecycle</li>
                            <li>Master Eloquent ORM for database interactions and relationships</li>
                            <li>Create dynamic, reusable Blade templates and components</li>
                            <li>Implement RESTful routing patterns and middleware</li>
                            <li>Build a complete blog system with CRUD operations</li>
                            <li>Apply Laravel conventions and best practices</li>
                        </ul>
                    </div>

                    <!-- Lesson 1.1 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">1.1 Laravel Architecture & Request Lifecycle</h3>
                            <span class="lesson-duration">2 hours</span>
                        </div>
                        <p class="lesson-description">
                            Deep dive into Laravel's architecture, understanding how requests flow through the framework,
                            and exploring the service container and service providers that power Laravel applications.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">MVC Pattern</span>
                            <span class="topic-tag">Request Lifecycle</span>
                            <span class="topic-tag">Service Container</span>
                            <span class="topic-tag">Service Providers</span>
                            <span class="topic-tag">Facades</span>
                        </div>

                        <div class="diagram">
                            <h4>Laravel Request Lifecycle</h4>
                            <div class="diagram-content">
HTTP Request → Public/index.php → Bootstrap → Kernel → Middleware → Router → Controller → Response
                ↓
            Service Container
                ↓
            Service Providers
                ↓
            Application Services
            </div>
                        </div>

                        <div class="code-example" data-lang="php">
<?php
// Understanding Service Container and Dependency Injection

// Service Provider Example
class BlogServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(BlogRepositoryInterface::class, BlogRepository::class);

        $this->app->singleton('blog.manager', function ($app) {
            return new BlogManager($app['config']['blog']);
        });
    }

    public function boot()
    {
        $this->loadViewsFrom(__DIR__.'/views', 'blog');
        $this->publishes([
            __DIR__.'/config/blog.php' => config_path('blog.php'),
        ]);
    }
}

// Controller with Dependency Injection
class PostController extends Controller
{
    public function __construct(
        private BlogRepositoryInterface $blogRepository,
        private CacheManager $cache
    ) {}

    public function index()
    {
        $posts = $this->cache->remember('posts.all', 3600, function () {
            return $this->blogRepository->getAllPublished();
        });

        return view('posts.index', compact('posts'));
    }
}
                        </div>

                        <div class="best-practices">
                            <h4>✅ Best Practices</h4>
                            <ul>
                                <li>Use dependency injection instead of facades in controllers for better testability</li>
                                <li>Keep service providers focused on a single responsibility</li>
                                <li>Leverage the service container for complex object creation</li>
                                <li>Use contracts (interfaces) for better decoupling</li>
                            </ul>
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Exercise 1.1</h4>
                            <p><strong>Task:</strong> Create a custom service provider for a "Settings" service that manages application configuration.</p>
                            <ul>
                                <li>Create a SettingsServiceProvider</li>
                                <li>Implement a Settings service class</li>
                                <li>Register the service in the container</li>
                                <li>Use dependency injection in a controller</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Lesson 1.2 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">1.2 Advanced Eloquent Models & Relationships</h3>
                            <span class="lesson-duration">3 hours</span>
                        </div>
                        <p class="lesson-description">
                            Master Eloquent ORM with advanced model features, complex relationships, scopes, mutators,
                            accessors, and query optimization techniques for building robust data layers.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Eloquent Models</span>
                            <span class="topic-tag">Relationships</span>
                            <span class="topic-tag">Scopes</span>
                            <span class="topic-tag">Mutators/Accessors</span>
                            <span class="topic-tag">Query Optimization</span>
                        </div>

                        <div class="diagram">
                            <h4>Blog System ERD</h4>
                            <div class="diagram-content">
Users                    Posts                    Categories
├── id                   ├── id                   ├── id
├── name                 ├── title                ├── name
├── email                ├── slug                 ├── slug
├── password             ├── content              ├── description
├── created_at           ├── excerpt              └── created_at
└── updated_at           ├── published_at
                         ├── user_id (FK)
                         ├── category_id (FK)
                         ├── created_at
                         └── updated_at

                    Comments                 Tags
                    ├── id                   ├── id
                    ├── content              ├── name
                    ├── post_id (FK)         ├── slug
                    ├── user_id (FK)         └── created_at
                    ├── created_at
                    └── updated_at           PostTag (Pivot)
                                            ├── post_id (FK)
                                            └── tag_id (FK)
            </div>
                        </div>

                        <div class="code-example" data-lang="php">
<?php
// Advanced Eloquent Model Example

class Post extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title', 'slug', 'content', 'excerpt', 'published_at',
        'user_id', 'category_id', 'featured_image', 'meta_description'
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'is_featured' => 'boolean',
        'meta_data' => 'array',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)
                    ->withTimestamps()
                    ->withPivot(['sort_order']);
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('published_at', '<=', now())
                    ->whereNotNull('published_at');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCategory($query, $categorySlug)
    {
        return $query->whereHas('category', function ($q) use ($categorySlug) {
            $q->where('slug', $categorySlug);
        });
    }

    public function scopeWithRelated($query)
    {
        return $query->with(['user:id,name', 'category:id,name,slug', 'tags:id,name,slug']);
    }

    // Mutators
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        $this->attributes['slug'] = Str::slug($value);
    }

    // Accessors
    public function getExcerptAttribute($value)
    {
        return $value ?: Str::limit(strip_tags($this->content), 150);
    }

    public function getReadingTimeAttribute()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        return ceil($wordCount / 200); // Average reading speed
    }

    // Boot method for model events
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            if (empty($post->slug)) {
                $post->slug = Str::slug($post->title);
            }
        });

        static::deleting(function ($post) {
            $post->comments()->delete();
            $post->tags()->detach();
        });
    }
}
                        </div>

                        <div class="best-practices">
                            <h4>✅ Best Practices</h4>
                            <ul>
                                <li>Use eager loading to prevent N+1 query problems</li>
                                <li>Implement model scopes for reusable query logic</li>
                                <li>Use mutators for data transformation on save</li>
                                <li>Leverage accessors for computed attributes</li>
                                <li>Always define fillable or guarded properties</li>
                            </ul>
                        </div>

                        <div class="pitfalls">
                            <h4>⚠️ Common Pitfalls</h4>
                            <ul>
                                <li>Forgetting to eager load relationships (N+1 queries)</li>
                                <li>Not using database transactions for related operations</li>
                                <li>Overusing accessors for expensive computations</li>
                                <li>Not validating data before saving to database</li>
                            </ul>
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Exercise 1.2</h4>
                            <p><strong>Task:</strong> Create a complete blog post model with relationships and advanced features.</p>
                            <ul>
                                <li>Implement all relationships (User, Category, Comments, Tags)</li>
                                <li>Add scopes for published posts and featured posts</li>
                                <li>Create mutators for slug generation and content processing</li>
                                <li>Add accessors for reading time and excerpt</li>
                                <li>Test relationships and scopes in Tinker</li>
                            </ul>
                        </div>
                    </div>

                    <div class="resources">
                        <h4>📚 Module 1 Resources</h4>
                        <ul>
                            <li><a href="https://laravel.com/docs/controllers">Laravel Controllers Documentation</a></li>
                            <li><a href="https://laravel.com/docs/eloquent">Eloquent ORM Documentation</a></li>
                            <li><a href="https://laravel.com/docs/routing">Laravel Routing Documentation</a></li>
                            <li><a href="https://laracasts.com/series/laravel-from-scratch">Laracasts Laravel Series</a></li>
                            <li><a href="https://laravel.com/docs/blade">Blade Templates Documentation</a></li>
                        </ul>
                    </div>
                </div>
            </div>