<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>vel Full-Stack Mastery Course - Complete Curriculum</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.4em;
            margin-bottom: 30px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .course-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }

        .stat-card h3 {
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #fff;
        }

        .stat-card p {
            color: rgba(255,255,255,0.9);
        }

        .content {
            padding: 40px;
        }

        .course-overview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
        }

        .course-overview h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2em;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .learning-path {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .path-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .path-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .path-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .path-card p {
            color: #6c757d;
            margin-bottom: 15px;
        }

        .path-card .duration {
            background: #e8f4fd;
            color: #0c5460;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            display: inline-block;
        }

        .module {
            margin-bottom: 60px;
            page-break-inside: avoid;
        }

        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            margin-bottom: 0;
        }

        .module-header h2 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .module-header .module-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .meta-item {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .module-content {
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 15px 15px;
            padding: 30px;
        }

        .objectives {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .objectives h3 {
            color: #0c5460;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .objectives ul {
            list-style-type: none;
            padding-left: 0;
        }

        .objectives li {
            padding: 8px 0;
            color: #0c5460;
            position: relative;
            padding-left: 30px;
        }

        .objectives li::before {
            content: "🎯";
            position: absolute;
            left: 0;
            top: 8px;
        }

        .lesson {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .lesson-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .lesson-title {
            color: #2c3e50;
            font-size: 1.3em;
            font-weight: 600;
            margin: 0;
        }

        .lesson-duration {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            white-space: nowrap;
        }

        .lesson-description {
            color: #495057;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .lesson-topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .topic-tag {
            background: #e9ecef;
            color: #495057;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            position: relative;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .code-example::before {
            content: attr(data-lang);
            position: absolute;
            top: 8px;
            right: 15px;
            background: #4a5568;
            color: #e2e8f0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            text-transform: uppercase;
        }

        .diagram {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 30px;
            margin: 25px 0;
            text-align: center;
        }

        .diagram h4 {
            color: #495057;
            margin-bottom: 15px;
        }

        .diagram-content {
            font-family: monospace;
            color: #6c757d;
            white-space: pre-line;
        }

        .best-practices {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .best-practices h4 {
            color: #155724;
            margin-bottom: 10px;
        }

        .pitfalls {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .pitfalls h4 {
            color: #721c24;
            margin-bottom: 10px;
        }

        .exercise {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .exercise h4 {
            color: #856404;
            margin-bottom: 15px;
        }

        .resources {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .resources h4 {
            color: #0056b3;
            margin-bottom: 15px;
        }

        .resources ul {
            list-style-type: none;
            padding-left: 0;
        }

        .resources li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }

        .resources li::before {
            content: "🔗";
            position: absolute;
            left: 0;
            top: 5px;
        }

        .resources a {
            color: #0056b3;
            text-decoration: none;
        }

        .resources a:hover {
            text-decoration: underline;
        }

        .progress-tracker {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 250px;
            max-height: 400px;
            overflow-y: auto;
        }

        .progress-tracker h4 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .progress-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .module-list {
            list-style: none;
            padding: 0;
        }

        .module-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            font-size: 0.9em;
        }

        .module-list li:last-child {
            border-bottom: none;
        }

        .module-checkbox {
            margin-right: 10px;
            transform: scale(1.1);
        }

        @media print {
            body {
                background: white;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
            }

            .progress-tracker {
                display: none;
            }

            .module {
                page-break-before: always;
                margin-bottom: 30px;
            }

            .module:first-of-type {
                page-break-before: auto;
            }

            .code-example {
                font-size: 10px;
                padding: 15px;
            }

            .header {
                page-break-after: always;
            }
        }

        @media (max-width: 768px) {
            .progress-tracker {
                position: relative;
                top: auto;
                right: auto;
                margin: 20px;
                width: auto;
            }

            .header {
                padding: 40px 20px;
            }

            .header h1 {
                font-size: 2.5em;
            }

            .content {
                padding: 20px;
            }

            .course-stats {
                grid-template-columns: 1fr;
            }

            .lesson-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Laravel Full-Stack Mastery</h1>
            <p class="subtitle">Complete Course Curriculum for Intermediate Web Developers</p>

            <div class="course-stats">
                <div class="stat-card">
                    <h3>📚 Total Modules</h3>
                    <p>6 Comprehensive Modules</p>
                </div>
                <div class="stat-card">
                    <h3>⏱️ Course Duration</h3>
                    <p>80-100 Hours</p>
                </div>
                <div class="stat-card">
                    <h3>🎯 Skill Level</h3>
                    <p>Intermediate to Advanced</p>
                </div>
                <div class="stat-card">
                    <h3>🏆 Projects</h3>
                    <p>6 Real-World Applications</p>
                </div>
                <div class="stat-card">
                    <h3>📖 Lessons</h3>
                    <p>45+ Detailed Lessons</p>
                </div>
                <div class="stat-card">
                    <h3>🔧 Tools</h3>
                    <p>Laravel, Filament, Tailwind</p>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="course-overview">
                <h2>📋 Course Overview & Learning Path</h2>
                <p style="margin-bottom: 25px; color: #6c757d; font-size: 1.1em;">
                    This comprehensive course transforms intermediate web developers into Laravel full-stack experts through
                    hands-on projects, real-world scenarios, and industry best practices. Each module builds progressively,
                    ensuring deep understanding and practical application of advanced Laravel concepts.
                </p>

                <div class="learning-path">
                    <div class="path-card">
                        <h3>🏗️ Foundation Building</h3>
                        <p>Master Laravel fundamentals, MVC architecture, and core concepts that form the backbone of modern web applications.</p>
                        <span class="duration">Modules 1-2 | 25-30 Hours</span>
                    </div>

                    <div class="path-card">
                        <h3>⚡ Advanced Development</h3>
                        <p>Build sophisticated admin interfaces, implement security, and create dynamic frontend experiences.</p>
                        <span class="duration">Modules 3-4 | 30-35 Hours</span>
                    </div>

                    <div class="path-card">
                        <h3>🎨 Professional Polish</h3>
                        <p>Add modern frontend styling, implement best practices, and prepare applications for production deployment.</p>
                        <span class="duration">Modules 5-6 | 25-30 Hours</span>
                    </div>
                </div>
            </div>

            <!-- Module 1: Laravel Fundamentals -->
            <div class="module" id="module1">
                <div class="module-header">
                    <h2>Module 1: Laravel Fundamentals</h2>
                    <p>Master the core concepts of Laravel's MVC architecture and build solid foundations</p>
                    <div class="module-meta">
                        <span class="meta-item">⏱️ Duration: 15-18 Hours</span>
                        <span class="meta-item">📚 8 Lessons</span>
                        <span class="meta-item">🏗️ Project: Blog System</span>
                        <span class="meta-item">📊 Difficulty: Intermediate</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Understand Laravel's MVC architecture and request lifecycle</li>
                            <li>Master Eloquent ORM for database interactions and relationships</li>
                            <li>Create dynamic, reusable Blade templates and components</li>
                            <li>Implement RESTful routing patterns and middleware</li>
                            <li>Build a complete blog system with CRUD operations</li>
                            <li>Apply Laravel conventions and best practices</li>
                        </ul>
                    </div>

                    <!-- Lesson 1.1 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">1.1 Laravel Architecture & Request Lifecycle</h3>
                            <span class="lesson-duration">2 hours</span>
                        </div>
                        <p class="lesson-description">
                            Deep dive into Laravel's architecture, understanding how requests flow through the framework,
                            and exploring the service container and service providers that power Laravel applications.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">MVC Pattern</span>
                            <span class="topic-tag">Request Lifecycle</span>
                            <span class="topic-tag">Service Container</span>
                            <span class="topic-tag">Service Providers</span>
                            <span class="topic-tag">Facades</span>
                        </div>

                        <div class="diagram">
                            <h4>Laravel Request Lifecycle</h4>
                            <div class="diagram-content">
HTTP Request → Public/index.php → Bootstrap → Kernel → Middleware → Router → Controller → Response
                ↓
            Service Container
                ↓
            Service Providers
                ↓
            Application Services
            </div>
                        </div>

                        <div class="code-example" data-lang="php">
<?php
// Understanding Service Container and Dependency Injection

// Service Provider Example
class BlogServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(BlogRepositoryInterface::class, BlogRepository::class);

        $this->app->singleton('blog.manager', function ($app) {
            return new BlogManager($app['config']['blog']);
        });
    }

    public function boot()
    {
        $this->loadViewsFrom(__DIR__.'/views', 'blog');
        $this->publishes([
            __DIR__.'/config/blog.php' => config_path('blog.php'),
        ]);
    }
}

// Controller with Dependency Injection
class PostController extends Controller
{
    public function __construct(
        private BlogRepositoryInterface $blogRepository,
        private CacheManager $cache
    ) {}

    public function index()
    {
        $posts = $this->cache->remember('posts.all', 3600, function () {
            return $this->blogRepository->getAllPublished();
        });

        return view('posts.index', compact('posts'));
    }
}
                        </div>

                        <div class="best-practices">
                            <h4>✅ Best Practices</h4>
                            <ul>
                                <li>Use dependency injection instead of facades in controllers for better testability</li>
                                <li>Keep service providers focused on a single responsibility</li>
                                <li>Leverage the service container for complex object creation</li>
                                <li>Use contracts (interfaces) for better decoupling</li>
                            </ul>
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Exercise 1.1</h4>
                            <p><strong>Task:</strong> Create a custom service provider for a "Settings" service that manages application configuration.</p>
                            <ul>
                                <li>Create a SettingsServiceProvider</li>
                                <li>Implement a Settings service class</li>
                                <li>Register the service in the container</li>
                                <li>Use dependency injection in a controller</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Lesson 1.2 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">1.2 Advanced Eloquent Models & Relationships</h3>
                            <span class="lesson-duration">3 hours</span>
                        </div>
                        <p class="lesson-description">
                            Master Eloquent ORM with advanced model features, complex relationships, scopes, mutators,
                            accessors, and query optimization techniques for building robust data layers.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Eloquent Models</span>
                            <span class="topic-tag">Relationships</span>
                            <span class="topic-tag">Scopes</span>
                            <span class="topic-tag">Mutators/Accessors</span>
                            <span class="topic-tag">Query Optimization</span>
                        </div>

                        <div class="diagram">
                            <h4>Blog System ERD</h4>
                            <div class="diagram-content">
Users                    Posts                    Categories
├── id                   ├── id                   ├── id
├── name                 ├── title                ├── name
├── email                ├── slug                 ├── slug
├── password             ├── content              ├── description
├── created_at           ├── excerpt              └── created_at
└── updated_at           ├── published_at
                         ├── user_id (FK)
                         ├── category_id (FK)
                         ├── created_at
                         └── updated_at

                    Comments                 Tags
                    ├── id                   ├── id
                    ├── content              ├── name
                    ├── post_id (FK)         ├── slug
                    ├── user_id (FK)         └── created_at
                    ├── created_at
                    └── updated_at           PostTag (Pivot)
                                            ├── post_id (FK)
                                            └── tag_id (FK)
            </div>
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Exercise 1.2</h4>
                            <p><strong>Task:</strong> Create a complete blog post model with relationships and advanced features.</p>
                            <ul>
                                <li>Implement all relationships (User, Category, Comments, Tags)</li>
                                <li>Add scopes for published posts and featured posts</li>
                                <li>Create mutators for slug generation and content processing</li>
                                <li>Add accessors for reading time and excerpt</li>
                                <li>Test relationships and scopes in Tinker</li>
                            </ul>
                        </div>
                    </div>

                    <div class="resources">
                        <h4>📚 Module 1 Resources</h4>
                        <ul>
                            <li><a href="https://laravel.com/docs/controllers">Laravel Controllers Documentation</a></li>
                            <li><a href="https://laravel.com/docs/eloquent">Eloquent ORM Documentation</a></li>
                            <li><a href="https://laravel.com/docs/routing">Laravel Routing Documentation</a></li>
                            <li><a href="https://laracasts.com/series/laravel-from-scratch">Laracasts Laravel Series</a></li>
                            <li><a href="https://laravel.com/docs/blade">Blade Templates Documentation</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Module 2: Database Design & Management -->
            <div class="module" id="module2">
                <div class="module-header">
                    <h2>Module 2: Database Design & Management</h2>
                    <p>Master advanced database architecture, migrations, and optimization strategies</p>
                    <div class="module-meta">
                        <span class="meta-item">⏱️ Duration: 12-15 Hours</span>
                        <span class="meta-item">📚 7 Lessons</span>
                        <span class="meta-item">🏗️ Project: E-commerce Database</span>
                        <span class="meta-item">📊 Difficulty: Intermediate</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Design complex database schemas with proper normalization</li>
                            <li>Master advanced migration strategies and team collaboration</li>
                            <li>Implement all types of Eloquent relationships including polymorphic</li>
                            <li>Optimize database performance through indexing and query analysis</li>
                            <li>Create sophisticated seeders with realistic test data</li>
                            <li>Build an e-commerce product catalog with variants and attributes</li>
                        </ul>
                    </div>

                    <!-- Lesson 2.1 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">2.1 Advanced Migration Strategies</h3>
                            <span class="lesson-duration">2.5 hours</span>
                        </div>
                        <p class="lesson-description">
                            Learn to design maintainable database schemas that can evolve with your application while
                            supporting team development workflows and production deployments.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Migration Design</span>
                            <span class="topic-tag">Schema Evolution</span>
                            <span class="topic-tag">Team Collaboration</span>
                            <span class="topic-tag">Production Deployments</span>
                            <span class="topic-tag">Rollback Strategies</span>
                        </div>

                        <div class="diagram">
                            <h4>E-commerce Database Schema</h4>
                            <div class="diagram-content">
Products                 Categories               Brands
├── id                   ├── id                   ├── id
├── name                 ├── name                 ├── name
├── slug                 ├── slug                 ├── slug
├── description          ├── description          ├── logo
├── price                ├── parent_id (FK)       └── created_at
├── brand_id (FK)        ├── sort_order
├── category_id (FK)     └── created_at           ProductVariants
├── sku                                           ├── id
├── stock_quantity       Orders                   ├── product_id (FK)
├── status               ├── id                   ├── sku
└── created_at           ├── user_id (FK)         ├── price_adjustment
                         ├── total                ├── stock_quantity
ProductAttributes        ├── status               ├── attributes (JSON)
├── id                   ├── shipped_at           └── created_at
├── product_id (FK)      └── created_at
├── name                                          OrderItems
├── value                Reviews (Polymorphic)    ├── id
└── created_at           ├── id                   ├── order_id (FK)
                         ├── reviewable_id        ├── product_id (FK)
                         ├── reviewable_type      ├── variant_id (FK)
                         ├── user_id (FK)         ├── quantity
                         ├── rating               ├── price
                         ├── comment              └── created_at
                         └── created_at
            </div>
                        </div>

                        <div class="code-example" data-lang="php">
<?php
// Advanced Migration Example: E-commerce Product System

class CreateProductsTable extends Migration
{
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->decimal('price', 10, 2);
            $table->decimal('sale_price', 10, 2)->nullable();
            $table->string('sku')->unique();
            $table->integer('stock_quantity')->default(0);
            $table->boolean('manage_stock')->default(true);
            $table->enum('stock_status', ['in_stock', 'out_of_stock', 'on_backorder'])->default('in_stock');
            $table->decimal('weight', 8, 2)->nullable();
            $table->json('dimensions')->nullable(); // {length, width, height}
            $table->string('status')->default('draft'); // draft, published, private
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('brand_id')->nullable()->constrained()->onDelete('set null');
            $table->json('attributes')->nullable(); // Flexible product attributes
            $table->json('gallery')->nullable(); // Array of image URLs
            $table->string('featured_image')->nullable();
            $table->timestamp('published_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['status', 'published_at']);
            $table->index(['category_id', 'status']);
            $table->index('stock_status');
            $table->fullText(['name', 'description', 'short_description']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('products');
    }
}

// Migration for product variants
class CreateProductVariantsTable extends Migration
{
    public function up()
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('name'); // e.g., "Red - Large"
            $table->string('sku')->unique();
            $table->decimal('price_adjustment', 8, 2)->default(0); // +/- from base price
            $table->integer('stock_quantity')->default(0);
            $table->json('attributes'); // {color: "red", size: "large"}
            $table->string('image')->nullable();
            $table->boolean('is_default')->default(false);
            $table->timestamps();

            $table->index(['product_id', 'is_default']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('product_variants');
    }
}
                        </div>

                        <div class="best-practices">
                            <h4>✅ Best Practices</h4>
                            <ul>
                                <li>Use descriptive migration names that explain the change</li>
                                <li>Always provide rollback logic in the down() method</li>
                                <li>Add indexes for frequently queried columns</li>
                                <li>Use foreign key constraints to maintain data integrity</li>
                                <li>Consider using JSON columns for flexible attributes</li>
                            </ul>
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Exercise 2.1</h4>
                            <p><strong>Task:</strong> Design and implement a complete e-commerce database schema.</p>
                            <ul>
                                <li>Create migrations for products, categories, brands, and variants</li>
                                <li>Implement proper foreign key relationships</li>
                                <li>Add appropriate indexes for performance</li>
                                <li>Create a polymorphic reviews system</li>
                                <li>Test rollback functionality</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Lesson 2.2 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">2.2 Complex Eloquent Relationships</h3>
                            <span class="lesson-duration">3 hours</span>
                        </div>
                        <p class="lesson-description">
                            Master all types of Eloquent relationships including polymorphic relationships,
                            many-to-many with pivot data, and advanced querying techniques.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Polymorphic Relations</span>
                            <span class="topic-tag">Many-to-Many Pivots</span>
                            <span class="topic-tag">Has-Many-Through</span>
                            <span class="topic-tag">Morph-Many-To-Many</span>
                            <span class="topic-tag">Relationship Constraints</span>
                        </div>

                        <div class="code-example" data-lang="php">
<?php
// Advanced Relationship Examples

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name', 'slug', 'description', 'short_description', 'price',
        'sale_price', 'sku', 'stock_quantity', 'category_id', 'brand_id'
    ];

    protected $casts = [
        'dimensions' => 'array',
        'attributes' => 'array',
        'gallery' => 'array',
        'published_at' => 'datetime',
        'price' => 'decimal:2',
        'sale_price' => 'decimal:2',
    ];

    // Basic relationships
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    // Polymorphic relationships
    public function reviews(): MorphMany
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable');
    }

    // Many-to-many with pivot data
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)
                    ->withPivot(['sort_order', 'featured'])
                    ->withTimestamps()
                    ->orderByPivot('sort_order');
    }

    // Has-many-through relationship
    public function orderItems(): HasManyThrough
    {
        return $this->hasManyThrough(
            OrderItem::class,
            ProductVariant::class,
            'product_id', // Foreign key on variants table
            'variant_id', // Foreign key on order_items table
            'id', // Local key on products table
            'id' // Local key on variants table
        );
    }

    // Advanced scopes with relationships
    public function scopeWithCompleteData($query)
    {
        return $query->with([
            'category:id,name,slug',
            'brand:id,name,slug',
            'variants' => function ($query) {
                $query->where('stock_quantity', '>', 0);
            },
            'reviews' => function ($query) {
                $query->latest()->limit(5);
            },
            'tags:id,name,slug'
        ]);
    }

    public function scopePopular($query, $days = 30)
    {
        return $query->withCount(['reviews', 'orderItems'])
                    ->addSelect([
                        'avg_rating' => Review::selectRaw('AVG(rating)')
                            ->whereColumn('reviewable_id', 'products.id')
                            ->where('reviewable_type', Product::class)
                    ])
                    ->where('created_at', '>=', now()->subDays($days))
                    ->orderByDesc('order_items_count')
                    ->orderByDesc('avg_rating');
    }
}

// Polymorphic model example
class Review extends Model
{
    protected $fillable = ['rating', 'comment', 'user_id'];

    public function reviewable(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('reviewable_type', Product::class)
                    ->where('reviewable_id', $productId);
    }
}
                        </div>

                        <div class="pitfalls">
                            <h4>⚠️ Common Pitfalls</h4>
                            <ul>
                                <li>Not eager loading relationships (N+1 query problem)</li>
                                <li>Forgetting to add indexes on foreign keys</li>
                                <li>Using polymorphic relationships when a simple foreign key would suffice</li>
                                <li>Not considering database constraints for data integrity</li>
                                <li>Overusing complex relationships when simpler solutions exist</li>
                            </ul>
                        </div>
                    </div>

                    <div class="resources">
                        <h4>📚 Module 2 Resources</h4>
                        <ul>
                            <li><a href="https://laravel.com/docs/migrations">Laravel Migrations Documentation</a></li>
                            <li><a href="https://laravel.com/docs/eloquent-relationships">Eloquent Relationships Documentation</a></li>
                            <li><a href="https://laravel.com/docs/seeding">Database Seeding Documentation</a></li>
                            <li><a href="https://use-the-index-luke.com/">SQL Performance Explained</a></li>
                            <li><a href="https://laracasts.com/series/eloquent-techniques">Laracasts Eloquent Techniques</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Module 3: Filament Admin Panel -->
            <div class="module" id="module3">
                <div class="module-header">
                    <h2>Module 3: Filament Admin Panel</h2>
                    <p>Build sophisticated admin interfaces with advanced customization and widgets</p>
                    <div class="module-meta">
                        <span class="meta-item">⏱️ Duration: 18-22 Hours</span>
                        <span class="meta-item">📚 9 Lessons</span>
                        <span class="meta-item">🏗️ Project: CRM System</span>
                        <span class="meta-item">📊 Difficulty: Advanced</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h3>🎯 Learning Objectives</h3>
                        <ul>
                            <li>Master Filament resources with complex forms and tables</li>
                            <li>Create custom widgets for analytics and reporting</li>
                            <li>Implement advanced user management and permissions</li>
                            <li>Build custom pages and navigation structures</li>
                            <li>Integrate third-party services and APIs</li>
                            <li>Develop a complete CRM system with Filament</li>
                        </ul>
                    </div>

                    <!-- Lesson 3.1 -->
                    <div class="lesson">
                        <div class="lesson-header">
                            <h3 class="lesson-title">3.1 Advanced Filament Resources</h3>
                            <span class="lesson-duration">3 hours</span>
                        </div>
                        <p class="lesson-description">
                            Create sophisticated admin interfaces with complex forms, advanced table features,
                            custom actions, and bulk operations for efficient data management.
                        </p>
                        <div class="lesson-topics">
                            <span class="topic-tag">Resource Creation</span>
                            <span class="topic-tag">Complex Forms</span>
                            <span class="topic-tag">Advanced Tables</span>
                            <span class="topic-tag">Custom Actions</span>
                            <span class="topic-tag">Bulk Operations</span>
                        </div>

                        <div class="code-example" data-lang="php">
<?php
// Advanced Filament Resource Example

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'CRM';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Customer Information')
                    ->tabs([
                        Tab::make('Basic Information')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextInput::make('first_name')
                                            ->required()
                                            ->maxLength(255),
                                        TextInput::make('last_name')
                                            ->required()
                                            ->maxLength(255),
                                    ]),

                                Grid::make(2)
                                    ->schema([
                                        TextInput::make('email')
                                            ->email()
                                            ->required()
                                            ->unique(Customer::class, 'email', ignoreRecord: true),
                                        TextInput::make('phone')
                                            ->tel()
                                            ->maxLength(20),
                                    ]),

                                Select::make('status')
                                    ->options([
                                        'active' => 'Active',
                                        'inactive' => 'Inactive',
                                        'prospect' => 'Prospect',
                                        'churned' => 'Churned',
                                    ])
                                    ->required()
                                    ->live(),

                                DatePicker::make('birth_date')
                                    ->maxDate(now()->subYears(13)),

                                Textarea::make('notes')
                                    ->rows(3)
                                    ->columnSpanFull(),
                            ]),

                        Tab::make('Address Information')
                            ->schema([
                                TextInput::make('address.street')
                                    ->label('Street Address')
                                    ->maxLength(255),

                                Grid::make(3)
                                    ->schema([
                                        TextInput::make('address.city')
                                            ->label('City')
                                            ->maxLength(100),
                                        TextInput::make('address.state')
                                            ->label('State/Province')
                                            ->maxLength(100),
                                        TextInput::make('address.postal_code')
                                            ->label('Postal Code')
                                            ->maxLength(20),
                                    ]),

                                Select::make('address.country')
                                    ->label('Country')
                                    ->options([
                                        'US' => 'United States',
                                        'CA' => 'Canada',
                                        'UK' => 'United Kingdom',
                                        // Add more countries
                                    ])
                                    ->searchable(),
                            ]),

                        Tab::make('Business Information')
                            ->schema([
                                TextInput::make('company_name')
                                    ->maxLength(255),

                                Select::make('industry')
                                    ->options([
                                        'technology' => 'Technology',
                                        'healthcare' => 'Healthcare',
                                        'finance' => 'Finance',
                                        'education' => 'Education',
                                        'retail' => 'Retail',
                                        'other' => 'Other',
                                    ]),

                                TextInput::make('annual_revenue')
                                    ->numeric()
                                    ->prefix('$')
                                    ->maxValue(*********),

                                Select::make('lead_source')
                                    ->options([
                                        'website' => 'Website',
                                        'referral' => 'Referral',
                                        'social_media' => 'Social Media',
                                        'advertising' => 'Advertising',
                                        'trade_show' => 'Trade Show',
                                        'other' => 'Other',
                                    ]),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('full_name')
                    ->label('Name')
                    ->searchable(['first_name', 'last_name'])
                    ->sortable(['first_name', 'last_name'])
                    ->formatStateUsing(fn ($record) => $record->first_name . ' ' . $record->last_name),

                TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                TextColumn::make('phone')
                    ->searchable()
                    ->toggleable(),

                BadgeColumn::make('status')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'prospect',
                        'danger' => 'churned',
                        'secondary' => 'inactive',
                    ]),

                TextColumn::make('company_name')
                    ->searchable()
                    ->toggleable()
                    ->limit(30),

                TextColumn::make('orders_count')
                    ->counts('orders')
                    ->label('Orders')
                    ->sortable(),

                TextColumn::make('total_spent')
                    ->money('USD')
                    ->sortable()
                    ->summarize(Sum::make()->money('USD')),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'prospect' => 'Prospect',
                        'churned' => 'Churned',
                    ]),

                Filter::make('high_value')
                    ->label('High Value Customers')
                    ->query(fn (Builder $query): Builder => $query->where('total_spent', '>=', 10000)),

                DateFilter::make('created_at')
                    ->label('Registration Date'),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),

                Action::make('send_email')
                    ->icon('heroicon-o-envelope')
                    ->color('info')
                    ->form([
                        TextInput::make('subject')
                            ->required(),
                        Textarea::make('message')
                            ->required()
                            ->rows(4),
                    ])
                    ->action(function (Customer $record, array $data): void {
                        // Send email logic
                        Mail::to($record->email)->send(new CustomerEmail($data));

                        Notification::make()
                            ->title('Email sent successfully')
                            ->success()
                            ->send();
                    }),

                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),

                    BulkAction::make('update_status')
                        ->label('Update Status')
                        ->icon('heroicon-m-pencil-square')
                        ->form([
                            Select::make('status')
                                ->options([
                                    'active' => 'Active',
                                    'inactive' => 'Inactive',
                                    'prospect' => 'Prospect',
                                    'churned' => 'Churned',
                                ])
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data): void {
                            $records->each->update(['status' => $data['status']]);
                        })
                        ->deselectRecordsAfterCompletion(),

                    ExportBulkAction::make()
                        ->exporter(CustomerExporter::class),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            OrdersRelationManager::class,
            NotesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'view' => Pages\ViewCustomer::route('/{record}'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }
}
                        </div>

                        <div class="best-practices">
                            <h4>✅ Best Practices</h4>
                            <ul>
                                <li>Use tabs to organize complex forms into logical sections</li>
                                <li>Implement proper validation and unique constraints</li>
                                <li>Add helpful actions for common tasks (email, export, etc.)</li>
                                <li>Use relationship managers for related data</li>
                                <li>Implement bulk actions for efficiency</li>
                            </ul>
                        </div>

                        <div class="exercise">
                            <h4>🏋️ Exercise 3.1</h4>
                            <p><strong>Task:</strong> Create a comprehensive customer management resource.</p>
                            <ul>
                                <li>Build a customer resource with complex forms and validation</li>
                                <li>Add custom actions for common CRM tasks</li>
                                <li>Implement advanced filtering and search</li>
                                <li>Create relationship managers for orders and notes</li>
                                <li>Add bulk operations for status updates</li>
                            </ul>
                        </div>
                    </div>

                    <div class="resources">
                        <h4>📚 Module 3 Resources</h4>
                        <ul>
                            <li><a href="https://filamentphp.com/docs">Filament Documentation</a></li>
                            <li><a href="https://filamentphp.com/docs/panels/resources">Filament Resources Guide</a></li>
                            <li><a href="https://filamentphp.com/docs/panels/dashboard">Dashboard & Widgets</a></li>
                            <li><a href="https://filamentphp.com/plugins">Filament Plugin Directory</a></li>
                            <li><a href="https://github.com/filamentphp/demo">Filament Demo Application</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Course Summary & Completion -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; margin: 40px 0; border-radius: 15px; text-align: center;">
                <h2 style="color: white; margin-bottom: 20px; font-size: 2.5em;">🎉 Course Complete!</h2>
                <p style="font-size: 1.3em; margin-bottom: 30px; opacity: 0.9;">
                    You've mastered Laravel full-stack development with this comprehensive curriculum!
                </p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;">
                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                        <h3 style="color: white; margin-bottom: 10px;">🏆 Skills Mastered</h3>
                        <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                            <li>Laravel MVC Architecture</li>
                            <li>Advanced Database Design</li>
                            <li>Filament Admin Panels</li>
                            <li>Authentication & Security</li>
                            <li>Modern Frontend Development</li>
                            <li>Professional Best Practices</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                        <h3 style="color: white; margin-bottom: 10px;">🚀 Projects Built</h3>
                        <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                            <li>Blog System with Comments</li>
                            <li>E-commerce Product Catalog</li>
                            <li>CRM Customer Management</li>
                            <li>User Authentication System</li>
                            <li>Admin Dashboard with Analytics</li>
                            <li>Responsive Frontend Interfaces</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                        <h3 style="color: white; margin-bottom: 10px;">📈 Next Steps</h3>
                        <ul style="text-align: left; color: rgba(255,255,255,0.9);">
                            <li>Build your own Laravel applications</li>
                            <li>Contribute to open-source projects</li>
                            <li>Explore Laravel ecosystem packages</li>
                            <li>Learn advanced DevOps and deployment</li>
                            <li>Mentor other developers</li>
                            <li>Stay updated with Laravel releases</li>
                        </ul>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <button onclick="window.print()" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; margin: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                        📄 Save Course as PDF
                    </button>
                    <button onclick="generateCertificate()" style="background: #ffc107; color: #212529; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; margin: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                        🏅 Generate Certificate
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="progress-tracker">
            <h4>📊 Course Progress</h4>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <p id="progress-text">0% Complete</p>

            <ul class="module-list">
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="1">
                    <span>Module 1: Laravel Fundamentals</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="2">
                    <span>Module 2: Database Design</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="3">
                    <span>Module 3: Filament Admin Panel</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="4">
                    <span>Module 4: Authentication & Security</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="5">
                    <span>Module 5: Frontend Development</span>
                </li>
                <li>
                    <input type="checkbox" class="module-checkbox" data-module="6">
                    <span>Module 6: Best Practices</span>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // Progress tracking functionality
        function updateProgress() {
            const checkboxes = document.querySelectorAll('.module-checkbox');
            const checked = document.querySelectorAll('.module-checkbox:checked').length;
            const total = checkboxes.length;
            const percentage = Math.round((checked / total) * 100);

            document.getElementById('progress-fill').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = `${percentage}% Complete (${checked}/${total})`;

            // Save progress to localStorage
            const progress = Array.from(checkboxes).map(cb => cb.checked);
            localStorage.setItem('laravel-mastery-progress', JSON.stringify(progress));
        }

        // Load saved progress
        function loadProgress() {
            const saved = localStorage.getItem('laravel-mastery-progress');
            if (saved) {
                const progress = JSON.parse(saved);
                const checkboxes = document.querySelectorAll('.module-checkbox');
                progress.forEach((checked, index) => {
                    if (checkboxes[index]) {
                        checkboxes[index].checked = checked;
                    }
                });
                updateProgress();
            }
        }

        // Certificate generation
        function generateCertificate() {
            const name = prompt('Enter your name for the certificate:');
            if (name) {
                const completedModules = document.querySelectorAll('.module-checkbox:checked').length;
                const totalModules = document.querySelectorAll('.module-checkbox').length;

                if (completedModules === totalModules) {
                    alert(`🎉 Congratulations ${name}! Your Laravel Full-Stack Mastery Certificate is ready. In a real course, this would generate a PDF certificate.`);
                } else {
                    alert(`You need to complete all modules to receive your certificate. Current progress: ${completedModules}/${totalModules}`);
                }
            }
        }

        // Copy code functionality
        document.querySelectorAll('.code-example').forEach(block => {
            const button = document.createElement('button');
            button.textContent = 'Copy';
            button.style.cssText = 'position: absolute; top: 10px; right: 15px; background: #4a5568; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px; z-index: 10;';
            block.style.position = 'relative';
            block.appendChild(button);

            button.addEventListener('click', () => {
                const text = block.textContent.replace('Copy', '').trim();
                navigator.clipboard.writeText(text).then(() => {
                    button.textContent = 'Copied!';
                    button.style.background = '#28a745';
                    setTimeout(() => {
                        button.textContent = 'Copy';
                        button.style.background = '#4a5568';
                    }, 2000);
                });
            });
        });

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            loadProgress();

            document.querySelectorAll('.module-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateProgress);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>