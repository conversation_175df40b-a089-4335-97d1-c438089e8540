<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Complaints Management System - Step by Step Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
            page-break-before: auto;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .step {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            page-break-inside: avoid;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .step-title {
            display: inline-block;
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
            margin: 10px 0;
        }
        code {
            background: #ecf0f1;
            color: #e74c3c;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .command {
            background: #27ae60;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        .file-path {
            background: #f39c12;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .feature h4 {
            color: #2980b9;
            margin-top: 0;
        }
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .step { page-break-inside: avoid; }
            h2 { page-break-before: always; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Complete Complaints Management System<br>Step-by-Step Development Guide</h1>

        <div class="success">
            <h3>🎯 What You'll Build</h3>
            <p>A comprehensive complaints management system with Laravel, Filament, and Tailwind CSS featuring user management, complaint tracking, admin panel, student portal, and ISO 21001 compliance features.</p>
        </div>

        <div class="features">
            <div class="feature">
                <h4>✅ User Management</h4>
                <p>Multi-role system with Admin, Teacher, and Student roles</p>
            </div>
            <div class="feature">
                <h4>✅ Admin Panel</h4>
                <p>Powerful Filament-based administration interface</p>
            </div>
            <div class="feature">
                <h4>✅ Student Portal</h4>
                <p>Dedicated portal for complaint submission and tracking</p>
            </div>
            <div class="feature">
                <h4>✅ Complaint System</h4>
                <p>Complete complaint lifecycle management</p>
            </div>
            <div class="feature">
                <h4>✅ Dashboard & Analytics</h4>
                <p>Real-time statistics and reporting widgets</p>
            </div>
            <div class="feature">
                <h4>✅ Responsive Design</h4>
                <p>Mobile-friendly interface with Tailwind CSS</p>
            </div>
        </div>

        <div class="toc">
            <h3>📋 Table of Contents</h3>
            <ul>
                <li><a href="#prerequisites">Prerequisites & Requirements</a></li>
                <li><a href="#step1">Step 1: Create Laravel Project</a></li>
                <li><a href="#step2">Step 2: Install Required Packages</a></li>
                <li><a href="#step3">Step 3: Environment Configuration</a></li>
                <li><a href="#step4">Step 4: Database Setup</a></li>
                <li><a href="#step5">Step 5: Install Filament</a></li>
                <li><a href="#step6">Step 6: Spatie Permission Setup</a></li>
                <li><a href="#step7">Step 7: Create Models & Migrations</a></li>
                <li><a href="#step8">Step 8: Update User Model</a></li>
                <li><a href="#step9">Step 9: Create Filament Resources</a></li>
                <li><a href="#step10">Step 10: Create Dashboard Widgets</a></li>
                <li><a href="#step11">Step 11: Create Seeders</a></li>
                <li><a href="#step12">Step 12: Create Student Portal</a></li>
                <li><a href="#step13">Step 13: Create Middleware</a></li>
                <li><a href="#step14">Step 14: Create Views</a></li>
                <li><a href="#step15">Step 15: Setup Routes</a></li>
                <li><a href="#step16">Step 16: Install Authentication</a></li>
                <li><a href="#step17">Step 17: Run Migrations</a></li>
                <li><a href="#step18">Step 18: Create Admin User</a></li>
                <li><a href="#step19">Step 19: Final Setup</a></li>
                <li><a href="#step20">Step 20: Access Application</a></li>
            </ul>
        </div>

        <div id="prerequisites" class="step">
            <div class="step-number">📋</div>
            <div class="step-title">Prerequisites & Requirements</div>
            <div class="note">
                <h4>System Requirements</h4>
                <ul>
                    <li><strong>PHP 8.2 or higher</strong> - Latest stable version recommended</li>
                    <li><strong>Composer</strong> - PHP dependency manager</li>
                    <li><strong>Node.js & NPM</strong> - For frontend asset compilation (optional)</li>
                    <li><strong>SQLite extension</strong> - Must be enabled in PHP</li>
                    <li><strong>Git</strong> - Version control (recommended)</li>
                </ul>
            </div>

            <h4>Verify Installation</h4>
            <div class="command">php --version</div>
            <div class="command">composer --version</div>
            <div class="command">node --version && npm --version</div>
        </div>

        <div id="step1" class="step">
            <div class="step-number">1</div>
            <div class="step-title">Create New Laravel Project</div>

            <p>Start by creating a fresh Laravel project using Composer:</p>

            <div class="command">composer create-project laravel/laravel complaints-management-system</div>
            <div class="command">cd complaints-management-system</div>

            <div class="note">
                <strong>Note:</strong> This will create a new Laravel project in the <code>complaints-management-system</code> directory with all the latest Laravel features and dependencies.
            </div>
        </div>

        <div id="step2" class="step">
            <div class="step-number">2</div>
            <div class="step-title">Install Required Packages</div>

            <p>Install the essential packages for our complaints management system:</p>

            <div class="command">composer require filament/filament:"^3.0" spatie/laravel-permission</div>

            <div class="note">
                <h4>Package Overview:</h4>
                <ul>
                    <li><strong>Filament v3</strong> - Modern admin panel with rich UI components</li>
                    <li><strong>Spatie Permission</strong> - Role and permission management system</li>
                </ul>
            </div>
        </div>

        <div id="step3" class="step">
            <div class="step-number">3</div>
            <div class="step-title">Environment Configuration</div>

            <p>Configure your environment by editing the <span class="file-path">.env</span> file:</p>

            <pre>APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

PERMISSION_CACHE_ENABLED=false</pre>

            <div class="warning">
                <strong>Important:</strong> We're using SQLite for simplicity. The <code>PERMISSION_CACHE_ENABLED=false</code> setting prevents cache-related issues during development.
            </div>
        </div>

        <div id="step4" class="step">
            <div class="step-number">4</div>
            <div class="step-title">Database Setup</div>

            <p>Generate application key and create the SQLite database:</p>

            <div class="command">php artisan key:generate</div>

            <p><strong>For Linux/Mac:</strong></p>
            <div class="command">touch database/database.sqlite</div>

            <p><strong>For Windows:</strong></p>
            <div class="command">echo. > database\database.sqlite</div>

            <div class="note">
                <strong>Tip:</strong> SQLite is perfect for development and small to medium applications. The database file will be created in the <code>database/</code> directory.
            </div>
        </div>

        <div id="step5" class="step">
            <div class="step-number">5</div>
            <div class="step-title">Install Filament</div>

            <p>Install and configure Filament admin panel:</p>

            <div class="command">php artisan filament:install --panels</div>

            <div class="note">
                This command will:
                <ul>
                    <li>Install Filament panel provider</li>
                    <li>Publish configuration files</li>
                    <li>Set up the admin panel structure</li>
                </ul>
            </div>
        </div>

        <div id="step6" class="step">
            <div class="step-number">6</div>
            <div class="step-title">Spatie Permission Setup</div>

            <p>Publish the Spatie Permission migrations:</p>

            <div class="command">php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"</div>

            <div class="note">
                This will create migration files for roles and permissions tables that are essential for our user management system.
            </div>
        </div>

        <div id="step7" class="step">
            <div class="step-number">7</div>
            <div class="step-title">Create Models and Migrations</div>

            <h4>7.1 Add Fields to Users Table</h4>
            <p>Create migration to add additional fields to users table:</p>

            <div class="command">php artisan make:migration add_fields_to_users_table --table=users</div>

            <p>Edit <span class="file-path">database/migrations/xxxx_add_fields_to_users_table.php</span>:</p>

            <pre><?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('department')->nullable();
            $table->string('user_type')->default('student');
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->timestamp('last_login_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['department', 'user_type', 'phone', 'address', 'last_login_at']);
        });
    }
};</pre>

            <h4>7.2 Create Category Model</h4>
            <div class="command">php artisan make:model Category -m</div>

            <p>Edit <span class="file-path">app/Models/Category.php</span>:</p>

            <pre><?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'description', 'color', 'is_active', 'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class);
    }
}</pre>

            <p>Edit <span class="file-path">database/migrations/xxxx_create_categories_table.php</span>:</p>

            <pre><?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('color')->default('#3B82F6');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};</pre>

            <h4>7.3 Create Complaint Model</h4>
            <div class="command">php artisan make:model Complaint -m</div>

            <div class="note">
                <strong>Note:</strong> The Complaint model is the core of our system. It will handle complaint submission, tracking, and status management.
            </div>
        </div>

        <div id="step8" class="step">
            <div class="step-number">8</div>
            <div class="step-title">Update User Model</div>

            <p>Update the User model to work with Filament and Spatie Permission:</p>

            <p>Edit <span class="file-path">app/Models/User.php</span>:</p>

            <pre><?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name', 'email', 'password', 'department', 'user_type',
        'phone', 'address', 'last_login_at',
    ];

    protected $hidden = ['password', 'remember_token'];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return $this->hasAnyRole(['admin', 'teacher']);
    }

    public function submittedComplaints(): HasMany
    {
        return $this->hasMany(Complaint::class, 'submitted_by');
    }

    public function assignedComplaints(): HasMany
    {
        return $this->hasMany(Complaint::class, 'assigned_to');
    }
}</pre>

            <div class="warning">
                <strong>Important:</strong> The <code>canAccessPanel</code> method restricts admin panel access to users with 'admin' or 'teacher' roles only.
            </div>
        </div>

        <div id="step9" class="step">
            <div class="step-number">9</div>
            <div class="step-title">Create Filament Resources</div>

            <h4>9.1 Create Category Resource</h4>
            <div class="command">php artisan make:filament-resource Category</div>

            <h4>9.2 Create Complaint Resource</h4>
            <div class="command">php artisan make:filament-resource Complaint</div>

            <h4>9.3 Create User Resource</h4>
            <div class="command">php artisan make:filament-resource User</div>

            <div class="note">
                <strong>Tip:</strong> Filament resources automatically generate CRUD interfaces for your models. You can customize forms, tables, and actions in the generated resource files.
            </div>
        </div>

        <div id="step10" class="step">
            <div class="step-number">10</div>
            <div class="step-title">Create Dashboard Widgets</div>

            <h4>10.1 Create Statistics Widget</h4>
            <div class="command">php artisan make:filament-widget ComplaintsOverview --stats-overview</div>

            <h4>10.2 Create Chart Widget</h4>
            <div class="command">php artisan make:filament-widget ComplaintVolumeWidget --chart</div>

            <div class="note">
                These widgets will provide real-time analytics and visual representations of complaint data on the admin dashboard.
            </div>
        </div>

        <div id="step11" class="step">
            <div class="step-number">11</div>
            <div class="step-title">Create Database Seeders</div>

            <h4>11.1 Create Category Seeder</h4>
            <div class="command">php artisan make:seeder CategorySeeder</div>

            <h4>11.2 Create User Seeder</h4>
            <div class="command">php artisan make:seeder UserSeeder</div>

            <p>Update <span class="file-path">database/seeders/DatabaseSeeder.php</span>:</p>

            <pre><?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            CategorySeeder::class,
            UserSeeder::class,
        ]);
    }
}</pre>

            <div class="note">
                Seeders will populate your database with initial data including default categories and demo users for testing.
            </div>
        </div>

        <div id="step12" class="step">
            <div class="step-number">12</div>
            <div class="step-title">Create Student Portal</div>

            <h4>12.1 Create Controllers</h4>
            <div class="command">php artisan make:controller StudentDashboardController</div>
            <div class="command">php artisan make:controller StudentPortalController</div>

            <h4>12.2 Create Views Directory Structure</h4>
            <div class="command">mkdir -p resources/views/layouts</div>
            <div class="command">mkdir -p resources/views/student/complaints</div>

            <div class="note">
                The student portal provides a dedicated interface for students to submit complaints, track progress, and communicate with administrators.
            </div>
        </div>

        <div id="step13" class="step">
            <div class="step-number">13</div>
            <div class="step-title">Create Middleware</div>

            <h4>13.1 Create Student Middleware</h4>
            <div class="command">php artisan make:middleware EnsureUserIsStudent</div>

            <h4>13.2 Create Teacher Middleware</h4>
            <div class="command">php artisan make:middleware EnsureUserIsTeacher</div>

            <h4>13.3 Register Middleware</h4>
            <p>Update <span class="file-path">bootstrap/app.php</span> to register the middleware aliases:</p>

            <pre>->withMiddleware(function (Middleware $middleware) {
    $middleware->alias([
        'student' => \App\Http\Middleware\EnsureUserIsStudent::class,
        'teacher' => \App\Http\Middleware\EnsureUserIsTeacher::class,
    ]);
})</pre>

            <div class="warning">
                <strong>Security:</strong> Middleware ensures that only authorized users can access specific parts of the application.
            </div>
        </div>

        <div id="step14" class="step">
            <div class="step-number">14</div>
            <div class="step-title">Create Views and Templates</div>

            <h4>14.1 Create Student Layout</h4>
            <p>Create <span class="file-path">resources/views/layouts/student.blade.php</span> with responsive navigation and styling.</p>

            <h4>14.2 Create Student Dashboard</h4>
            <p>Create <span class="file-path">resources/views/student/dashboard.blade.php</span> with statistics cards and quick complaint submission.</p>

            <h4>14.3 Create Complaint Views</h4>
            <ul>
                <li><span class="file-path">resources/views/student/complaints/index.blade.php</span> - List all complaints</li>
                <li><span class="file-path">resources/views/student/complaints/create.blade.php</span> - Submit new complaint</li>
                <li><span class="file-path">resources/views/student/complaints/show.blade.php</span> - View complaint details</li>
            </ul>

            <div class="note">
                All views use Tailwind CSS for responsive design and include proper form validation and user feedback.
            </div>
        </div>

        <div id="step15" class="step">
            <div class="step-number">15</div>
            <div class="step-title">Setup Routes</div>

            <p>Configure routes in <span class="file-path">routes/web.php</span>:</p>

            <pre><?php

use App\Http\Controllers\StudentDashboardController;
use App\Http\Controllers\StudentPortalController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    if (auth()->check()) {
        $user = auth()->user();
        if ($user->hasAnyRole(['admin', 'teacher'])) {
            return redirect('/admin');
        } else {
            return redirect('/student/dashboard');
        }
    }
    return redirect('/login');
});

// Student Portal Routes
Route::prefix('student')->name('student.')->middleware(['auth', 'verified', 'student'])->group(function () {
    Route::get('/dashboard', [StudentDashboardController::class, 'index'])->name('dashboard');
    Route::post('/dashboard/quick-submit', [StudentDashboardController::class, 'quickSubmit'])->name('dashboard.quick-submit');

    Route::get('/complaints', [StudentPortalController::class, 'index'])->name('complaints');
    Route::get('/complaints/create', [StudentPortalController::class, 'create'])->name('complaints.create');
    Route::post('/complaints', [StudentPortalController::class, 'store'])->name('complaints.store');
    Route::get('/complaints/{complaint}', [StudentPortalController::class, 'show'])->name('complaints.show');

    Route::post('/complaints/{complaint}/comments', [StudentPortalController::class, 'addComment'])->name('complaints.comments.store');
    Route::post('/complaints/{complaint}/reopen', [StudentPortalController::class, 'reopen'])->name('complaints.reopen');
    Route::post('/complaints/{complaint}/rating', [StudentPortalController::class, 'submitRating'])->name('complaints.rating');
});

require __DIR__.'/auth.php';</pre>

            <div class="note">
                Routes are organized with proper middleware protection and RESTful naming conventions.
            </div>
        </div>

        <div id="step16" class="step">
            <div class="step-number">16</div>
            <div class="step-title">Install Laravel Breeze for Authentication</div>

            <p>Install and configure Laravel Breeze for authentication:</p>

            <div class="command">composer require laravel/breeze --dev</div>
            <div class="command">php artisan breeze:install blade</div>
            <div class="command">npm install && npm run build</div>

            <div class="note">
                Laravel Breeze provides a simple authentication system with login, registration, and password reset functionality.
            </div>
        </div>

        <div id="step17" class="step">
            <div class="step-number">17</div>
            <div class="step-title">Run Migrations and Seeders</div>

            <p>Execute database migrations and populate with initial data:</p>

            <div class="command">php artisan migrate:fresh --seed</div>

            <div class="warning">
                <strong>Warning:</strong> The <code>--fresh</code> flag will drop all tables and recreate them. Only use this during development.
            </div>
        </div>

        <div id="step18" class="step">
            <div class="step-number">18</div>
            <div class="step-title">Create Admin User Command</div>

            <p>Create a custom Artisan command for creating admin users:</p>

            <div class="command">php artisan make:command CreateAdminUser</div>

            <p>Edit <span class="file-path">app/Console/Commands/CreateAdminUser.php</span>:</p>

            <pre><?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class CreateAdminUser extends Command
{
    protected $signature = 'make:admin {email} {password}';
    protected $description = 'Create an admin user';

    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');

        $adminRole = Role::firstOrCreate(['name' => 'admin']);

        $admin = User::firstOrCreate(
            ['email' => $email],
            [
                'name' => 'Administrator',
                'password' => Hash::make($password),
                'email_verified_at' => now(),
                'department' => 'IT',
                'user_type' => 'admin',
            ]
        );

        $admin->assignRole($adminRole);

        $this->info("Admin user created successfully!");
        $this->info("Email: {$email}");
    }
}</pre>
        </div>

        <div id="step19" class="step">
            <div class="step-number">19</div>
            <div class="step-title">Final Setup Commands</div>

            <p>Execute the final setup commands:</p>

            <div class="command">php artisan make:admin <EMAIL> password</div>
            <div class="command">php artisan storage:link</div>
            <div class="command">php artisan config:clear</div>
            <div class="command">php artisan cache:clear</div>

            <div class="success">
                <strong>Success!</strong> Your application is now ready to run.
            </div>
        </div>

        <div id="step20" class="step">
            <div class="step-number">20</div>
            <div class="step-title">Access the Application</div>

            <p>Start the development server:</p>

            <div class="command">php artisan serve</div>

            <h4>Access URLs:</h4>
            <ul>
                <li><strong>Admin Panel:</strong> <a href="http://localhost:8000/admin" target="_blank">http://localhost:8000/admin</a></li>
                <li><strong>Student Portal:</strong> <a href="http://localhost:8000/student/dashboard" target="_blank">http://localhost:8000/student/dashboard</a></li>
                <li><strong>Login Page:</strong> <a href="http://localhost:8000/login" target="_blank">http://localhost:8000/login</a></li>
            </ul>

            <h4>Demo Credentials:</h4>
            <div class="note">
                <ul>
                    <li><strong>Admin:</strong> <EMAIL> / password</li>
                    <li><strong>Teacher:</strong> <EMAIL> / password</li>
                    <li><strong>Student:</strong> <EMAIL> / password</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <div class="step-number">🎉</div>
            <div class="step-title">Congratulations!</div>

            <div class="success">
                <h3>You have successfully built a complete Complaints Management System!</h3>

                <h4>Features Implemented:</h4>
                <ul>
                    <li>✅ <strong>User Management</strong> - Multi-role system with Admin, Teacher, Student</li>
                    <li>✅ <strong>Admin Panel</strong> - Powerful Filament-based interface</li>
                    <li>✅ <strong>Student Portal</strong> - Dedicated complaint submission and tracking</li>
                    <li>✅ <strong>Complaint System</strong> - Complete lifecycle management</li>
                    <li>✅ <strong>Dashboard Analytics</strong> - Real-time statistics and charts</li>
                    <li>✅ <strong>Authentication</strong> - Secure login and registration</li>
                    <li>✅ <strong>Responsive Design</strong> - Mobile-friendly interface</li>
                    <li>✅ <strong>Database Management</strong> - Proper relationships and migrations</li>
                </ul>

                <h4>Next Steps:</h4>
                <ul>
                    <li>🔧 Customize the design and branding</li>
                    <li>📧 Add email notifications</li>
                    <li>📎 Implement file attachments</li>
                    <li>📊 Create advanced reporting</li>
                    <li>🔒 Add ISO 21001 compliance features</li>
                    <li>🚀 Deploy to production</li>
                </ul>
            </div>

            <div class="note">
                <h4>📚 Additional Resources:</h4>
                <ul>
                    <li><a href="https://laravel.com/docs" target="_blank">Laravel Documentation</a></li>
                    <li><a href="https://filamentphp.com/docs" target="_blank">Filament Documentation</a></li>
                    <li><a href="https://spatie.be/docs/laravel-permission" target="_blank">Spatie Permission Documentation</a></li>
                    <li><a href="https://tailwindcss.com/docs" target="_blank">Tailwind CSS Documentation</a></li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; border-top: 2px solid #3498db;">
            <h3>📄 Download This Guide</h3>
            <p>To save this guide as a PDF, use your browser's print function (Ctrl+P) and select "Save as PDF".</p>
            <button onclick="window.print()" style="background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
                🖨️ Print / Save as PDF
            </button>
        </div>
    </div>

    <script>
        // Add print functionality
        function printPDF() {
            window.print();
        }

        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
