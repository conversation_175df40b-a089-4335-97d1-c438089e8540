# SQLite Database Guide

## Overview

The Complaints Management System now uses SQLite as the default database for optimal performance and ease of deployment. This guide covers everything you need to know about using SQLite with the system.

## Why SQLite?

✅ **Zero Configuration** - No database server setup required  
✅ **High Performance** - Faster than client-server databases for most use cases  
✅ **ACID Compliant** - Full transaction support  
✅ **Portable** - Single file database  
✅ **Reliable** - Used by major applications worldwide  
✅ **Full SQL Support** - Complete SQL implementation  

## Quick Setup

### Automated Setup (Recommended)
```bash
SQLITE-SETUP.bat
```

This script will:
- Configure SQLite database
- Optimize performance settings
- Run migrations and seeders
- Create demo users
- Start the development server

## Manual Configuration

### 1. Environment Configuration

Update your `.env` file:
```env
DB_CONNECTION=sqlite
SESSION_DRIVER=database
CACHE_DRIVER=database
QUEUE_CONNECTION=database
```

### 2. Create Database File
```bash
# Windows
echo. > database\database.sqlite

# Linux/Mac
touch database/database.sqlite
```

### 3. Run Migrations
```bash
php artisan migrate:fresh --seed
```

## Performance Optimizations

The system automatically applies these SQLite optimizations:

### WAL Mode (Write-Ahead Logging)
- Enables concurrent reads during writes
- Better performance for web applications
- Automatic checkpoint management

### Memory Optimizations
- **Cache Size**: 10,000 pages (~40MB)
- **Memory Mapping**: 256MB for faster I/O
- **Temp Storage**: In-memory for better performance

### Connection Settings
- **Busy Timeout**: 30 seconds for concurrent access
- **Synchronous Mode**: NORMAL for balanced performance/safety
- **Foreign Keys**: Enabled for data integrity

## Maintenance Commands

### Daily Maintenance
```bash
# Update query statistics
php artisan sqlite:maintenance --analyze

# Create backup
php artisan sqlite:maintenance --backup
```

### Weekly Maintenance
```bash
# Reclaim unused space
php artisan sqlite:maintenance --vacuum

# Full maintenance (all operations)
php artisan sqlite:maintenance
```

### Database Integrity
```bash
# Check database integrity
php artisan sqlite:maintenance --integrity
```

## Backup and Recovery

### Automatic Backups
- Daily backups are created automatically
- Stored in `storage/backups/`
- Old backups cleaned up after 30 days

### Manual Backup
```bash
# Create immediate backup
php artisan sqlite:maintenance --backup

# Or copy the database file
cp database/database.sqlite backup/database_backup.sqlite
```

### Recovery
```bash
# Restore from backup
cp backup/database_backup.sqlite database/database.sqlite
```

## Monitoring and Debugging

### Database Size
```bash
# Check database file size
ls -lh database/database.sqlite

# Or on Windows
dir database\database.sqlite
```

### Query Performance
Enable query logging in `.env`:
```env
DB_LOG_QUERIES=true
SQLITE_QUERY_LOG=true
```

### Database Statistics
```sql
-- Connect to database
sqlite3 database/database.sqlite

-- Check database info
.dbinfo

-- Show table sizes
.schema

-- Analyze query plans
EXPLAIN QUERY PLAN SELECT * FROM complaints;
```

## Production Considerations

### File Permissions
```bash
# Set proper permissions
chmod 664 database/database.sqlite
chmod 755 database/
```

### Backup Strategy
1. **Daily automated backups** (already configured)
2. **Pre-deployment backups** before updates
3. **Off-site backup storage** for production

### Scaling Considerations
SQLite is suitable for:
- ✅ Up to 100,000 complaints
- ✅ Concurrent users: 50-100
- ✅ Database size: Up to 1TB
- ✅ Read-heavy workloads

Consider MySQL/PostgreSQL for:
- ❌ Very high concurrent writes (>100 simultaneous)
- ❌ Multiple application servers
- ❌ Complex replication needs

## Migration to MySQL (if needed)

### 1. Export Data
```bash
# Export SQLite to SQL
sqlite3 database/database.sqlite .dump > backup.sql
```

### 2. Update Configuration
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=complaints_management
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 3. Import Data
```bash
# Create MySQL database
mysql -u root -p -e "CREATE DATABASE complaints_management;"

# Run migrations
php artisan migrate:fresh

# Import data (may need manual adjustment)
mysql -u root -p complaints_management < backup.sql
```

## Troubleshooting

### Database Locked Error
```bash
# Check for long-running processes
ps aux | grep php

# Kill stuck processes
kill -9 [process_id]

# Or restart web server
php artisan serve
```

### Corruption Issues
```bash
# Check integrity
php artisan sqlite:maintenance --integrity

# If corrupted, restore from backup
cp storage/backups/database_[date].sqlite database/database.sqlite
```

### Performance Issues
```bash
# Run maintenance
php artisan sqlite:maintenance

# Check database size
ls -lh database/database.sqlite

# Consider VACUUM if database is large
php artisan sqlite:maintenance --vacuum
```

## Best Practices

### Development
1. Use automated setup scripts
2. Regular maintenance commands
3. Monitor database size
4. Keep backups current

### Production
1. Set up monitoring for database size
2. Implement backup verification
3. Monitor query performance
4. Plan for scaling if needed

### Security
1. Proper file permissions (664 for database file)
2. Regular security updates
3. Backup encryption for sensitive data
4. Access control at application level

## Configuration Reference

### SQLite Pragmas Applied
```sql
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = 268435456;
PRAGMA busy_timeout = 30000;
PRAGMA foreign_keys = ON;
PRAGMA optimize;
```

### Environment Variables
```env
SQLITE_JOURNAL_MODE=WAL
SQLITE_SYNCHRONOUS=NORMAL
SQLITE_CACHE_SIZE=10000
SQLITE_BUSY_TIMEOUT=30000
SQLITE_BACKUP_ENABLED=true
SQLITE_BACKUP_RETENTION=30
```

## Support

For SQLite-specific issues:
1. Check this guide first
2. Run maintenance commands
3. Verify file permissions
4. Check available disk space
5. Review Laravel logs in `storage/logs/`

The system is optimized for SQLite and should provide excellent performance for most use cases.
