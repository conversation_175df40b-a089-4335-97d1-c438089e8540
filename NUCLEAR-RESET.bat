@echo off
echo ========================================
echo NUCLEAR RESET - Starting Completely Fresh
echo ========================================

echo Step 1: Deleting old database and cache...
if exist database\database.sqlite del database\database.sqlite
if exist bootstrap\cache\*.php del bootstrap\cache\*.php
if exist storage\framework\cache\data rmdir /s /q storage\framework\cache\data
if exist storage\framework\sessions rmdir /s /q storage\framework\sessions
if exist storage\framework\views rmdir /s /q storage\framework\views
if exist storage\logs\*.log del storage\logs\*.log
echo ✅ Old files deleted

echo Step 2: Creating fresh .env file...
(
echo APP_NAME="Complaints Management System"
echo APP_ENV=local
echo APP_KEY=
echo APP_DEBUG=true
echo APP_URL=http://localhost
echo.
echo LOG_CHANNEL=stack
echo LOG_LEVEL=debug
echo.
echo DB_CONNECTION=sqlite
echo.
echo BROADCAST_DRIVER=log
echo CACHE_DRIVER=file
echo FILESYSTEM_DISK=local
echo QUEUE_CONNECTION=sync
echo SESSION_DRIVER=file
echo SESSION_LIFETIME=120
echo.
echo MAIL_MAILER=log
echo MAIL_FROM_ADDRESS="<EMAIL>"
echo MAIL_FROM_NAME="${APP_NAME}"
) > .env
echo ✅ Fresh .env created

echo Step 3: Creating new database...
echo. > database\database.sqlite
echo ✅ New database created

echo Step 4: Generating new app key...
php artisan key:generate --force
echo ✅ App key generated

echo Step 5: Clearing everything...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
echo ✅ All caches cleared

echo Step 6: Creating database tables...
php artisan migrate:fresh --force
echo ✅ Tables created

echo Step 7: Creating admin user directly in database...
php artisan tinker --execute="
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

// Create roles
try {
    Role::create(['name' => 'admin']);
    Role::create(['name' => 'teacher']); 
    Role::create(['name' => 'student']);
    echo 'Roles created\n';
} catch (Exception \$e) {
    echo 'Roles already exist\n';
}

// Delete any existing users
DB::table('users')->truncate();
echo 'Users table cleared\n';

// Create admin user
\$admin = new User();
\$admin->name = 'Admin User';
\$admin->email = '<EMAIL>';
\$admin->password = Hash::make('123456');
\$admin->email_verified_at = now();
\$admin->department = 'IT';
\$admin->user_type = 'admin';
\$admin->save();
\$admin->assignRole('admin');

echo 'Admin created: <EMAIL> / 123456\n';

// Create student user
\$student = new User();
\$student->name = 'Student User';
\$student->email = '<EMAIL>';
\$student->password = Hash::make('123456');
\$student->email_verified_at = now();
\$student->department = 'CS';
\$student->user_type = 'student';
\$student->save();
\$student->assignRole('student');

echo 'Student created: <EMAIL> / 123456\n';
"

echo.
echo ========================================
echo FRESH SETUP COMPLETE!
echo ========================================
echo.
echo NEW LOGIN CREDENTIALS:
echo • Admin: <EMAIL> / 123456
echo • Student: <EMAIL> / 123456
echo.
echo URLs:
echo • Admin: http://localhost:8000/admin
echo • Student: http://localhost:8000/student/dashboard
echo.
echo Starting server...
pause

php artisan serve
