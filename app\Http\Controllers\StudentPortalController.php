<?php

namespace App\Http\Controllers;

use App\Models\Complaint;
use App\Models\ComplaintAttachment;
use App\Models\ComplaintCategory;
use App\Models\ComplaintComment;
use App\Models\ComplaintStatusHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class StudentPortalController extends Controller
{
    /**
     * Display the student dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();
        $complaints = Complaint::where('submitted_by', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $newCount = Complaint::where('submitted_by', $user->id)
            ->where('status', 'new')
            ->count();

        $inProgressCount = Complaint::where('submitted_by', $user->id)
            ->whereIn('status', ['assigned', 'in_progress'])
            ->count();

        $resolvedCount = Complaint::where('submitted_by', $user->id)
            ->whereIn('status', ['resolved', 'closed'])
            ->count();

        return view('student.complaints', compact('complaints', 'newCount', 'inProgressCount', 'resolvedCount'));
    }

    /**
     * Show the form for creating a new complaint.
     */
    public function create()
    {
        $categories = ComplaintCategory::where('is_active', true)->get();
        return view('student.complaint-form', compact('categories'));
    }

    /**
     * Store a newly created complaint in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:complaint_categories,id',
            'attachments.*' => 'nullable|file|max:10240', // 10MB max
        ]);

        $complaint = Complaint::create([
            'reference_number' => Complaint::generateReferenceNumber(),
            'title' => $request->title,
            'description' => $request->description,
            'category_id' => $request->category_id,
            'submitted_by' => Auth::id(),
            'status' => 'new',
            'priority' => 'medium', // Default priority, will be adjusted by admin
        ]);

        // Record status history
        ComplaintStatusHistory::create([
            'complaint_id' => $complaint->id,
            'status' => 'new',
            'changed_by' => Auth::id(),
            'notes' => 'Complaint submitted',
        ]);

        // Handle file uploads
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('complaint_attachments', 'public');

                ComplaintAttachment::create([
                    'complaint_id' => $complaint->id,
                    'file_name' => $file->getClientOriginalName(),
                    'file_path' => $path,
                    'file_type' => $file->getClientMimeType(),
                    'file_size' => $file->getSize(),
                    'uploaded_by' => Auth::id(),
                ]);
            }
        }

        // Send notification to the student
        $complaint->submitter->notify(new \App\Notifications\ComplaintSubmitted($complaint));

        return redirect()->route('student.complaints.show', $complaint)
            ->with('success', 'Complaint submitted successfully.');
    }

    /**
     * Display the specified complaint.
     */
    public function show(Complaint $complaint)
    {
        // Ensure the user can only view their own complaints
        if ($complaint->submitted_by !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $comments = $complaint->comments()
            ->where('is_internal', false)
            ->orderBy('created_at', 'desc')
            ->get();

        $attachments = $complaint->attachments;
        $statusHistory = $complaint->statusHistory()->orderBy('created_at', 'desc')->get();

        return view('student.complaint-detail', compact('complaint', 'comments', 'attachments', 'statusHistory'));
    }

    /**
     * Add a comment to a complaint.
     */
    public function addComment(Request $request, Complaint $complaint)
    {
        // Ensure the user can only comment on their own complaints
        if ($complaint->submitted_by !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'comment' => 'required|string',
        ]);

        ComplaintComment::create([
            'complaint_id' => $complaint->id,
            'user_id' => Auth::id(),
            'comment' => $request->comment,
            'is_internal' => false,
        ]);

        return redirect()->route('student.complaints.show', $complaint)
            ->with('success', 'Comment added successfully.');
    }

    /**
     * Request to reopen a resolved complaint.
     */
    public function reopen(Complaint $complaint)
    {
        // Ensure the user can only reopen their own complaints
        if ($complaint->submitted_by !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Only allow reopening of resolved or closed complaints
        if (!in_array($complaint->status, ['resolved', 'closed'])) {
            return redirect()->route('student.complaints.show', $complaint)
                ->with('error', 'Only resolved or closed complaints can be reopened.');
        }

        $complaint->update([
            'status' => 'reopened',
        ]);

        // Record status history
        ComplaintStatusHistory::create([
            'complaint_id' => $complaint->id,
            'status' => 'reopened',
            'changed_by' => Auth::id(),
            'notes' => 'Complaint reopened by student',
        ]);

        return redirect()->route('student.complaints.show', $complaint)
            ->with('success', 'Complaint reopened successfully.');
    }

    /**
     * Submit satisfaction rating for a resolved complaint.
     */
    public function submitRating(Request $request, Complaint $complaint)
    {
        // Ensure the user can only rate their own complaints
        if ($complaint->submitted_by !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Only allow rating of resolved complaints
        if ($complaint->status !== 'resolved') {
            return redirect()->route('student.complaints.show', $complaint)
                ->with('error', 'Only resolved complaints can be rated.');
        }

        $request->validate([
            'satisfaction_rating' => 'required|integer|min:1|max:5',
            'satisfaction_comment' => 'nullable|string',
        ]);

        $complaint->update([
            'satisfaction_rating' => $request->satisfaction_rating,
            'satisfaction_comment' => $request->satisfaction_comment,
            'status' => 'closed',
        ]);

        // Record status history
        ComplaintStatusHistory::create([
            'complaint_id' => $complaint->id,
            'status' => 'closed',
            'changed_by' => Auth::id(),
            'notes' => 'Complaint closed with satisfaction rating: ' . $request->satisfaction_rating,
        ]);

        return redirect()->route('student.complaints.show', $complaint)
            ->with('success', 'Thank you for your feedback.');
    }
}
