<?php

namespace Tests\Feature;

use App\Models\Complaint;
use App\Models\ComplaintCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ComplaintManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'teacher']);
        Role::create(['name' => 'student']);
    }

    public function test_student_can_submit_complaint(): void
    {
        $student = User::factory()->create();
        $student->assignRole('student');
        
        $category = ComplaintCategory::factory()->create();

        $response = $this->actingAs($student)->post('/student/complaints', [
            'title' => 'Test Complaint',
            'description' => 'This is a test complaint description.',
            'category_id' => $category->id,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('complaints', [
            'title' => 'Test Complaint',
            'submitted_by' => $student->id,
            'status' => 'new',
        ]);
    }

    public function test_complaint_reference_number_is_generated(): void
    {
        $complaint = Complaint::factory()->create();
        
        $this->assertNotNull($complaint->reference_number);
        $this->assertStringStartsWith('CMP-', $complaint->reference_number);
    }

    public function test_complaint_status_transitions(): void
    {
        $complaint = Complaint::factory()->new()->create();
        
        // Test valid transitions
        $this->assertTrue($complaint->canTransitionTo('assigned'));
        $this->assertTrue($complaint->canTransitionTo('in_progress'));
        $this->assertTrue($complaint->canTransitionTo('closed'));
        
        // Test invalid transitions
        $this->assertFalse($complaint->canTransitionTo('reopened'));
        
        // Test actual transition
        $result = $complaint->transitionTo('assigned');
        $this->assertTrue($result);
        $this->assertEquals('assigned', $complaint->fresh()->status);
    }

    public function test_complaint_sla_calculation(): void
    {
        $category = ComplaintCategory::factory()->create(['sla_days' => 7]);
        $complaint = Complaint::factory()->create([
            'category_id' => $category->id,
            'priority' => 'high',
        ]);

        $dueDate = $complaint->calculateSLADueDate();
        
        // High priority should be 50% of base time (3.5 days)
        $expectedDays = ceil(7 * 0.5);
        $expectedDate = $complaint->created_at->addDays($expectedDays);
        
        $this->assertEquals($expectedDate->format('Y-m-d'), $dueDate->format('Y-m-d'));
    }

    public function test_complaint_is_overdue_detection(): void
    {
        $complaint = Complaint::factory()->create([
            'due_date' => now()->subDay(),
            'status' => 'in_progress',
        ]);

        $this->assertTrue($complaint->isOverdue());

        // Resolved complaints should not be overdue
        $resolvedComplaint = Complaint::factory()->resolved()->create([
            'due_date' => now()->subDay(),
        ]);

        $this->assertFalse($resolvedComplaint->isOverdue());
    }

    public function test_admin_can_access_filament_panel(): void
    {
        $admin = User::factory()->create(['email_verified_at' => now()]);
        $admin->assignRole('admin');

        $this->assertTrue($admin->canAccessPanel(new \Filament\Panel()));
    }

    public function test_teacher_can_access_filament_panel(): void
    {
        $teacher = User::factory()->create(['email_verified_at' => now()]);
        $teacher->assignRole('teacher');

        $this->assertTrue($teacher->canAccessPanel(new \Filament\Panel()));
    }

    public function test_student_cannot_access_filament_panel(): void
    {
        $student = User::factory()->create(['email_verified_at' => now()]);
        $student->assignRole('student');

        $this->assertFalse($student->canAccessPanel(new \Filament\Panel()));
    }

    public function test_complaint_assignment(): void
    {
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');
        
        $complaint = Complaint::factory()->new()->create();
        
        $assignment = $complaint->assignTo($teacher->id, 'Manual assignment for testing');
        
        $this->assertEquals($teacher->id, $complaint->fresh()->assigned_to);
        $this->assertEquals('assigned', $complaint->fresh()->status);
        $this->assertInstanceOf(\App\Models\ComplaintAssignment::class, $assignment);
    }

    public function test_complaint_escalation(): void
    {
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');
        
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        $complaint = Complaint::factory()->assigned()->create([
            'assigned_to' => $teacher->id,
        ]);
        
        $escalation = $complaint->escalate($admin->id, 'Escalating due to complexity');
        
        $this->assertEquals($admin->id, $complaint->fresh()->assigned_to);
        $this->assertEquals(1, $escalation->escalation_level);
        $this->assertEquals('Escalating due to complexity', $escalation->reason);
    }

    public function test_workload_calculation(): void
    {
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');
        
        // Create some active complaints assigned to teacher
        Complaint::factory()->count(3)->assigned()->create([
            'assigned_to' => $teacher->id,
        ]);
        
        // Create a resolved complaint (should not count)
        Complaint::factory()->resolved()->create([
            'assigned_to' => $teacher->id,
        ]);
        
        $workload = Complaint::getWorkloadForUser($teacher->id);
        $this->assertEquals(3, $workload);
    }

    public function test_complaint_analytics_update(): void
    {
        $complaint = Complaint::factory()->resolved()->create();
        
        // Trigger analytics update
        \App\Models\ComplaintAnalytics::updateFromComplaint($complaint);
        
        $this->assertDatabaseHas('complaint_analytics', [
            'complaint_id' => $complaint->id,
            'status' => $complaint->status,
            'priority' => $complaint->priority,
        ]);
    }
}
