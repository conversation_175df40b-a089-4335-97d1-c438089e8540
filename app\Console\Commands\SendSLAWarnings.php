<?php

namespace App\Console\Commands;

use App\Models\Complaint;
use App\Notifications\SLAWarning;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendSLAWarnings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'complaints:sla-warnings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send SLA warning notifications for complaints approaching due date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Sending SLA warning notifications...');
        
        // Get complaints that are approaching SLA breach (within 24 hours)
        $complaintsAtRisk = Complaint::whereNotIn('status', ['resolved', 'closed'])
            ->where('due_date', '>', now())
            ->where('due_date', '<=', now()->addDay())
            ->with(['submitter', 'assignee', 'category'])
            ->get();

        $warningsSent = 0;
        
        foreach ($complaintsAtRisk as $complaint) {
            $this->sendSLAWarning($complaint);
            $warningsSent++;
        }
        
        // Also check for complaints that are already overdue
        $overdueComplaints = Complaint::whereNotIn('status', ['resolved', 'closed'])
            ->where('due_date', '<', now())
            ->with(['submitter', 'assignee', 'category'])
            ->get();

        foreach ($overdueComplaints as $complaint) {
            $this->sendOverdueNotification($complaint);
            $warningsSent++;
        }
        
        $this->info("Sent {$warningsSent} SLA warning notifications");
        Log::info("Sent {$warningsSent} SLA warning notifications");
        
        return 0;
    }

    /**
     * Send SLA warning notification
     */
    private function sendSLAWarning(Complaint $complaint): void
    {
        try {
            // Notify assignee if assigned
            if ($complaint->assignee) {
                $complaint->assignee->notify(new SLAWarning($complaint, 'approaching'));
            }
            
            // Notify submitter
            if ($complaint->submitter) {
                $complaint->submitter->notify(new SLAWarning($complaint, 'approaching'));
            }
            
            Log::info("SLA warning sent for complaint {$complaint->reference_number}");
        } catch (\Exception $e) {
            Log::error("Failed to send SLA warning for complaint {$complaint->reference_number}: " . $e->getMessage());
        }
    }

    /**
     * Send overdue notification
     */
    private function sendOverdueNotification(Complaint $complaint): void
    {
        try {
            // Notify assignee if assigned
            if ($complaint->assignee) {
                $complaint->assignee->notify(new SLAWarning($complaint, 'overdue'));
            }
            
            // Notify submitter
            if ($complaint->submitter) {
                $complaint->submitter->notify(new SLAWarning($complaint, 'overdue'));
            }
            
            Log::info("Overdue notification sent for complaint {$complaint->reference_number}");
        } catch (\Exception $e) {
            Log::error("Failed to send overdue notification for complaint {$complaint->reference_number}: " . $e->getMessage());
        }
    }
}
