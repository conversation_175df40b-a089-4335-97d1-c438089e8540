<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Welcome Section -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Welcome back, {{ Auth::user()->name }}!</h1>
                <p class="mt-2 text-gray-600">Manage your complaints and track their progress from your dashboard.</p>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Complaints</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $totalComplaints }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">New</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $newComplaints }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-indigo-100 rounded-lg">
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">In Progress</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $inProgressComplaints }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Resolved</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $resolvedComplaints }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Quick Complaint Submission -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">Quick Complaint Submission</h2>
                            <p class="text-sm text-gray-500">Submit a new complaint quickly from your dashboard</p>
                        </div>
                        <div class="p-6">
                            <form action="{{ route('student.dashboard.quick-submit') }}" method="POST" class="space-y-4">
                                @csrf
                                <div>
                                    <label for="quick-title" class="block text-sm font-medium text-gray-700">Title</label>
                                    <input type="text" id="quick-title" name="title" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                           placeholder="Brief description of your complaint">
                                </div>

                                <div>
                                    <label for="quick-category" class="block text-sm font-medium text-gray-700">Category</label>
                                    <select id="quick-category" name="category_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Select a category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div>
                                    <label for="quick-description" class="block text-sm font-medium text-gray-700">Description</label>
                                    <textarea id="quick-description" name="description" rows="3" required
                                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                              placeholder="Provide detailed information about your complaint"></textarea>
                                </div>

                                <div class="flex justify-between items-center">
                                    <a href="{{ route('student.complaints.create') }}" class="text-sm text-blue-600 hover:text-blue-500">
                                        Need to attach files? Use the full form →
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        Submit Complaint
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Complaints -->
                    <div class="mt-8 bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-900">Recent Complaints</h2>
                            <a href="{{ route('student.complaints') }}" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
                        </div>
                        <div class="divide-y divide-gray-200">
                            @forelse($recentComplaints as $complaint)
                                <div class="p-6 hover:bg-gray-50">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-3">
                                                <h3 class="text-sm font-medium text-gray-900">{{ $complaint->title }}</h3>
                                                <span class="status-badge status-badge-{{ $complaint->status }}">
                                                    {{ ucfirst(str_replace('_', ' ', $complaint->status)) }}
                                                </span>
                                            </div>
                                            <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                                                <span>{{ $complaint->reference_number }}</span>
                                                <span>{{ $complaint->category->name }}</span>
                                                <span>{{ $complaint->created_at->diffForHumans() }}</span>
                                            </div>
                                        </div>
                                        <a href="{{ route('student.complaints.show', $complaint) }}"
                                           class="text-blue-600 hover:text-blue-500">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            @empty
                                <div class="p-6 text-center text-gray-500">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <p class="mt-2">No complaints submitted yet</p>
                                    <a href="{{ route('student.complaints.create') }}" class="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-500">
                                        Submit your first complaint
                                    </a>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Notifications -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-900">Notifications</h2>
                            @if($unreadNotifications->count() > 0)
                                <button onclick="markAllAsRead()" class="text-sm text-blue-600 hover:text-blue-500">
                                    Mark all as read
                                </button>
                            @endif
                        </div>
                        <div class="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                            @forelse($unreadNotifications as $notification)
                                <div class="p-4 hover:bg-gray-50 cursor-pointer" onclick="markAsRead('{{ $notification->id }}')">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <div class="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm font-medium text-gray-900">
                                                {{ $notification->data['message'] ?? 'New notification' }}
                                            </p>
                                            <p class="text-xs text-gray-500 mt-1">
                                                {{ $notification->created_at->diffForHumans() }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="p-4 text-center text-gray-500">
                                    <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"></path>
                                    </svg>
                                    <p class="mt-2 text-sm">No new notifications</p>
                                </div>
                            @endforelse
                        </div>
                    </div>

                    <!-- Help & FAQ -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">Help & FAQ</h2>
                        </div>
                        <div class="p-6 space-y-4">
                            <div>
                                <h3 class="text-sm font-medium text-gray-900">How to submit a complaint?</h3>
                                <p class="text-sm text-gray-500 mt-1">Use the quick form above or the full form for detailed complaints with attachments.</p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-900">How long does resolution take?</h3>
                                <p class="text-sm text-gray-500 mt-1">Most complaints are resolved within 7 business days, depending on complexity.</p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-900">How to track my complaint?</h3>
                                <p class="text-sm text-gray-500 mt-1">Use your reference number to track status updates and view progress.</p>
                            </div>
                            <a href="#" class="inline-flex items-center text-sm text-blue-600 hover:text-blue-500">
                                View all FAQs
                                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function markAsRead(notificationId) {
            fetch(`/student/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        }

        function markAllAsRead() {
            fetch('/student/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        }
    </script>
</x-app-layout>
