<?php

namespace Database\Factories;

use App\Models\Complaint;
use App\Models\ComplaintCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Complaint>
 */
class ComplaintFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Complaint::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $submittedAt = $this->faker->dateTimeBetween('-6 months', 'now');
        $status = $this->faker->randomElement(['new', 'assigned', 'in_progress', 'on_hold', 'resolved', 'closed']);
        $priority = $this->faker->randomElement(['low', 'medium', 'high', 'critical']);
        
        // Calculate due date based on priority
        $daysToAdd = match($priority) {
            'critical' => 1,
            'high' => 3,
            'medium' => 7,
            'low' => 14,
        };

        return [
            'reference_number' => $this->generateReferenceNumber(),
            'title' => $this->faker->sentence(6),
            'description' => $this->faker->paragraphs(3, true),
            'category_id' => ComplaintCategory::factory(),
            'submitted_by' => User::factory()->state(['user_type' => 'student']),
            'assigned_to' => $status !== 'new' ? User::factory()->state(['user_type' => 'teacher']) : null,
            'status' => $status,
            'priority' => $priority,
            'due_date' => (clone $submittedAt)->modify("+{$daysToAdd} days"),
            'resolution' => in_array($status, ['resolved', 'closed']) ? $this->faker->paragraph() : null,
            'resolution_date' => in_array($status, ['resolved', 'closed']) ? $this->faker->dateTimeBetween($submittedAt, 'now') : null,
            'satisfaction_rating' => in_array($status, ['resolved', 'closed']) && $this->faker->boolean(70) ? $this->faker->numberBetween(1, 5) : null,
            'satisfaction_comment' => in_array($status, ['resolved', 'closed']) && $this->faker->boolean(30) ? $this->faker->sentence() : null,
            'created_at' => $submittedAt,
            'updated_at' => $this->faker->dateTimeBetween($submittedAt, 'now'),
        ];
    }

    /**
     * Generate a unique reference number
     */
    private function generateReferenceNumber(): string
    {
        $prefix = 'CMP';
        $year = date('Y');
        $month = date('m');
        $random = $this->faker->unique()->numberBetween(1000, 9999);

        return $prefix . '-' . $year . $month . '-' . $random;
    }

    /**
     * Indicate that the complaint is new.
     */
    public function new(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'new',
            'assigned_to' => null,
            'resolution' => null,
            'resolution_date' => null,
            'satisfaction_rating' => null,
            'satisfaction_comment' => null,
        ]);
    }

    /**
     * Indicate that the complaint is assigned.
     */
    public function assigned(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'assigned',
            'assigned_to' => User::factory()->state(['user_type' => 'teacher']),
            'resolution' => null,
            'resolution_date' => null,
            'satisfaction_rating' => null,
            'satisfaction_comment' => null,
        ]);
    }

    /**
     * Indicate that the complaint is in progress.
     */
    public function inProgress(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'in_progress',
            'assigned_to' => User::factory()->state(['user_type' => 'teacher']),
            'resolution' => null,
            'resolution_date' => null,
            'satisfaction_rating' => null,
            'satisfaction_comment' => null,
        ]);
    }

    /**
     * Indicate that the complaint is resolved.
     */
    public function resolved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'resolved',
            'assigned_to' => User::factory()->state(['user_type' => 'teacher']),
            'resolution' => $this->faker->paragraph(),
            'resolution_date' => $this->faker->dateTimeBetween($attributes['created_at'] ?? '-1 month', 'now'),
            'satisfaction_rating' => $this->faker->boolean(80) ? $this->faker->numberBetween(1, 5) : null,
            'satisfaction_comment' => $this->faker->boolean(40) ? $this->faker->sentence() : null,
        ]);
    }

    /**
     * Indicate that the complaint is closed.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'closed',
            'assigned_to' => User::factory()->state(['user_type' => 'teacher']),
            'resolution' => $this->faker->paragraph(),
            'resolution_date' => $this->faker->dateTimeBetween($attributes['created_at'] ?? '-1 month', 'now'),
            'satisfaction_rating' => $this->faker->boolean(90) ? $this->faker->numberBetween(1, 5) : null,
            'satisfaction_comment' => $this->faker->boolean(50) ? $this->faker->sentence() : null,
        ]);
    }

    /**
     * Indicate that the complaint is overdue.
     */
    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => $this->faker->randomElement(['assigned', 'in_progress']),
            'assigned_to' => User::factory()->state(['user_type' => 'teacher']),
            'due_date' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
            'resolution' => null,
            'resolution_date' => null,
            'satisfaction_rating' => null,
            'satisfaction_comment' => null,
        ]);
    }

    /**
     * Indicate that the complaint has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
            'due_date' => (clone ($attributes['created_at'] ?? now()))->modify('+3 days'),
        ]);
    }

    /**
     * Indicate that the complaint has critical priority.
     */
    public function critical(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'critical',
            'due_date' => (clone ($attributes['created_at'] ?? now()))->modify('+1 day'),
        ]);
    }
}
