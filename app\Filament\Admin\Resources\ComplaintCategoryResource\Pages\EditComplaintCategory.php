<?php

namespace App\Filament\Admin\Resources\ComplaintCategoryResource\Pages;

use App\Filament\Admin\Resources\ComplaintCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditComplaintCategory extends EditRecord
{
    protected static string $resource = ComplaintCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
