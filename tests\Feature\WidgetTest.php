<?php

namespace Tests\Feature;

use App\Filament\Widgets\SLAMonitoringWidget;
use App\Filament\Widgets\SatisfactionRatingWidget;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class WidgetTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'teacher']);
        Role::create(['name' => 'student']);
    }

    public function test_sla_monitoring_widget_can_be_instantiated(): void
    {
        $admin = User::factory()->create(['email_verified_at' => now()]);
        $admin->assignRole('admin');

        $this->actingAs($admin);

        $widget = new SLAMonitoringWidget();
        
        $this->assertInstanceOf(SLAMonitoringWidget::class, $widget);
        
        // Test that the widget can get its heading
        $reflection = new \ReflectionClass($widget);
        $method = $reflection->getMethod('getHeading');
        $method->setAccessible(true);
        $heading = $method->invoke($widget);
        
        $this->assertEquals('SLA Monitoring', $heading);
    }

    public function test_satisfaction_rating_widget_can_be_instantiated(): void
    {
        $admin = User::factory()->create(['email_verified_at' => now()]);
        $admin->assignRole('admin');

        $this->actingAs($admin);

        $widget = new SatisfactionRatingWidget();
        
        $this->assertInstanceOf(SatisfactionRatingWidget::class, $widget);
        
        // Test that the widget can get its heading
        $reflection = new \ReflectionClass($widget);
        $method = $reflection->getMethod('getHeading');
        $method->setAccessible(true);
        $heading = $method->invoke($widget);
        
        $this->assertEquals('Customer Satisfaction Metrics', $heading);
    }

    public function test_sla_monitoring_widget_returns_stats(): void
    {
        $admin = User::factory()->create(['email_verified_at' => now()]);
        $admin->assignRole('admin');

        $this->actingAs($admin);

        $widget = new SLAMonitoringWidget();
        
        $reflection = new \ReflectionClass($widget);
        $method = $reflection->getMethod('getStats');
        $method->setAccessible(true);
        $stats = $method->invoke($widget);
        
        $this->assertIsArray($stats);
        $this->assertNotEmpty($stats);
        
        // Check that all stats are Stat instances
        foreach ($stats as $stat) {
            $this->assertInstanceOf(\Filament\Widgets\StatsOverviewWidget\Stat::class, $stat);
        }
    }

    public function test_satisfaction_rating_widget_returns_stats(): void
    {
        $admin = User::factory()->create(['email_verified_at' => now()]);
        $admin->assignRole('admin');

        $this->actingAs($admin);

        $widget = new SatisfactionRatingWidget();
        
        $reflection = new \ReflectionClass($widget);
        $method = $reflection->getMethod('getStats');
        $method->setAccessible(true);
        $stats = $method->invoke($widget);
        
        $this->assertIsArray($stats);
        $this->assertNotEmpty($stats);
        
        // Check that all stats are Stat instances
        foreach ($stats as $stat) {
            $this->assertInstanceOf(\Filament\Widgets\StatsOverviewWidget\Stat::class, $stat);
        }
    }
}
