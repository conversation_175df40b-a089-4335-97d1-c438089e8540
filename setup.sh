#!/bin/bash

echo "========================================"
echo "Complaints Management System Setup"
echo "========================================"
echo

echo "Step 1: Copying environment file..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo ".env file created from .env.example"
else
    echo ".env file already exists"
fi
echo

echo "Step 2: Generating application key..."
php artisan key:generate
echo

echo "Step 3: Creating SQLite database..."
if [ ! -f database/database.sqlite ]; then
    touch database/database.sqlite
    echo "SQLite database file created"
else
    echo "SQLite database file already exists"
fi
echo

echo "Step 4: Running database migrations..."
php artisan migrate --force
echo

echo "Step 5: Seeding database with demo data..."
php artisan db:seed --force
echo

echo "Step 6: Creating storage link..."
php artisan storage:link
echo

echo "Step 7: Clearing cache..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
echo

echo "========================================"
echo "Setup Complete!"
echo "========================================"
echo
echo "You can now access the application:"
echo "- Home/Admin: http://localhost:8000"
echo "- Student Portal: http://localhost:8000/student/dashboard"
echo
echo "Demo Login Credentials:"
echo "- Admin: <EMAIL> / password"
echo "- Teacher: <EMAIL> / password"
echo "- Student: <EMAIL> / password"
echo
echo "To start the development server, run:"
echo "php artisan serve"
echo
