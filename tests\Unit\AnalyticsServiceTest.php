<?php

namespace Tests\Unit;

use App\Models\ComplaintAnalytics;
use App\Services\AnalyticsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AnalyticsServiceTest extends TestCase
{
    use RefreshDatabase;

    private AnalyticsService $analyticsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->analyticsService = new AnalyticsService();
    }

    public function test_trend_analysis_calculation(): void
    {
        // Create some test analytics data
        ComplaintAnalytics::factory()->count(10)->create([
            'submitted_date' => now()->subMonths(6),
            'resolution_time_hours' => 24,
            'sla_breached' => false,
            'satisfaction_rating' => 4,
        ]);

        ComplaintAnalytics::factory()->count(15)->create([
            'submitted_date' => now()->subMonths(3),
            'resolution_time_hours' => 18,
            'sla_breached' => false,
            'satisfaction_rating' => 4.5,
        ]);

        $trendData = $this->analyticsService->getTrendAnalysis('12months');

        $this->assertArrayHasKey('volume_trend', $trendData);
        $this->assertArrayHasKey('resolution_trend', $trendData);
        $this->assertArrayHasKey('sla_trend', $trendData);
        $this->assertArrayHasKey('satisfaction_trend', $trendData);
        $this->assertArrayHasKey('data', $trendData);
    }

    public function test_predictive_analysis(): void
    {
        // Create historical data for the last 12 months
        for ($i = 11; $i >= 0; $i--) {
            ComplaintAnalytics::factory()->count(10 + $i)->create([
                'submitted_date' => now()->subMonths($i)->startOfMonth(),
            ]);
        }

        $prediction = $this->analyticsService->getPredictiveAnalysis();

        $this->assertArrayHasKey('next_month_prediction', $prediction);
        $this->assertArrayHasKey('trend_direction', $prediction);
        $this->assertArrayHasKey('confidence_level', $prediction);
        $this->assertArrayHasKey('seasonal_factors', $prediction);
        $this->assertArrayHasKey('recommendations', $prediction);

        $this->assertIsNumeric($prediction['next_month_prediction']);
        $this->assertContains($prediction['trend_direction'], ['increasing', 'decreasing']);
    }

    public function test_staff_performance_metrics(): void
    {
        // Create users with different performance levels
        $highPerformer = \App\Models\User::factory()->create();
        $highPerformer->assignRole('teacher');

        $lowPerformer = \App\Models\User::factory()->create();
        $lowPerformer->assignRole('teacher');

        // Create analytics data for high performer
        ComplaintAnalytics::factory()->count(10)->create([
            'resolver_id' => $highPerformer->id,
            'resolution_time_hours' => 12,
            'sla_breached' => false,
            'satisfaction_rating' => 5,
            'submitted_date' => now()->subDays(15),
        ]);

        // Create analytics data for low performer
        ComplaintAnalytics::factory()->count(5)->create([
            'resolver_id' => $lowPerformer->id,
            'resolution_time_hours' => 48,
            'sla_breached' => true,
            'satisfaction_rating' => 2,
            'submitted_date' => now()->subDays(15),
        ]);

        $performanceMetrics = $this->analyticsService->getStaffPerformanceMetrics();

        $this->assertIsArray($performanceMetrics);
        $this->assertNotEmpty($performanceMetrics);

        // Check that metrics include expected fields
        $firstMetric = $performanceMetrics[0];
        $this->assertArrayHasKey('name', $firstMetric);
        $this->assertArrayHasKey('total_resolved', $firstMetric);
        $this->assertArrayHasKey('avg_resolution_time', $firstMetric);
        $this->assertArrayHasKey('sla_compliance_rate', $firstMetric);
        $this->assertArrayHasKey('productivity_score', $firstMetric);
    }

    public function test_cost_analysis(): void
    {
        // Create analytics data with different priorities and costs
        ComplaintAnalytics::factory()->count(5)->create([
            'priority' => 'critical',
            'cost_estimate' => 500,
            'resolution_time_hours' => 8,
            'submitted_date' => now()->subDays(30),
        ]);

        ComplaintAnalytics::factory()->count(10)->create([
            'priority' => 'medium',
            'cost_estimate' => 200,
            'resolution_time_hours' => 24,
            'submitted_date' => now()->subDays(30),
        ]);

        $costAnalysis = $this->analyticsService->getCostAnalysis('30days');

        $this->assertArrayHasKey('total_cost', $costAnalysis);
        $this->assertArrayHasKey('avg_cost_per_complaint', $costAnalysis);
        $this->assertArrayHasKey('cost_by_priority', $costAnalysis);
        $this->assertArrayHasKey('cost_trends', $costAnalysis);
        $this->assertArrayHasKey('cost_savings_opportunities', $costAnalysis);

        $this->assertIsNumeric($costAnalysis['total_cost']);
        $this->assertIsArray($costAnalysis['cost_by_priority']);
    }

    public function test_comparative_analysis(): void
    {
        // Create data for current month
        ComplaintAnalytics::factory()->count(20)->create([
            'submitted_date' => now()->startOfMonth(),
            'resolution_time_hours' => 20,
            'sla_breached' => false,
            'satisfaction_rating' => 4,
        ]);

        // Create data for last month
        ComplaintAnalytics::factory()->count(15)->create([
            'submitted_date' => now()->subMonth()->startOfMonth(),
            'resolution_time_hours' => 25,
            'sla_breached' => true,
            'satisfaction_rating' => 3,
        ]);

        $comparison = $this->analyticsService->getComparativeAnalysis();

        $this->assertArrayHasKey('month_over_month', $comparison);
        $this->assertArrayHasKey('year_over_year', $comparison);
        $this->assertArrayHasKey('current_month', $comparison);
        $this->assertArrayHasKey('last_month', $comparison);

        // Check month-over-month comparison structure
        $mom = $comparison['month_over_month'];
        $this->assertArrayHasKey('total_complaints', $mom);
        $this->assertArrayHasKey('avg_resolution_time', $mom);
        $this->assertArrayHasKey('sla_compliance', $mom);

        // Each metric should have current, previous, change_percentage, and direction
        $totalComplaints = $mom['total_complaints'];
        $this->assertArrayHasKey('current', $totalComplaints);
        $this->assertArrayHasKey('previous', $totalComplaints);
        $this->assertArrayHasKey('change_percentage', $totalComplaints);
        $this->assertArrayHasKey('direction', $totalComplaints);
    }

    public function test_sla_compliance_calculation(): void
    {
        // Create compliant analytics
        ComplaintAnalytics::factory()->count(8)->create([
            'sla_breached' => false,
            'submitted_date' => now()->subDays(15),
        ]);

        // Create non-compliant analytics
        ComplaintAnalytics::factory()->count(2)->create([
            'sla_breached' => true,
            'submitted_date' => now()->subDays(15),
        ]);

        $complianceRate = ComplaintAnalytics::getSLAComplianceRate(
            now()->subDays(30),
            now()
        );

        $this->assertEquals(80.0, $complianceRate); // 8 out of 10 = 80%
    }

    public function test_satisfaction_metrics(): void
    {
        // Create satisfaction data
        ComplaintAnalytics::factory()->count(2)->create([
            'satisfaction_rating' => 5,
            'submitted_date' => now()->subDays(15),
        ]);

        ComplaintAnalytics::factory()->count(3)->create([
            'satisfaction_rating' => 4,
            'submitted_date' => now()->subDays(15),
        ]);

        ComplaintAnalytics::factory()->count(1)->create([
            'satisfaction_rating' => 2,
            'submitted_date' => now()->subDays(15),
        ]);

        $satisfactionMetrics = ComplaintAnalytics::getSatisfactionMetrics(
            now()->subDays(30),
            now()
        );

        $this->assertArrayHasKey('average_rating', $satisfactionMetrics);
        $this->assertArrayHasKey('total_responses', $satisfactionMetrics);
        $this->assertArrayHasKey('rating_distribution', $satisfactionMetrics);

        $this->assertEquals(6, $satisfactionMetrics['total_responses']);
        $this->assertEquals(4.17, round($satisfactionMetrics['average_rating'], 2)); // (5*2 + 4*3 + 2*1) / 6
        
        $distribution = $satisfactionMetrics['rating_distribution'];
        $this->assertEquals(2, $distribution[5]);
        $this->assertEquals(3, $distribution[4]);
        $this->assertEquals(1, $distribution[2]);
    }
}
