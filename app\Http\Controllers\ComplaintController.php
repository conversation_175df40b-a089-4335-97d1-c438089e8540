<?php

namespace App\Http\Controllers;

use App\Models\Complaint;
use App\Models\ComplaintCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ComplaintController extends Controller
{
    /**
     * Display a listing of the user's complaints.
     */
    public function index()
    {
        $complaints = Complaint::forUser(Auth::id(), Auth::user()->user_type)
            ->with(['category', 'assignee'])
            ->latest()
            ->paginate(10);

        return view('complaints.index', compact('complaints'));
    }

    /**
     * Show the form for creating a new complaint.
     */
    public function create()
    {
        $categories = ComplaintCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->get();
            
        return view('complaints.create', compact('categories'));
    }

    /**
     * Store a newly created complaint in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:complaint_categories,id',
            'priority' => 'required|in:low,medium,high,critical',
            'is_anonymous' => 'boolean',
            'attachments.*' => 'file|max:10240|mimes:pdf,doc,docx,jpg,jpeg,png', // 10MB max
        ]);

        // Handle file uploads
        $attachments = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('complaint-attachments', 'public');
                $attachments[] = [
                    'original_name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ];
            }
        }

        $complaint = Complaint::create([
            'reference_number' => Complaint::generateReferenceNumber(),
            'title' => $validated['title'],
            'description' => $validated['description'],
            'category_id' => $validated['category_id'],
            'priority' => $validated['priority'],
            'submitted_by' => Auth::id(),
            'is_anonymous' => $request->boolean('is_anonymous'),
            'attachments' => $attachments,
            'status' => 'new',
        ]);

        // Auto-assign if enabled
        $complaint->autoAssign();

        return redirect()->route('complaints.show', $complaint)
            ->with('success', 'Complaint submitted successfully. Reference: ' . $complaint->reference_number);
    }

    /**
     * Display the specified complaint.
     */
    public function show(Complaint $complaint)
    {
        // Authorization check
        if (Auth::user()->user_type === 'student' && $complaint->submitted_by !== Auth::id()) {
            abort(403, 'You can only view your own complaints.');
        }

        $complaint->load([
            'category', 
            'submitter', 
            'assignee', 
            'comments.user',
            'statusHistory.changedBy',
            'attachments'
        ]);
        
        return view('complaints.show', compact('complaint'));
    }

    /**
     * Show the form for editing the specified complaint.
     */
    public function edit(Complaint $complaint)
    {
        // Students can only edit their own complaints and only if status is 'new'
        if (Auth::user()->user_type === 'student') {
            if ($complaint->submitted_by !== Auth::id()) {
                abort(403, 'You can only edit your own complaints.');
            }
            if ($complaint->status !== 'new') {
                abort(403, 'You can only edit complaints that have not been processed yet.');
            }
        }

        $categories = ComplaintCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->get();
            
        return view('complaints.edit', compact('complaint', 'categories'));
    }

    /**
     * Update the specified complaint in storage.
     */
    public function update(Request $request, Complaint $complaint)
    {
        // Authorization check
        if (Auth::user()->user_type === 'student') {
            if ($complaint->submitted_by !== Auth::id()) {
                abort(403, 'You can only edit your own complaints.');
            }
            if ($complaint->status !== 'new') {
                abort(403, 'You can only edit complaints that have not been processed yet.');
            }
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:complaint_categories,id',
            'priority' => 'required|in:low,medium,high,critical',
            'attachments.*' => 'file|max:10240|mimes:pdf,doc,docx,jpg,jpeg,png',
        ]);

        // Handle new file uploads
        $existingAttachments = $complaint->attachments ?? [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('complaint-attachments', 'public');
                $existingAttachments[] = [
                    'original_name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ];
            }
        }

        $complaint->update([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'category_id' => $validated['category_id'],
            'priority' => $validated['priority'],
            'attachments' => $existingAttachments,
        ]);

        return redirect()->route('complaints.show', $complaint)
            ->with('success', 'Complaint updated successfully.');
    }

    /**
     * Remove the specified complaint from storage.
     */
    public function destroy(Complaint $complaint)
    {
        // Only allow deletion if status is 'new' and user is the submitter
        if ($complaint->submitted_by !== Auth::id() || $complaint->status !== 'new') {
            abort(403, 'You can only delete your own complaints that have not been processed yet.');
        }

        // Delete associated files
        if ($complaint->attachments) {
            foreach ($complaint->attachments as $attachment) {
                Storage::disk('public')->delete($attachment['path']);
            }
        }

        $complaint->delete();

        return redirect()->route('complaints.index')
            ->with('success', 'Complaint deleted successfully.');
    }

    /**
     * Download attachment
     */
    public function downloadAttachment(Complaint $complaint, $attachmentIndex)
    {
        // Authorization check
        if (Auth::user()->user_type === 'student' && $complaint->submitted_by !== Auth::id()) {
            abort(403);
        }

        $attachments = $complaint->attachments ?? [];
        if (!isset($attachments[$attachmentIndex])) {
            abort(404, 'Attachment not found.');
        }

        $attachment = $attachments[$attachmentIndex];
        $filePath = storage_path('app/public/' . $attachment['path']);

        if (!file_exists($filePath)) {
            abort(404, 'File not found.');
        }

        return response()->download($filePath, $attachment['original_name']);
    }
}
