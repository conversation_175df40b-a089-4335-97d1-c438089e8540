<?php

echo "🔧 Fixing database connection issues...\n";

// Step 1: Ensure database directory exists
if (!is_dir('database')) {
    mkdir('database', 0755, true);
    echo "✅ Created database directory\n";
}

// Step 2: Remove old database file
$dbFile = 'database/database.sqlite';
if (file_exists($dbFile)) {
    unlink($dbFile);
    echo "✅ Removed old database file\n";
}

// Step 3: Create new database file with proper permissions
file_put_contents($dbFile, '');
chmod($dbFile, 0664);
echo "✅ Created new database file\n";

// Step 4: Test database connection
try {
    $pdo = new PDO('sqlite:' . $dbFile);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test basic operations
    $pdo->exec('CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY)');
    $pdo->exec('INSERT INTO test_table (id) VALUES (1)');
    $result = $pdo->query('SELECT COUNT(*) FROM test_table')->fetchColumn();
    $pdo->exec('DROP TABLE test_table');
    
    if ($result == 1) {
        echo "✅ Database connection test passed\n";
    } else {
        echo "❌ Database connection test failed\n";
        exit(1);
    }
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 5: Create proper .env file
$envContent = 'APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=base64:SGVsbG9Xb3JsZEhlbGxvV29ybGRIZWxsb1dvcmxkSGVsbG9Xb3JsZA==
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
';

file_put_contents('.env', $envContent);
echo "✅ Created .env file\n";

// Step 6: Create required directories
$dirs = [
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    }
}

// Step 7: Clear cache files
$cacheFiles = [
    'bootstrap/cache/config.php',
    'bootstrap/cache/routes-v7.php',
    'bootstrap/cache/services.php'
];

foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "✅ Removed cache file: $file\n";
    }
}

echo "\n🎉 Database connection fix complete!\n";
echo "Next steps:\n";
echo "1. php artisan config:clear\n";
echo "2. php artisan migrate:fresh --force\n";
echo "3. php artisan db:seed --force\n";
echo "4. php artisan serve\n";
