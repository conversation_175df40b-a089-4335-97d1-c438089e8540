# Complaints and Feedback Management System

A comprehensive ISO 21001 compliant complaints management system built with Laravel 11, Filament 3, and modern web technologies.

## Features

### Core Functionality
- **Multi-role Authentication**: Admin, Teacher, and Student roles with appropriate permissions
- **Complaint Lifecycle Management**: Complete workflow from submission to resolution
- **Real-time Notifications**: Email and in-app notifications for all stakeholders
- **File Attachments**: Support for multiple file types with validation
- **Advanced Search & Filtering**: Comprehensive search capabilities across all complaint data

### Business Intelligence & Analytics
- **Interactive Dashboards**: Real-time widgets showing key metrics and trends
- **Predictive Analytics**: Volume forecasting using linear regression
- **Staff Performance Metrics**: Productivity scoring and comparative analysis
- **Cost Analysis**: Resolution cost tracking and savings opportunities
- **Custom Report Generation**: PDF, Excel, and CSV exports with scheduling

### ISO 21001 Compliance
- **SLA Monitoring**: Automated tracking and breach notifications
- **Escalation System**: Multi-level escalation with configurable rules
- **Audit Trail**: Complete activity logging for compliance verification
- **Stakeholder Satisfaction**: Rating system with NPS calculation
- **Continuous Improvement**: Trend analysis and improvement recommendations

## Technology Stack

- **Backend**: Laravel 11 with PHP 8.2+
- **Admin Panel**: Filament 3 with custom widgets and resources
- **Database**: SQLite (optimized) with MySQL support
- **Authentication**: Laravel Breeze with role-based permissions
- **Permissions**: Spatie Laravel Permission package
- **Frontend**: Tailwind CSS with responsive design
- **Queue System**: Database-driven job processing
- **Notifications**: Multi-channel notification system

## Quick Setup

### Automated Setup (Recommended)

**SQLite Setup (Fastest):**
```bash
SQLITE-SETUP.bat
```

**Complete Setup:**
```bash
COMPLETE-AUTO-FIX.bat
```

**Linux/Mac:**
```bash
chmod +x setup.sh
./setup.sh
```

Then start the development server:
```bash
php artisan serve
```

### Manual Installation

#### Prerequisites
- PHP 8.2 or higher with SQLite extension
- Composer
- Node.js & NPM (optional, for asset compilation)
- SQLite (default) or MySQL 8.0+ (optional)

#### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd complaints-management-system
   ```

2. **Install dependencies**
   ```bash
   composer install
   npm install  # optional
   ```

3. **Environment configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Setup database (SQLite - Recommended)**
   ```bash
   # Windows
   echo. > database\database.sqlite

   # Linux/Mac
   touch database/database.sqlite
   ```

   **Or configure MySQL** in your `.env` file:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=complaints_management
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

5. **Run migrations and seeders**
   ```bash
   php artisan migrate --seed
   ```

6. **Create storage link**
   ```bash
   php artisan storage:link
   ```

7. **Start the application**
   ```bash
   php artisan serve
   ```

## Default Users

After running the seeders, you can log in with these demo accounts:

- **Admin**: <EMAIL> / password
- **Teacher**: <EMAIL> / password
- **Student**: <EMAIL> / password

## Usage

### For Students
- Access the student portal at `/student/dashboard`
- Submit new complaints with attachments
- Track complaint status and progress
- Receive notifications on updates
- Rate satisfaction after resolution

### For Teachers/Staff
- Access the admin panel at `/admin`
- Manage assigned complaints
- Update status and add comments
- View performance metrics
- Generate reports

### For Administrators
- Full system access through admin panel
- User management and role assignment
- System configuration and settings
- Advanced analytics and reporting
- SLA and escalation management

## Key Commands

### Scheduled Jobs
```bash
# Process complaint escalations (runs hourly)
php artisan complaints:process-escalations

# Send SLA warning notifications (runs every 4 hours)
php artisan complaints:sla-warnings

# Start queue worker for background jobs
php artisan queue:work
```

### Manual Operations
```bash
# Clear application cache
php artisan optimize:clear

# Run tests
php artisan test

# Generate application key
php artisan key:generate
```

## Development Environment Setup

### Tailwind CSS Configuration

The project uses Tailwind CSS for styling. The configuration is in `tailwind.config.js` and includes:

- Custom color schemes
- Form plugins
- Typography plugins

### Custom Components

We've created custom Tailwind components for:

- Status badges
- Priority badges
- Complaint cards
- Form inputs
- Buttons

These components are defined in `resources/css/app.css` using the `@layer components` directive.

### Filament Admin Panel

The admin panel is built with Filament and includes:

- Dashboard widgets for complaint statistics
- Resources for managing complaints, users, and categories
- Custom pages for reports and settings
- Navigation groups for organization

## UI/UX Design

The UI/UX design follows these principles:

- Clean, minimalist interface
- Consistent color coding for status and priority
- Mobile-first responsive design
- Accessible forms and components
- Clear navigation and information hierarchy

## License

This project is licensed under the MIT License.
