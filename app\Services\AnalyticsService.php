<?php

namespace App\Services;

use App\Models\ComplaintAnalytics;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AnalyticsService
{
    /**
     * Get trend analysis for complaint patterns
     */
    public function getTrendAnalysis(string $period = '12months'): array
    {
        [$startDate, $endDate] = $this->getPeriodDates($period);
        
        $analytics = ComplaintAnalytics::whereBetween('submitted_date', [$startDate, $endDate])
            ->selectRaw("
                DATE_FORMAT(submitted_date, '%Y-%m') as month,
                COUNT(*) as total_complaints,
                AVG(resolution_time_hours) as avg_resolution_time,
                SUM(CASE WHEN sla_breached = 1 THEN 1 ELSE 0 END) as sla_breaches,
                AVG(satisfaction_rating) as avg_satisfaction
            ")
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return [
            'volume_trend' => $this->calculateTrend($analytics->pluck('total_complaints')),
            'resolution_trend' => $this->calculateTrend($analytics->pluck('avg_resolution_time')),
            'sla_trend' => $this->calculateTrend($analytics->pluck('sla_breaches')),
            'satisfaction_trend' => $this->calculateTrend($analytics->pluck('avg_satisfaction')),
            'data' => $analytics,
        ];
    }

    /**
     * Implement predictive analysis for complaint volume
     */
    public function getPredictiveAnalysis(): array
    {
        // Get historical data for the last 12 months
        $historicalData = ComplaintAnalytics::where('submitted_date', '>=', now()->subMonths(12))
            ->selectRaw("
                DATE_FORMAT(submitted_date, '%Y-%m') as month,
                COUNT(*) as complaint_count
            ")
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Simple linear regression for prediction
        $prediction = $this->calculateLinearRegression($historicalData->pluck('complaint_count')->toArray());
        
        // Seasonal analysis
        $seasonalFactors = $this->calculateSeasonalFactors($historicalData);
        
        return [
            'next_month_prediction' => round($prediction['next_value']),
            'trend_direction' => $prediction['slope'] > 0 ? 'increasing' : 'decreasing',
            'confidence_level' => $prediction['r_squared'],
            'seasonal_factors' => $seasonalFactors,
            'recommendations' => $this->generateRecommendations($prediction, $seasonalFactors),
        ];
    }

    /**
     * Build performance metrics for staff productivity
     */
    public function getStaffPerformanceMetrics(): array
    {
        $staffMetrics = User::role(['admin', 'teacher'])
            ->select('users.id', 'users.name')
            ->leftJoin('complaint_analytics', 'users.id', '=', 'complaint_analytics.resolver_id')
            ->selectRaw("
                users.id,
                users.name,
                COUNT(complaint_analytics.id) as total_resolved,
                AVG(complaint_analytics.resolution_time_hours) as avg_resolution_time,
                SUM(CASE WHEN complaint_analytics.sla_breached = 0 THEN 1 ELSE 0 END) as sla_compliant,
                AVG(complaint_analytics.satisfaction_rating) as avg_satisfaction,
                COUNT(CASE WHEN complaint_analytics.submitted_date >= ? THEN 1 END) as this_month_resolved
            ", [now()->startOfMonth()])
            ->where('complaint_analytics.submitted_date', '>=', now()->subMonths(6))
            ->groupBy('users.id', 'users.name')
            ->get();

        return $staffMetrics->map(function ($staff) {
            $slaCompliance = $staff->total_resolved > 0 ? 
                ($staff->sla_compliant / $staff->total_resolved) * 100 : 0;
            
            return [
                'id' => $staff->id,
                'name' => $staff->name,
                'total_resolved' => $staff->total_resolved,
                'avg_resolution_time' => round($staff->avg_resolution_time ?? 0, 1),
                'sla_compliance_rate' => round($slaCompliance, 1),
                'avg_satisfaction' => round($staff->avg_satisfaction ?? 0, 1),
                'this_month_resolved' => $staff->this_month_resolved,
                'productivity_score' => $this->calculateProductivityScore($staff),
            ];
        })->sortByDesc('productivity_score')->values()->toArray();
    }

    /**
     * Create cost analysis for complaint resolution
     */
    public function getCostAnalysis(string $period = '12months'): array
    {
        [$startDate, $endDate] = $this->getPeriodDates($period);
        
        $costData = ComplaintAnalytics::whereBetween('submitted_date', [$startDate, $endDate])
            ->selectRaw("
                priority,
                COUNT(*) as complaint_count,
                SUM(cost_estimate) as total_cost,
                AVG(cost_estimate) as avg_cost_per_complaint,
                AVG(resolution_time_hours) as avg_resolution_time
            ")
            ->groupBy('priority')
            ->get();

        $totalCost = $costData->sum('total_cost');
        $totalComplaints = $costData->sum('complaint_count');

        return [
            'total_cost' => $totalCost,
            'avg_cost_per_complaint' => $totalComplaints > 0 ? $totalCost / $totalComplaints : 0,
            'cost_by_priority' => $costData->toArray(),
            'cost_trends' => $this->getCostTrends($startDate, $endDate),
            'cost_savings_opportunities' => $this->identifyCostSavings($costData),
        ];
    }

    /**
     * Implement comparative analysis (month-over-month, year-over-year)
     */
    public function getComparativeAnalysis(): array
    {
        $currentMonth = $this->getMonthlyMetrics(now()->startOfMonth(), now());
        $lastMonth = $this->getMonthlyMetrics(
            now()->subMonth()->startOfMonth(), 
            now()->subMonth()->endOfMonth()
        );
        $currentYear = $this->getYearlyMetrics(now()->startOfYear(), now());
        $lastYear = $this->getYearlyMetrics(
            now()->subYear()->startOfYear(), 
            now()->subYear()->endOfYear()
        );

        return [
            'month_over_month' => $this->calculateComparison($currentMonth, $lastMonth),
            'year_over_year' => $this->calculateComparison($currentYear, $lastYear),
            'current_month' => $currentMonth,
            'last_month' => $lastMonth,
            'current_year' => $currentYear,
            'last_year' => $lastYear,
        ];
    }

    private function getPeriodDates(string $period): array
    {
        return match ($period) {
            '30days' => [now()->subDays(30), now()],
            '3months' => [now()->subMonths(3), now()],
            '6months' => [now()->subMonths(6), now()],
            '12months' => [now()->subMonths(12), now()],
            'ytd' => [now()->startOfYear(), now()],
            default => [now()->subMonths(12), now()],
        };
    }

    private function calculateTrend(Collection $data): array
    {
        $values = $data->filter()->values()->toArray();
        if (count($values) < 2) {
            return ['direction' => 'stable', 'percentage' => 0];
        }

        $first = array_slice($values, 0, ceil(count($values) / 2));
        $second = array_slice($values, floor(count($values) / 2));
        
        $firstAvg = array_sum($first) / count($first);
        $secondAvg = array_sum($second) / count($second);
        
        $percentage = $firstAvg > 0 ? (($secondAvg - $firstAvg) / $firstAvg) * 100 : 0;
        
        return [
            'direction' => $percentage > 5 ? 'increasing' : ($percentage < -5 ? 'decreasing' : 'stable'),
            'percentage' => round($percentage, 1),
        ];
    }

    private function calculateLinearRegression(array $data): array
    {
        $n = count($data);
        if ($n < 2) return ['next_value' => 0, 'slope' => 0, 'r_squared' => 0];

        $x = range(1, $n);
        $y = $data;
        
        $sumX = array_sum($x);
        $sumY = array_sum($y);
        $sumXY = array_sum(array_map(fn($i) => $x[$i] * $y[$i], range(0, $n-1)));
        $sumX2 = array_sum(array_map(fn($val) => $val * $val, $x));
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;
        
        $nextValue = $slope * ($n + 1) + $intercept;
        
        return [
            'next_value' => max(0, $nextValue),
            'slope' => $slope,
            'r_squared' => $this->calculateRSquared($x, $y, $slope, $intercept),
        ];
    }

    private function calculateRSquared(array $x, array $y, float $slope, float $intercept): float
    {
        $yMean = array_sum($y) / count($y);
        $ssRes = 0;
        $ssTot = 0;
        
        for ($i = 0; $i < count($y); $i++) {
            $predicted = $slope * $x[$i] + $intercept;
            $ssRes += pow($y[$i] - $predicted, 2);
            $ssTot += pow($y[$i] - $yMean, 2);
        }
        
        return $ssTot > 0 ? 1 - ($ssRes / $ssTot) : 0;
    }

    private function calculateSeasonalFactors(Collection $data): array
    {
        $monthlyAverages = [];
        foreach ($data as $item) {
            $month = (int) substr($item->month, -2);
            $monthlyAverages[$month][] = $item->complaint_count;
        }

        $factors = [];
        $overallAvg = $data->avg('complaint_count');
        
        for ($month = 1; $month <= 12; $month++) {
            if (isset($monthlyAverages[$month])) {
                $monthAvg = array_sum($monthlyAverages[$month]) / count($monthlyAverages[$month]);
                $factors[$month] = $overallAvg > 0 ? $monthAvg / $overallAvg : 1;
            } else {
                $factors[$month] = 1;
            }
        }

        return $factors;
    }

    private function generateRecommendations(array $prediction, array $seasonalFactors): array
    {
        $recommendations = [];
        
        if ($prediction['slope'] > 0) {
            $recommendations[] = 'Complaint volume is trending upward. Consider increasing staff capacity.';
        }
        
        $highSeasonMonths = array_keys(array_filter($seasonalFactors, fn($factor) => $factor > 1.2));
        if (!empty($highSeasonMonths)) {
            $recommendations[] = 'High complaint months: ' . implode(', ', array_map(fn($m) => date('F', mktime(0, 0, 0, $m, 1)), $highSeasonMonths));
        }
        
        return $recommendations;
    }

    private function calculateProductivityScore($staff): float
    {
        $resolutionScore = $staff->avg_resolution_time > 0 ? min(100, 100 / ($staff->avg_resolution_time / 24)) : 0;
        $volumeScore = min(100, $staff->total_resolved * 2);
        $satisfactionScore = ($staff->avg_satisfaction ?? 0) * 20;
        $slaScore = $staff->total_resolved > 0 ? ($staff->sla_compliant / $staff->total_resolved) * 100 : 0;
        
        return ($resolutionScore + $volumeScore + $satisfactionScore + $slaScore) / 4;
    }

    private function getCostTrends(Carbon $startDate, Carbon $endDate): array
    {
        return ComplaintAnalytics::whereBetween('submitted_date', [$startDate, $endDate])
            ->selectRaw("
                DATE_FORMAT(submitted_date, '%Y-%m') as month,
                SUM(cost_estimate) as monthly_cost
            ")
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    private function identifyCostSavings(Collection $costData): array
    {
        $opportunities = [];
        
        $highCostPriorities = $costData->where('avg_cost_per_complaint', '>', 500);
        foreach ($highCostPriorities as $priority) {
            $opportunities[] = "Reduce {$priority->priority} priority complaint resolution time to save costs";
        }
        
        return $opportunities;
    }

    private function getMonthlyMetrics(Carbon $start, Carbon $end): array
    {
        return ComplaintAnalytics::whereBetween('submitted_date', [$start, $end])
            ->selectRaw("
                COUNT(*) as total_complaints,
                AVG(resolution_time_hours) as avg_resolution_time,
                SUM(CASE WHEN sla_breached = 0 THEN 1 ELSE 0 END) / COUNT(*) * 100 as sla_compliance,
                AVG(satisfaction_rating) as avg_satisfaction,
                SUM(cost_estimate) as total_cost
            ")
            ->first()
            ->toArray();
    }

    private function getYearlyMetrics(Carbon $start, Carbon $end): array
    {
        return $this->getMonthlyMetrics($start, $end);
    }

    private function calculateComparison(array $current, array $previous): array
    {
        $comparison = [];
        
        foreach ($current as $key => $value) {
            if (isset($previous[$key]) && $previous[$key] > 0) {
                $change = (($value - $previous[$key]) / $previous[$key]) * 100;
                $comparison[$key] = [
                    'current' => $value,
                    'previous' => $previous[$key],
                    'change_percentage' => round($change, 1),
                    'direction' => $change > 0 ? 'increase' : ($change < 0 ? 'decrease' : 'stable'),
                ];
            } else {
                $comparison[$key] = [
                    'current' => $value,
                    'previous' => $previous[$key] ?? 0,
                    'change_percentage' => 0,
                    'direction' => 'stable',
                ];
            }
        }
        
        return $comparison;
    }
}
