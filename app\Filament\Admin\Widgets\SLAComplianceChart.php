<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Complaint;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class SLAComplianceChart extends ChartWidget
{
    protected static ?string $heading = 'SLA Compliance';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $compliantCount = Complaint::whereIn('status', ['resolved', 'closed'])
            ->whereNotNull('due_date')
            ->whereNotNull('resolution_date')
            ->where('resolution_date', '<=', 'due_date')
            ->count();

        $nonCompliantCount = Complaint::whereIn('status', ['resolved', 'closed'])
            ->whereNotNull('due_date')
            ->whereNotNull('resolution_date')
            ->where('resolution_date', '>', 'due_date')
            ->count();

        $pendingCount = Complaint::whereNotIn('status', ['resolved', 'closed'])
            ->count();

        return [
            'datasets' => [
                [
                    'label' => 'SLA Compliance',
                    'data' => [$compliantCount, $nonCompliantCount, $pendingCount],
                    'backgroundColor' => ['#10b981', '#ef4444', '#f59e0b'],
                ],
            ],
            'labels' => ['Within SLA', 'Outside SLA', 'Pending'],
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
