<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class CreateDemoUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:create-demo';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create demo users for the complaints system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating demo users...');

        // Create roles first
        $this->info('Creating roles...');
        Role::firstOrCreate(['name' => 'admin']);
        Role::firstOrCreate(['name' => 'teacher']);
        Role::firstOrCreate(['name' => 'student']);
        $this->info('✅ Roles created');

        // Delete existing demo users if they exist
        User::whereIn('email', [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ])->delete();
        $this->info('✅ Cleaned up existing demo users');

        // Create admin user
        $admin = User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'department' => 'IT',
            'user_type' => 'admin',
        ]);
        $admin->assignRole('admin');
        $this->info('✅ Admin user created: <EMAIL> / password');

        // Create teacher user
        $teacher = User::create([
            'name' => 'John Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'department' => 'Academic',
            'user_type' => 'teacher',
        ]);
        $teacher->assignRole('teacher');
        $this->info('✅ Teacher user created: <EMAIL> / password');

        // Create student user
        $student = User::create([
            'name' => 'Jane Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'department' => 'Computer Science',
            'user_type' => 'student',
        ]);
        $student->assignRole('student');
        $this->info('✅ Student user created: <EMAIL> / password');

        $this->info('');
        $this->info('🎉 Demo users created successfully!');
        $this->info('');
        $this->info('Login credentials:');
        $this->info('• Admin: <EMAIL> / password');
        $this->info('• Teacher: <EMAIL> / password');
        $this->info('• Student: <EMAIL> / password');
        $this->info('');
        $this->info('Access URLs:');
        $this->info('• Admin Panel: http://localhost:8000/admin');
        $this->info('• Student Portal: http://localhost:8000/student/dashboard');

        return 0;
    }
}
