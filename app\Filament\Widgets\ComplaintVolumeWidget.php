<?php

namespace App\Filament\Widgets;

use App\Models\ComplaintAnalytics;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class ComplaintVolumeWidget extends ChartWidget
{
    protected static ?string $heading = 'Complaint Volume Trends';

    protected static ?int $sort = 5;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $maxHeight = '300px';

    public ?string $filter = '12months';

    protected function getData(): array
    {
        $data = $this->getComplaintVolumeData();

        return [
            'datasets' => [
                [
                    'label' => 'Complaints Submitted',
                    'data' => $data['submitted'],
                    'borderColor' => '#f59e0b',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'Complaints Resolved',
                    'data' => $data['resolved'],
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'Complaints Closed',
                    'data' => $data['closed'],
                    'borderColor' => '#6366f1',
                    'backgroundColor' => 'rgba(99, 102, 241, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $data['labels'],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getFilters(): ?array
    {
        return [
            '7days' => 'Last 7 days',
            '30days' => 'Last 30 days',
            '3months' => 'Last 3 months',
            '6months' => 'Last 6 months',
            '12months' => 'Last 12 months',
            'ytd' => 'Year to date',
        ];
    }

    private function getComplaintVolumeData(): array
    {
        $period = $this->filter ?? '12months';
        
        [$startDate, $endDate, $groupBy, $format] = $this->getPeriodSettings($period);
        
        $analytics = ComplaintAnalytics::whereBetween('submitted_date', [$startDate, $endDate])
            ->selectRaw("
                DATE_FORMAT(submitted_date, '{$format}') as period,
                COUNT(*) as submitted_count,
                SUM(CASE WHEN status IN ('resolved', 'closed') THEN 1 ELSE 0 END) as resolved_count,
                SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_count
            ")
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        $labels = [];
        $submitted = [];
        $resolved = [];
        $closed = [];

        // Generate all periods in range
        $current = $startDate->copy();
        while ($current <= $endDate) {
            $periodKey = $current->format($this->getPhpFormat($format));
            $labels[] = $this->formatLabel($current, $period);
            
            $data = $analytics->firstWhere('period', $periodKey);
            $submitted[] = $data?->submitted_count ?? 0;
            $resolved[] = $data?->resolved_count ?? 0;
            $closed[] = $data?->closed_count ?? 0;
            
            $current = $this->incrementPeriod($current, $groupBy);
        }

        return [
            'labels' => $labels,
            'submitted' => $submitted,
            'resolved' => $resolved,
            'closed' => $closed,
        ];
    }

    private function getPeriodSettings(string $period): array
    {
        return match ($period) {
            '7days' => [
                now()->subDays(7),
                now(),
                'day',
                '%Y-%m-%d'
            ],
            '30days' => [
                now()->subDays(30),
                now(),
                'day',
                '%Y-%m-%d'
            ],
            '3months' => [
                now()->subMonths(3),
                now(),
                'week',
                '%Y-%u'
            ],
            '6months' => [
                now()->subMonths(6),
                now(),
                'month',
                '%Y-%m'
            ],
            '12months' => [
                now()->subMonths(12),
                now(),
                'month',
                '%Y-%m'
            ],
            'ytd' => [
                now()->startOfYear(),
                now(),
                'month',
                '%Y-%m'
            ],
            default => [
                now()->subMonths(12),
                now(),
                'month',
                '%Y-%m'
            ],
        };
    }

    private function getPhpFormat(string $mysqlFormat): string
    {
        return match ($mysqlFormat) {
            '%Y-%m-%d' => 'Y-m-d',
            '%Y-%u' => 'Y-W',
            '%Y-%m' => 'Y-m',
            default => 'Y-m',
        };
    }

    private function formatLabel(Carbon $date, string $period): string
    {
        return match ($period) {
            '7days', '30days' => $date->format('M j'),
            '3months' => 'Week ' . $date->format('W'),
            '6months', '12months', 'ytd' => $date->format('M Y'),
            default => $date->format('M Y'),
        };
    }

    private function incrementPeriod(Carbon $date, string $groupBy): Carbon
    {
        return match ($groupBy) {
            'day' => $date->addDay(),
            'week' => $date->addWeek(),
            'month' => $date->addMonth(),
            default => $date->addMonth(),
        };
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'precision' => 0,
                    ],
                ],
            ],
            'interaction' => [
                'intersect' => false,
                'mode' => 'index',
            ],
        ];
    }
}
