<?php

namespace App\Providers;

use Illuminate\Database\Events\ConnectionEstablished;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class SQLiteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only apply SQLite optimizations if using SQLite
        if (config('database.default') === 'sqlite') {
            $this->optimizeSQLiteConnection();
        }
    }

    /**
     * Optimize SQLite connection with performance pragmas
     */
    private function optimizeSQLiteConnection(): void
    {
        Event::listen(ConnectionEstablished::class, function ($event) {
            if ($event->connection->getDriverName() === 'sqlite') {
                $pragmas = config('sqlite.pragmas', []);
                
                foreach ($pragmas as $pragma => $value) {
                    if ($value !== null) {
                        $event->connection->statement("PRAGMA {$pragma} = {$value}");
                    } else {
                        $event->connection->statement("PRAGMA {$pragma}");
                    }
                }

                // Additional performance optimizations
                $this->applySQLiteOptimizations($event->connection);
            }
        });
    }

    /**
     * Apply additional SQLite optimizations
     */
    private function applySQLiteOptimizations($connection): void
    {
        try {
            // Enable Write-Ahead Logging for better concurrency
            $connection->statement('PRAGMA journal_mode = WAL');
            
            // Set synchronous mode for better performance
            $connection->statement('PRAGMA synchronous = NORMAL');
            
            // Increase cache size for better performance
            $connection->statement('PRAGMA cache_size = 10000');
            
            // Use memory for temporary storage
            $connection->statement('PRAGMA temp_store = MEMORY');
            
            // Enable memory mapping for better I/O performance
            $connection->statement('PRAGMA mmap_size = 268435456'); // 256MB
            
            // Set busy timeout to handle concurrent access
            $connection->statement('PRAGMA busy_timeout = 30000'); // 30 seconds
            
            // Enable foreign key constraints
            $connection->statement('PRAGMA foreign_keys = ON');
            
            // Optimize database
            $connection->statement('PRAGMA optimize');
            
        } catch (\Exception $e) {
            // Log error but don't fail the application
            logger()->warning('SQLite optimization failed: ' . $e->getMessage());
        }
    }
}
