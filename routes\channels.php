<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Complaint-specific channels
Broadcast::channel('complaint.{complaintId}', function ($user, $complaintId) {
    // Users can listen to complaint updates if they are:
    // 1. The submitter of the complaint
    // 2. Assigned to the complaint
    // 3. Have admin or teacher role
    $complaint = \App\Models\Complaint::find($complaintId);
    
    if (!$complaint) {
        return false;
    }
    
    return $user->id === $complaint->submitted_by ||
           $user->id === $complaint->assigned_to ||
           $user->hasAnyRole(['admin', 'teacher']);
});

// Admin dashboard channel
Broadcast::channel('admin.dashboard', function ($user) {
    return $user->hasAnyRole(['admin', 'teacher']);
});

// Student notifications channel
Broadcast::channel('student.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId && $user->hasRole('student');
});
