# Troubleshooting Guide - Complaints Management System

## Database Connection Issues

### Error: "No connection could be established" / "Connection refused"

**Symptoms:**
```
SQLSTATE[HY000] [2002] No connection could be established because the target machine actively refused it
```

**Solutions:**

#### Option 1: Use SQLite (Recommended for Development)

1. **Update your `.env` file:**
   ```env
   DB_CONNECTION=sqlite
   ```
   Comment out or remove MySQL settings:
   ```env
   # DB_HOST=127.0.0.1
   # DB_PORT=3306
   # DB_DATABASE=complaints_management
   # DB_USERNAME=root
   # DB_PASSWORD=
   ```

2. **Create SQLite database file:**
   ```bash
   # Windows
   echo. > database\database.sqlite
   
   # Linux/Mac
   touch database/database.sqlite
   ```

3. **Run migrations:**
   ```bash
   php artisan migrate --seed
   ```

#### Option 2: Setup MySQL

1. **Install MySQL:**
   - Download from https://dev.mysql.com/downloads/mysql/
   - Or use XAMPP/WAMP/MAMP

2. **Create database:**
   ```sql
   CREATE DATABASE complaints_management;
   ```

3. **Update `.env` file:**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=complaints_management
   DB_USERNAME=root
   DB_PASSWORD=your_password
   ```

#### Option 3: Use Herd's MySQL (if using Laravel Herd)

1. **Start Herd's MySQL service**
2. **Update `.env` file:**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=complaints_management
   DB_USERNAME=root
   DB_PASSWORD=
   ```

## Quick Setup

### Automated Setup (Recommended)

**Windows:**
```bash
setup.bat
```

**Linux/Mac:**
```bash
chmod +x setup.sh
./setup.sh
```

### Manual Setup

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Generate application key:**
   ```bash
   php artisan key:generate
   ```

3. **Setup database (SQLite):**
   ```bash
   # Windows
   echo. > database\database.sqlite
   
   # Linux/Mac
   touch database/database.sqlite
   ```

4. **Run migrations and seeders:**
   ```bash
   php artisan migrate --seed
   ```

5. **Create storage link:**
   ```bash
   php artisan storage:link
   ```

6. **Clear cache:**
   ```bash
   php artisan config:clear
   php artisan cache:clear
   ```

7. **Start development server:**
   ```bash
   php artisan serve
   ```

## Common Issues

### Issue: "Class not found" errors

**Solution:**
```bash
composer dump-autoload
php artisan config:clear
php artisan cache:clear
```

### Issue: "Permission denied" on storage

**Solution:**
```bash
# Linux/Mac
chmod -R 775 storage bootstrap/cache

# Windows (run as administrator)
icacls storage /grant Everyone:F /T
icacls bootstrap\cache /grant Everyone:F /T
```

### Issue: "Key not found" error

**Solution:**
```bash
php artisan key:generate
```

### Issue: Widgets not loading

**Solution:**
```bash
php artisan filament:optimize
php artisan view:clear
```

### Issue: Routes not working

**Solution:**
```bash
php artisan route:clear
php artisan config:clear
```

## Demo Credentials

After successful setup, use these credentials:

- **Admin**: <EMAIL> / password
- **Teacher**: <EMAIL> / password
- **Student**: <EMAIL> / password

## URLs

- **Home/Admin Panel**: http://localhost:8000
- **Student Portal**: http://localhost:8000/student/dashboard
- **API Health Check**: http://localhost:8000/api/public/health

## System Requirements

- PHP 8.2 or higher
- Composer
- Node.js & NPM (for asset compilation)
- SQLite (recommended) or MySQL 8.0+

## Getting Help

1. **Check Laravel logs:**
   ```bash
   tail -f storage/logs/laravel.log
   ```

2. **Enable debug mode in `.env`:**
   ```env
   APP_DEBUG=true
   ```

3. **Run diagnostics:**
   ```bash
   php artisan about
   php artisan config:show database
   ```

4. **Test database connection:**
   ```bash
   php artisan tinker
   >>> DB::connection()->getPdo();
   ```

## Performance Tips

1. **Optimize for production:**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   composer install --optimize-autoloader --no-dev
   ```

2. **Queue processing:**
   ```bash
   php artisan queue:work
   ```

3. **Schedule runner:**
   ```bash
   php artisan schedule:work
   ```
