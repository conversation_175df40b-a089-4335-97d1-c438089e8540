<?php

namespace App\Notifications;

use App\Models\Complaint;
use App\Models\ComplaintEscalation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintEscalated extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Complaint $complaint,
        public ComplaintEscalation $escalation
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $isEscalatee = $notifiable->id === $this->escalation->escalated_to;
        
        if ($isEscalatee) {
            return (new MailMessage)
                ->subject('Complaint Escalated to You - Level ' . $this->escalation->escalation_level)
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line('A complaint has been escalated to you for urgent attention.')
                ->line('**Reference Number:** ' . $this->complaint->reference_number)
                ->line('**Title:** ' . $this->complaint->title)
                ->line('**Escalation Level:** ' . $this->escalation->getLevelName())
                ->line('**Escalation Reason:** ' . $this->escalation->reason)
                ->line('**Original Submitter:** ' . $this->complaint->submitter->name)
                ->line('**Escalated By:** ' . $this->escalation->escalatedBy->name)
                ->action('Review Complaint', url('/admin/complaints/' . $this->complaint->id))
                ->line('Please review and take immediate action on this escalated complaint.');
        } else {
            return (new MailMessage)
                ->subject('Your Complaint Has Been Escalated')
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line('Your complaint has been escalated for faster resolution.')
                ->line('**Reference Number:** ' . $this->complaint->reference_number)
                ->line('**Title:** ' . $this->complaint->title)
                ->line('**Escalation Level:** ' . $this->escalation->getLevelName())
                ->line('**Escalation Reason:** ' . $this->escalation->reason)
                ->line('**Escalated To:** ' . $this->escalation->escalatedTo->name)
                ->action('View Complaint', url('/student/complaints/' . $this->complaint->id))
                ->line('We are working to resolve your complaint as quickly as possible.');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $isEscalatee = $notifiable->id === $this->escalation->escalated_to;
        
        return [
            'complaint_id' => $this->complaint->id,
            'reference_number' => $this->complaint->reference_number,
            'title' => $this->complaint->title,
            'escalation_id' => $this->escalation->id,
            'escalation_level' => $this->escalation->escalation_level,
            'escalation_reason' => $this->escalation->reason,
            'message' => $isEscalatee 
                ? 'A complaint has been escalated to you at level ' . $this->escalation->escalation_level
                : 'Your complaint has been escalated to level ' . $this->escalation->escalation_level,
            'action_url' => $isEscalatee 
                ? url('/admin/complaints/' . $this->complaint->id)
                : url('/student/complaints/' . $this->complaint->id),
        ];
    }
}
