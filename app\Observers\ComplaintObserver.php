<?php

namespace App\Observers;

use App\Jobs\AggregateComplaintAnalytics;
use App\Models\Complaint;

class ComplaintObserver
{
    /**
     * Handle the Complaint "created" event.
     */
    public function created(Complaint $complaint): void
    {
        // Dispatch analytics aggregation job
        AggregateComplaintAnalytics::dispatch($complaint->id)->delay(now()->addMinutes(5));
    }

    /**
     * Handle the Complaint "updated" event.
     */
    public function updated(Complaint $complaint): void
    {
        // Check if important fields were updated
        $importantFields = [
            'status',
            'priority',
            'assigned_to',
            'resolution_date',
            'satisfaction_rating',
            'due_date',
        ];

        $hasImportantChanges = false;
        foreach ($importantFields as $field) {
            if ($complaint->wasChanged($field)) {
                $hasImportantChanges = true;
                break;
            }
        }

        if ($hasImportantChanges) {
            // Dispatch analytics aggregation job
            AggregateComplaintAnalytics::dispatch($complaint->id)->delay(now()->addMinutes(2));
        }
    }

    /**
     * Handle the Complaint "deleted" event.
     */
    public function deleted(Complaint $complaint): void
    {
        // Analytics record will be automatically deleted due to cascade constraint
    }

    /**
     * Handle the Complaint "restored" event.
     */
    public function restored(Complaint $complaint): void
    {
        // Re-create analytics record
        AggregateComplaintAnalytics::dispatch($complaint->id)->delay(now()->addMinutes(5));
    }

    /**
     * Handle the Complaint "force deleted" event.
     */
    public function forceDeleted(Complaint $complaint): void
    {
        // Analytics record will be automatically deleted due to cascade constraint
    }
}
