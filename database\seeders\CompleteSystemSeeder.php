<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\ComplaintCategory;
use App\Models\Complaint;
use App\Models\ComplaintComment;
use Illuminate\Support\Facades\Hash;

class CompleteSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for complaint management
        $permissions = [
            // Complaint permissions
            'view own complaints',
            'view all complaints',
            'create complaints',
            'edit own complaints',
            'edit all complaints',
            'delete complaints',
            'assign complaints',
            'resolve complaints',
            'escalate complaints',
            
            // Response permissions
            'view responses',
            'create responses',
            'edit responses',
            'delete responses',
            
            // User management permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Analytics and reporting
            'view analytics',
            'export reports',
            'view audit logs',
            
            // System administration
            'manage categories',
            'manage system settings',
            'access admin panel',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        
        // Student Role
        $studentRole = Role::firstOrCreate(['name' => 'student']);
        $studentRole->syncPermissions([
            'view own complaints',
            'create complaints',
            'edit own complaints',
            'view responses',
        ]);

        // Teacher Role
        $teacherRole = Role::firstOrCreate(['name' => 'teacher']);
        $teacherRole->syncPermissions([
            'view all complaints',
            'edit all complaints',
            'assign complaints',
            'resolve complaints',
            'view responses',
            'create responses',
            'edit responses',
            'view analytics',
        ]);

        // Admin Role
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions(Permission::all());

        // Create complaint categories
        $categories = [
            [
                'name' => 'Academic Issues',
                'description' => 'Issues related to courses, grading, curriculum',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Facilities & Infrastructure',
                'description' => 'Problems with buildings, equipment, maintenance',
                'color' => '#10B981',
                'sort_order' => 2,
            ],
            [
                'name' => 'Student Services',
                'description' => 'Issues with registration, financial aid, counseling',
                'color' => '#F59E0B',
                'sort_order' => 3,
            ],
            [
                'name' => 'Faculty & Staff',
                'description' => 'Concerns about teaching quality, staff behavior',
                'color' => '#EF4444',
                'sort_order' => 4,
            ],
            [
                'name' => 'Technology & IT',
                'description' => 'Computer lab, network, software issues',
                'color' => '#8B5CF6',
                'sort_order' => 5,
            ],
            [
                'name' => 'Safety & Security',
                'description' => 'Campus safety, security concerns',
                'color' => '#F97316',
                'sort_order' => 6,
            ],
            [
                'name' => 'Other',
                'description' => 'General complaints not covered by other categories',
                'color' => '#6B7280',
                'sort_order' => 7,
            ],
        ];

        foreach ($categories as $category) {
            ComplaintCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }

        // Create users
        
        // Admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
                'user_type' => 'admin',
                'department' => 'Administration',
                'phone' => '+1234567890',
                'is_active' => true,
            ]
        );
        $admin->assignRole('admin');

        // Teacher users
        $teachers = [
            [
                'name' => 'Dr. John Smith',
                'email' => '<EMAIL>',
                'department' => 'Computer Science',
                'phone' => '+1234567891',
            ],
            [
                'name' => 'Prof. Sarah Johnson',
                'email' => '<EMAIL>',
                'department' => 'Engineering',
                'phone' => '+1234567892',
            ],
            [
                'name' => 'Dr. Michael Brown',
                'email' => '<EMAIL>',
                'department' => 'Business',
                'phone' => '+1234567893',
            ],
        ];

        foreach ($teachers as $teacherData) {
            $teacher = User::firstOrCreate(
                ['email' => $teacherData['email']],
                array_merge($teacherData, [
                    'password' => Hash::make('password'),
                    'user_type' => 'teacher',
                    'student_id' => 'TCH' . str_pad(array_search($teacherData, $teachers) + 1, 3, '0', STR_PAD_LEFT),
                    'is_active' => true,
                ])
            );
            $teacher->assignRole('teacher');
        }

        // Student users
        $students = [
            [
                'name' => 'Alice Wilson',
                'email' => '<EMAIL>',
                'department' => 'Computer Science',
                'student_id' => 'CS2024001',
                'phone' => '+1234567894',
            ],
            [
                'name' => 'Bob Davis',
                'email' => '<EMAIL>',
                'department' => 'Engineering',
                'student_id' => 'ENG2024001',
                'phone' => '+1234567895',
            ],
            [
                'name' => 'Carol Martinez',
                'email' => '<EMAIL>',
                'department' => 'Business',
                'student_id' => 'BUS2024001',
                'phone' => '+1234567896',
            ],
            [
                'name' => 'David Lee',
                'email' => '<EMAIL>',
                'department' => 'Arts',
                'student_id' => 'ART2024001',
                'phone' => '+1234567897',
            ],
            [
                'name' => 'Emma Thompson',
                'email' => '<EMAIL>',
                'department' => 'Sciences',
                'student_id' => 'SCI2024001',
                'phone' => '+1234567898',
            ],
        ];

        foreach ($students as $studentData) {
            $student = User::firstOrCreate(
                ['email' => $studentData['email']],
                array_merge($studentData, [
                    'password' => Hash::make('password'),
                    'user_type' => 'student',
                    'is_active' => true,
                ])
            );
            $student->assignRole('student');
        }

        // Create sample complaints
        $this->createSampleComplaints();

        $this->command->info('Complete system seeded successfully!');
        $this->command->info('Login credentials:');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Teacher: <EMAIL> / password');
        $this->command->info('Student: <EMAIL> / password');
    }

    private function createSampleComplaints()
    {
        $students = User::where('user_type', 'student')->get();
        $teachers = User::where('user_type', 'teacher')->get();
        $categories = ComplaintCategory::all();

        $sampleComplaints = [
            [
                'title' => 'Broken projector in Computer Lab 1',
                'description' => 'The projector in Computer Lab 1 has been malfunctioning for the past week. It keeps shutting down randomly during lectures, disrupting the learning process.',
                'priority' => 'high',
                'status' => 'new',
            ],
            [
                'title' => 'Unfair grading in Mathematics course',
                'description' => 'I believe my final exam was graded unfairly. I would like to request a review of my paper as the grade does not reflect my understanding of the subject.',
                'priority' => 'medium',
                'status' => 'assigned',
            ],
            [
                'title' => 'WiFi connectivity issues in library',
                'description' => 'The WiFi connection in the library is very slow and frequently disconnects. This makes it difficult to conduct research and complete assignments.',
                'priority' => 'medium',
                'status' => 'in_progress',
            ],
            [
                'title' => 'Cafeteria food quality concerns',
                'description' => 'The quality of food in the cafeteria has declined significantly. Several students have reported stomach issues after eating there.',
                'priority' => 'high',
                'status' => 'resolved',
            ],
            [
                'title' => 'Parking shortage for students',
                'description' => 'There are not enough parking spaces for students. Many of us have to park far away and walk long distances to reach classes.',
                'priority' => 'low',
                'status' => 'closed',
            ],
        ];

        foreach ($sampleComplaints as $index => $complaintData) {
            $student = $students->random();
            $category = $categories->random();
            $teacher = $teachers->random();

            $complaint = Complaint::create([
                'reference_number' => Complaint::generateReferenceNumber(),
                'title' => $complaintData['title'],
                'description' => $complaintData['description'],
                'category_id' => $category->id,
                'submitted_by' => $student->id,
                'assigned_to' => in_array($complaintData['status'], ['assigned', 'in_progress', 'resolved', 'closed']) ? $teacher->id : null,
                'priority' => $complaintData['priority'],
                'status' => $complaintData['status'],
                'due_date' => now()->addDays(7),
                'acknowledged_at' => in_array($complaintData['status'], ['assigned', 'in_progress', 'resolved', 'closed']) ? now()->subDays(1) : null,
                'resolved_at' => in_array($complaintData['status'], ['resolved', 'closed']) ? now()->subHours(2) : null,
                'created_at' => now()->subDays(rand(1, 30)),
            ]);

            // Add sample comments for some complaints
            if (in_array($complaintData['status'], ['assigned', 'in_progress', 'resolved', 'closed'])) {
                ComplaintComment::create([
                    'complaint_id' => $complaint->id,
                    'user_id' => $teacher->id,
                    'comment' => 'Thank you for reporting this issue. We are looking into it and will provide an update soon.',
                    'is_internal' => false,
                    'created_at' => $complaint->created_at->addHours(2),
                ]);

                if ($complaintData['status'] === 'resolved' || $complaintData['status'] === 'closed') {
                    ComplaintComment::create([
                        'complaint_id' => $complaint->id,
                        'user_id' => $teacher->id,
                        'comment' => 'This issue has been resolved. Please let us know if you experience any further problems.',
                        'is_internal' => false,
                        'created_at' => $complaint->resolved_at,
                    ]);
                }
            }
        }
    }
}
