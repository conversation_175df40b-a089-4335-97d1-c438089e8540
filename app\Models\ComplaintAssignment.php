<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ComplaintAssignment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'complaint_id',
        'assigned_from',
        'assigned_to',
        'assigned_by',
        'assignment_reason',
        'assigned_at',
        'unassigned_at',
        'unassignment_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'assigned_at' => 'datetime',
        'unassigned_at' => 'datetime',
    ];

    /**
     * Get the complaint that was assigned
     */
    public function complaint(): BelongsTo
    {
        return $this->belongsTo(Complaint::class);
    }

    /**
     * Get the user who was previously assigned (if reassignment)
     */
    public function assignedFrom(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_from');
    }

    /**
     * Get the user to whom the complaint was assigned
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who made the assignment
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Check if assignment is active
     */
    public function isActive(): bool
    {
        return is_null($this->unassigned_at);
    }

    /**
     * Unassign the complaint
     */
    public function unassign(string $reason = null): void
    {
        $this->update([
            'unassigned_at' => now(),
            'unassignment_reason' => $reason,
        ]);
    }

    /**
     * Get assignment duration in hours
     */
    public function getDurationInHours(): ?float
    {
        if (!$this->unassigned_at) {
            return $this->assigned_at->diffInHours(now(), true);
        }
        
        return $this->assigned_at->diffInHours($this->unassigned_at, true);
    }

    /**
     * Get assignment duration in days
     */
    public function getDurationInDays(): ?float
    {
        $hours = $this->getDurationInHours();
        return $hours ? $hours / 24 : null;
    }
}
