<?php

namespace App\Filament\Resources\SLASettingResource\Pages;

use App\Filament\Resources\SLASettingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSLASettings extends ListRecords
{
    protected static string $resource = SLASettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
