@echo off
echo ========================================
echo QUICK ERROR FIX
echo ========================================

echo Fixing encryption errors...
php fix-encryption.php

echo.
echo Clearing caches...
php artisan config:clear 2>nul
php artisan cache:clear 2>nul
php artisan route:clear 2>nul
php artisan view:clear 2>nul

echo.
echo Setting up database...
php artisan migrate:fresh --force 2>nul
php artisan db:seed --force 2>nul

echo.
echo Creating users...
php auto-create-users.php 2>nul

echo.
echo ========================================
echo ERRORS FIXED!
echo ========================================
echo.
echo Login at: http://localhost:8000/admin
echo Credentials: <EMAIL> / 123456
echo.
echo Starting server...
php artisan serve
