<?php

namespace App\Filament\Resources\ComplaintResource\Pages;

use App\Filament\Resources\ComplaintResource;
use App\Models\Complaint;
use App\Models\ComplaintAttachment;
use App\Models\ComplaintStatusHistory;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class CreateComplaint extends CreateRecord
{
    protected static string $resource = ComplaintResource::class;
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function handleRecordCreation(array $data): Model
    {
        // Set the reference number
        $data['reference_number'] = Complaint::generateReferenceNumber();
        
        // Create the complaint
        $complaint = parent::handleRecordCreation($data);
        
        // Record the initial status
        ComplaintStatusHistory::create([
            'complaint_id' => $complaint->id,
            'status' => $complaint->status,
            'changed_by' => auth()->id(),
            'notes' => 'Complaint created',
        ]);
        
        // Handle attachments if any
        if (isset($data['attachments']) && is_array($data['attachments'])) {
            foreach ($data['attachments'] as $attachment) {
                ComplaintAttachment::create([
                    'complaint_id' => $complaint->id,
                    'file_name' => $attachment->getClientOriginalName(),
                    'file_path' => $attachment->store('complaint-attachments'),
                    'file_type' => $attachment->getMimeType(),
                    'file_size' => $attachment->getSize(),
                    'uploaded_by' => auth()->id(),
                ]);
            }
        }
        
        return $complaint;
    }
}
