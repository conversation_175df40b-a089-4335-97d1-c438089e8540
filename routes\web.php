<?php

use App\Http\Controllers\StudentPortalController;
use App\Http\Controllers\StudentDashboardController;
use App\Http\Controllers\ComplaintController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Teacher\ComplaintController as TeacherComplaintController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect('/admin');
});

// Authentication Routes
require __DIR__.'/auth.php';

// Student Portal Routes
Route::prefix('student')->name('student.')->middleware(['auth', 'verified', 'student'])->group(function () {
    Route::get('/dashboard', [StudentDashboardController::class, 'index'])->name('dashboard');
    Route::post('/dashboard/quick-submit', [StudentDashboardController::class, 'quickSubmit'])->name('dashboard.quick-submit');

    // Complaint Routes
    Route::get('/complaints', [StudentPortalController::class, 'index'])->name('complaints');
    Route::get('/complaints/create', [StudentPortalController::class, 'create'])->name('complaints.create');
    Route::post('/complaints', [StudentPortalController::class, 'store'])->name('complaints.store');
    Route::get('/complaints/{complaint}', [StudentPortalController::class, 'show'])->name('complaints.show');

    // Comment Routes
    Route::post('/complaints/{complaint}/comments', [StudentPortalController::class, 'addComment'])->name('complaints.comments.store');

    // Reopen Complaint
    Route::post('/complaints/{complaint}/reopen', [StudentPortalController::class, 'reopen'])->name('complaints.reopen');

    // Submit Rating
    Route::post('/complaints/{complaint}/rating', [StudentPortalController::class, 'submitRating'])->name('complaints.rating');

    // Notification Routes
    Route::post('/notifications/{notification}/read', [StudentDashboardController::class, 'markNotificationAsRead'])->name('notifications.read');
    Route::post('/notifications/mark-all-read', [StudentDashboardController::class, 'markAllNotificationsAsRead'])->name('notifications.mark-all-read');

    // Alternative complaint routes (for guide compatibility)
    Route::get('/complaints/{complaint}/download/{attachment}', [ComplaintController::class, 'downloadAttachment'])
        ->name('complaints.download');
});

// Alternative dashboard routes (for guide compatibility)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', function() {
        $user = \Illuminate\Support\Facades\Auth::user();
        return redirect($user->getDashboardRoute());
    })->name('dashboard.redirect');
});

// Teacher Portal Routes
Route::prefix('teacher')->name('teacher.')->middleware(['auth', 'verified', 'teacher'])->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\TeacherPortalController::class, 'dashboard'])->name('dashboard');

    // Complaint Management
    Route::get('/complaints/{complaint}', [\App\Http\Controllers\TeacherPortalController::class, 'show'])->name('complaints.show');
    Route::patch('/complaints/{complaint}/status', [\App\Http\Controllers\TeacherPortalController::class, 'updateStatus'])->name('complaints.update-status');
    Route::post('/complaints/{complaint}/comments', [\App\Http\Controllers\TeacherPortalController::class, 'addComment'])->name('complaints.comments.store');

    // Bulk Operations
    Route::post('/complaints/bulk-update', [\App\Http\Controllers\TeacherPortalController::class, 'bulkUpdate'])->name('complaints.bulk-update');

    // AJAX Routes
    Route::get('/api/complaints', [\App\Http\Controllers\TeacherPortalController::class, 'getAssignedComplaints'])->name('api.complaints');
});

// Report Download Route
Route::get('/reports/{report}/download', function (\App\Models\Report $report) {
    if (!$report->file_path || !file_exists(storage_path('app/' . $report->file_path))) {
        abort(404);
    }

    return response()->download(storage_path('app/' . $report->file_path));
})->middleware(['auth'])->name('reports.download');
