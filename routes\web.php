<?php

use App\Http\Controllers\StudentPortalController;
use App\Http\Controllers\StudentDashboardController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect('/admin');
});

// Authentication Routes
require __DIR__.'/auth.php';

// Student Portal Routes
Route::prefix('student')->name('student.')->middleware(['auth', 'verified', 'ensure.student'])->group(function () {
    Route::get('/dashboard', [StudentDashboardController::class, 'index'])->name('dashboard');
    Route::post('/dashboard/quick-submit', [StudentDashboardController::class, 'quickSubmit'])->name('dashboard.quick-submit');

    // Complaint Routes
    Route::get('/complaints', [StudentPortalController::class, 'index'])->name('complaints');
    Route::get('/complaints/create', [StudentPortalController::class, 'create'])->name('complaints.create');
    Route::post('/complaints', [StudentPortalController::class, 'store'])->name('complaints.store');
    Route::get('/complaints/{complaint}', [StudentPortalController::class, 'show'])->name('complaints.show');

    // Comment Routes
    Route::post('/complaints/{complaint}/comments', [StudentPortalController::class, 'addComment'])->name('complaints.comments.store');

    // Reopen Complaint
    Route::post('/complaints/{complaint}/reopen', [StudentPortalController::class, 'reopen'])->name('complaints.reopen');

    // Submit Rating
    Route::post('/complaints/{complaint}/rating', [StudentPortalController::class, 'submitRating'])->name('complaints.rating');

    // Notification Routes
    Route::post('/notifications/{notification}/read', [StudentDashboardController::class, 'markNotificationAsRead'])->name('notifications.read');
    Route::post('/notifications/mark-all-read', [StudentDashboardController::class, 'markAllNotificationsAsRead'])->name('notifications.mark-all-read');
});

// Report Download Route
Route::get('/reports/{report}/download', function (\App\Models\Report $report) {
    if (!$report->file_path || !file_exists(storage_path('app/' . $report->file_path))) {
        abort(404);
    }

    return response()->download(storage_path('app/' . $report->file_path));
})->middleware(['auth'])->name('reports.download');
