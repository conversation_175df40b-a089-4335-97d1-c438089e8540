<?php

namespace App\Http\Controllers;

use App\Models\Complaint;
use App\Models\ComplaintCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Student dashboard
     */
    public function student()
    {
        $user = Auth::user();
        
        // Get student's complaints with statistics
        $complaints = Complaint::where('submitted_by', $user->id)
            ->with(['category', 'assignee'])
            ->latest()
            ->take(5)
            ->get();

        $stats = [
            'total' => Complaint::where('submitted_by', $user->id)->count(),
            'pending' => Complaint::where('submitted_by', $user->id)
                ->whereIn('status', ['new', 'assigned', 'in_progress'])
                ->count(),
            'resolved' => Complaint::where('submitted_by', $user->id)
                ->whereIn('status', ['resolved', 'closed'])
                ->count(),
            'overdue' => Complaint::where('submitted_by', $user->id)
                ->where('due_date', '<', now())
                ->whereNotIn('status', ['resolved', 'closed'])
                ->count(),
        ];

        return view('student.dashboard', compact('complaints', 'stats'));
    }

    /**
     * Teacher dashboard
     */
    public function teacher()
    {
        $user = Auth::user();
        
        // Get assigned complaints
        $assignedComplaints = Complaint::where('assigned_to', $user->id)
            ->with(['category', 'submitter'])
            ->latest()
            ->take(10)
            ->get();

        // Get unassigned complaints (for teachers to pick up)
        $unassignedComplaints = Complaint::whereNull('assigned_to')
            ->with(['category', 'submitter'])
            ->latest()
            ->take(5)
            ->get();

        $stats = [
            'assigned' => Complaint::where('assigned_to', $user->id)
                ->whereNotIn('status', ['resolved', 'closed'])
                ->count(),
            'resolved_today' => Complaint::where('assigned_to', $user->id)
                ->whereDate('resolved_at', today())
                ->count(),
            'overdue' => Complaint::where('assigned_to', $user->id)
                ->where('due_date', '<', now())
                ->whereNotIn('status', ['resolved', 'closed'])
                ->count(),
            'unassigned' => Complaint::whereNull('assigned_to')->count(),
        ];

        return view('teacher.dashboard', compact('assignedComplaints', 'unassignedComplaints', 'stats'));
    }

    /**
     * Admin dashboard (handled by Filament)
     */
    public function admin()
    {
        return redirect('/admin');
    }
}
