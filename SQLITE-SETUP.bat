@echo off
echo ========================================
echo SQLITE DATABASE SETUP
echo ========================================
echo Converting system to use SQLite database
echo This will provide better performance and easier setup
echo.

echo Step 1: Converting to SQLite configuration...
php convert-to-sqlite.php

echo.
echo Step 2: Fixing migrations for SQLite compatibility...
php fix-sqlite-migrations.php

echo.
echo Step 3: Generating application key...
php artisan key:generate --force

echo.
echo Step 4: Clearing all caches...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo.
echo Step 5: Running fresh migrations...
php artisan migrate:fresh --force

echo.
echo Step 6: Seeding database with initial data...
php artisan db:seed --force

echo.
echo Step 7: Creating demo users...
php auto-create-users.php

echo.
echo Step 8: Setting up storage links...
php artisan storage:link

echo.
echo Step 9: Optimizing for SQLite performance...
php artisan config:cache

echo.
echo ========================================
echo SQLITE SETUP COMPLETE!
echo ========================================
echo.
echo Database Type: SQLite
echo Database File: database/database.sqlite
echo Performance: Optimized with WAL mode
echo.
echo Access URLs:
echo • Admin Panel: http://localhost:8000/admin
echo • Teacher Portal: http://localhost:8000/teacher/dashboard
echo • Student Portal: http://localhost:8000/student/dashboard
echo.
echo Demo Credentials:
echo • Admin: <EMAIL> / 123456
echo • Teacher: <EMAIL> / 123456
echo • Student: <EMAIL> / 123456
echo.
echo SQLite Benefits:
echo ✅ No database server required
echo ✅ Faster for development and small deployments
echo ✅ Zero configuration
echo ✅ ACID compliant
echo ✅ Full SQL support
echo ✅ Portable database file
echo.
echo Starting development server...
php artisan serve
