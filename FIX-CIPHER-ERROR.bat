@echo off
echo ========================================
echo FIXING CIPHER ERROR
echo ========================================

echo Step 1: Backing up current .env...
if exist .env copy .env .env.backup

echo Step 2: Creating fresh .env with correct cipher...
(
echo APP_NAME="Complaints Management System"
echo APP_ENV=local
echo APP_KEY=
echo APP_DEBUG=true
echo APP_URL=http://localhost
echo.
echo LOG_CHANNEL=stack
echo LOG_LEVEL=debug
echo.
echo DB_CONNECTION=sqlite
echo.
echo BROADCAST_DRIVER=log
echo CACHE_DRIVER=file
echo FILESYSTEM_DISK=local
echo QUEUE_CONNECTION=sync
echo SESSION_DRIVER=file
echo SESSION_LIFETIME=120
echo.
echo MAIL_MAILER=log
echo MAIL_FROM_ADDRESS="<EMAIL>"
echo MAIL_FROM_NAME="${APP_NAME}"
) > .env

echo Step 3: Clearing all caches...
php artisan config:clear 2>nul
php artisan cache:clear 2>nul
php artisan route:clear 2>nul
php artisan view:clear 2>nul

echo Step 4: Removing cache files...
if exist bootstrap\cache\config.php del bootstrap\cache\config.php
if exist bootstrap\cache\routes-v7.php del bootstrap\cache\routes-v7.php
if exist bootstrap\cache\services.php del bootstrap\cache\services.php

echo Step 5: Generating proper Laravel encryption key...
php artisan key:generate --force

echo Step 6: Verifying the key was generated...
php artisan config:show app.key

echo Step 7: Creating database...
if exist database\database.sqlite del database\database.sqlite
echo. > database\database.sqlite

echo Step 8: Creating required directories...
if not exist storage\framework\cache\data mkdir storage\framework\cache\data
if not exist storage\framework\sessions mkdir storage\framework\sessions
if not exist storage\framework\views mkdir storage\framework\views

echo Step 9: Running migrations...
php artisan migrate:fresh --force

echo Step 10: Seeding database...
php artisan db:seed --force

echo Step 11: Final cache clear...
php artisan config:clear

echo.
echo ========================================
echo CIPHER ERROR FIXED!
echo ========================================
echo.
echo The encryption key has been properly generated.
echo The system should now work without cipher errors.
echo.
echo Access: http://localhost:8000/admin
echo Login: <EMAIL> / 123456
echo.
echo Starting server...
php artisan serve
