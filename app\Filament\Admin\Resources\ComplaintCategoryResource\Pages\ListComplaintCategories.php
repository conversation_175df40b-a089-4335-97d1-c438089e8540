<?php

namespace App\Filament\Admin\Resources\ComplaintCategoryResource\Pages;

use App\Filament\Admin\Resources\ComplaintCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListComplaintCategories extends ListRecords
{
    protected static string $resource = ComplaintCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
