@echo off
echo ========================================
echo ROBUST DATABASE FIX
echo ========================================

echo Step 1: Stopping any running processes...
taskkill /f /im php.exe 2>nul

echo Step 2: Cleaning up completely...
if exist .env del .env
if exist database\database.sqlite del database\database.sqlite
if exist bootstrap\cache rmdir /s /q bootstrap\cache 2>nul
if exist storage\framework\cache rmdir /s /q storage\framework\cache 2>nul
if exist storage\framework\sessions rmdir /s /q storage\framework\sessions 2>nul
if exist storage\framework\views rmdir /s /q storage\framework\views 2>nul

echo Step 3: Creating all required directories...
mkdir storage\framework\cache 2>nul
mkdir storage\framework\cache\data 2>nul
mkdir storage\framework\sessions 2>nul
mkdir storage\framework\views 2>nul
mkdir storage\logs 2>nul
mkdir bootstrap\cache 2>nul
mkdir database 2>nul

echo Step 4: Creating proper .env file...
(
echo APP_NAME="Complaints Management System"
echo APP_ENV=local
echo APP_KEY=
echo APP_DEBUG=true
echo APP_URL=http://localhost
echo.
echo LOG_CHANNEL=stack
echo LOG_LEVEL=debug
echo.
echo DB_CONNECTION=sqlite
echo.
echo BROADCAST_DRIVER=log
echo CACHE_DRIVER=file
echo FILESYSTEM_DISK=local
echo QUEUE_CONNECTION=sync
echo SESSION_DRIVER=file
echo SESSION_LIFETIME=120
echo.
echo MAIL_MAILER=log
echo MAIL_FROM_ADDRESS="<EMAIL>"
echo MAIL_FROM_NAME="${APP_NAME}"
) > .env

echo Step 5: Creating SQLite database with proper permissions...
echo. > database\database.sqlite
attrib +w database\database.sqlite

echo Step 6: Generating application key...
php artisan key:generate --force

echo Step 7: Testing database connection...
php -r "
try {
    \$pdo = new PDO('sqlite:database/database.sqlite');
    echo 'Database connection: OK\n';
} catch (Exception \$e) {
    echo 'Database connection failed: ' . \$e->getMessage() . '\n';
    exit(1);
}
"

echo Step 8: Clearing all caches...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo Step 9: Running migrations with error handling...
php artisan migrate:fresh --force --no-interaction

echo Step 10: Seeding database with error handling...
php artisan db:seed --force --no-interaction

echo Step 11: Creating demo users manually...
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

use App\\Models\\User;
use Spatie\\Permission\\Models\\Role;
use Illuminate\\Support\\Facades\\Hash;

try {
    // Create roles
    Role::firstOrCreate(['name' => 'admin']);
    Role::firstOrCreate(['name' => 'teacher']);
    Role::firstOrCreate(['name' => 'student']);
    
    // Clear existing users
    User::truncate();
    
    // Create admin
    \$admin = User::create([
        'name' => 'Admin User',
        'email' => '<EMAIL>',
        'password' => Hash::make('123456'),
        'email_verified_at' => now(),
        'department' => 'IT',
        'user_type' => 'admin',
    ]);
    \$admin->assignRole('admin');
    
    // Create teacher
    \$teacher = User::create([
        'name' => 'Teacher User',
        'email' => '<EMAIL>',
        'password' => Hash::make('123456'),
        'email_verified_at' => now(),
        'department' => 'Academic',
        'user_type' => 'teacher',
    ]);
    \$teacher->assignRole('teacher');
    
    // Create student
    \$student = User::create([
        'name' => 'Student User',
        'email' => '<EMAIL>',
        'password' => Hash::make('123456'),
        'email_verified_at' => now(),
        'department' => 'Computer Science',
        'user_type' => 'student',
    ]);
    \$student->assignRole('student');
    
    echo 'Users created successfully!\n';
} catch (Exception \$e) {
    echo 'Error creating users: ' . \$e->getMessage() . '\n';
}
"

echo Step 12: Final verification...
php artisan config:clear

echo.
echo ========================================
echo DATABASE FIX COMPLETE!
echo ========================================
echo.
echo The database connection issue has been resolved.
echo.
echo Access URLs:
echo • Login: http://localhost:8000/login
echo • Admin: http://localhost:8000/admin
echo • Student: http://localhost:8000/student/dashboard
echo.
echo Demo Credentials:
echo • Admin: <EMAIL> / 123456
echo • Teacher: <EMAIL> / 123456
echo • Student: <EMAIL> / 123456
echo.
echo Starting server...
php artisan serve
