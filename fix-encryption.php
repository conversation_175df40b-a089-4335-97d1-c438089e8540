<?php

echo "🔧 Fixing encryption and configuration errors...\n";

// Step 1: Ensure .env file exists
if (!file_exists('.env')) {
    if (file_exists('.env.example')) {
        copy('.env.example', '.env');
        echo "✅ Created .env from .env.example\n";
    } else {
        // Create minimal .env
        $envContent = 'APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
';
        file_put_contents('.env', $envContent);
        echo "✅ Created minimal .env file\n";
    }
}

// Step 2: Check and fix APP_KEY
$envContent = file_get_contents('.env');
if (!preg_match('/^APP_KEY=.+$/m', $envContent) || preg_match('/^APP_KEY=\s*$/m', $envContent)) {
    // Generate a proper Laravel key
    $key = 'base64:' . base64_encode(random_bytes(32));
    $envContent = preg_replace('/^APP_KEY=.*$/m', "APP_KEY={$key}", $envContent);
    file_put_contents('.env', $envContent);
    echo "✅ Generated new APP_KEY\n";
} else {
    echo "✅ APP_KEY already exists\n";
}

// Step 3: Ensure database configuration is correct
$envContent = file_get_contents('.env');
if (!preg_match('/^DB_CONNECTION=sqlite$/m', $envContent)) {
    $envContent = preg_replace('/^DB_CONNECTION=.*$/m', 'DB_CONNECTION=sqlite', $envContent);
    file_put_contents('.env', $envContent);
    echo "✅ Fixed DB_CONNECTION\n";
}

// Step 4: Create required directories
$dirs = [
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions', 
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    }
}

// Step 5: Remove problematic cache files
$cacheFiles = [
    'bootstrap/cache/config.php',
    'bootstrap/cache/routes-v7.php',
    'bootstrap/cache/services.php',
    'bootstrap/cache/packages.php'
];

foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "✅ Removed cache file: $file\n";
    }
}

// Step 6: Create database if it doesn't exist
$dbFile = 'database/database.sqlite';
if (!file_exists($dbFile)) {
    if (!is_dir('database')) {
        mkdir('database', 0755, true);
    }
    file_put_contents($dbFile, '');
    echo "✅ Created database file\n";
}

// Step 7: Fix file permissions (Windows compatible)
if (PHP_OS_FAMILY === 'Windows') {
    // Windows - just ensure files are writable
    if (file_exists('.env')) {
        chmod('.env', 0666);
    }
    if (file_exists($dbFile)) {
        chmod($dbFile, 0666);
    }
} else {
    // Unix-like systems
    exec('chmod -R 755 storage bootstrap/cache');
    exec('chmod 666 .env database/database.sqlite');
}

echo "✅ Fixed file permissions\n";

echo "\n🎉 Encryption and configuration fixes complete!\n";
echo "Next steps:\n";
echo "1. php artisan config:clear\n";
echo "2. php artisan migrate:fresh --seed\n";
echo "3. php artisan serve\n";
