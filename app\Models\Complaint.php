<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Complaint extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reference_number',
        'title',
        'description',
        'category_id',
        'submitted_by',
        'assigned_to',
        'status',
        'priority',
        'due_date',
        'resolution',
        'resolution_date',
        'satisfaction_rating',
        'satisfaction_comment',
        'attachments',
        'is_anonymous',
        'acknowledged_at',
        'resolved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'due_date' => 'datetime',
        'resolution_date' => 'datetime',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
        'attachments' => 'array',
        'is_anonymous' => 'boolean',
    ];

    /**
     * The possible statuses for a complaint.
     */
    public const STATUSES = [
        'new' => 'New',
        'assigned' => 'Assigned',
        'in_progress' => 'In Progress',
        'on_hold' => 'On Hold',
        'resolved' => 'Resolved',
        'closed' => 'Closed',
        'reopened' => 'Reopened',
    ];

    /**
     * The possible priorities for a complaint.
     */
    public const PRIORITIES = [
        'low' => 'Low',
        'medium' => 'Medium',
        'high' => 'High',
        'critical' => 'Critical',
    ];

    /**
     * Get the category that the complaint belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ComplaintCategory::class, 'category_id');
    }

    /**
     * Get the user who submitted the complaint.
     */
    public function submitter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_by');
    }

    /**
     * Get the user who is assigned to the complaint.
     */
    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the attachments for the complaint.
     */
    public function attachments(): HasMany
    {
        return $this->hasMany(ComplaintAttachment::class);
    }

    /**
     * Get the comments for the complaint.
     */
    public function comments(): HasMany
    {
        return $this->hasMany(ComplaintComment::class);
    }

    /**
     * Get the status history for the complaint.
     */
    public function statusHistory(): HasMany
    {
        return $this->hasMany(ComplaintStatusHistory::class);
    }

    /**
     * Get the escalations for the complaint.
     */
    public function escalations(): HasMany
    {
        return $this->hasMany(ComplaintEscalation::class);
    }

    /**
     * Get the assignment history for the complaint.
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(ComplaintAssignment::class);
    }

    // Query Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeForUser($query, $userId, $role)
    {
        if ($role === 'student') {
            return $query->where('submitted_by', $userId);
        } elseif ($role === 'teacher') {
            return $query->where('assigned_to', $userId);
        }
        return $query; // Admin sees all
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereNotIn('status', ['resolved', 'closed']);
    }

    // Business logic methods
    public function acknowledge($userId = null)
    {
        $this->update([
            'status' => 'assigned',
            'acknowledged_at' => now(),
            'assigned_to' => $userId ?? auth()->id(),
        ]);
    }

    public function resolve($notes = null)
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolution' => $notes,
        ]);
    }

    /**
     * Generate a unique reference number for a new complaint.
     */
    public static function generateReferenceNumber(): string
    {
        $prefix = 'CMP';
        $year = date('Y');
        $month = date('m');

        // Get the count of complaints for this month
        $count = self::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->count() + 1;

        return $prefix . '-' . $year . $month . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Status transition rules
     */
    public const STATUS_TRANSITIONS = [
        'new' => ['assigned', 'in_progress', 'on_hold', 'closed'],
        'assigned' => ['in_progress', 'on_hold', 'resolved', 'closed'],
        'in_progress' => ['on_hold', 'resolved', 'closed', 'assigned'],
        'on_hold' => ['in_progress', 'assigned', 'closed'],
        'resolved' => ['closed', 'reopened'],
        'closed' => ['reopened'],
        'reopened' => ['assigned', 'in_progress', 'on_hold', 'resolved', 'closed'],
    ];

    /**
     * Check if status transition is valid
     */
    public function canTransitionTo(string $newStatus): bool
    {
        $allowedTransitions = self::STATUS_TRANSITIONS[$this->status] ?? [];
        return in_array($newStatus, $allowedTransitions);
    }

    /**
     * Transition to a new status with validation
     */
    public function transitionTo(string $newStatus, ?int $changedBy = null, ?string $notes = null): bool
    {
        if (!$this->canTransitionTo($newStatus)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = $newStatus;

        // Set resolution date for resolved/closed status
        if (in_array($newStatus, ['resolved', 'closed']) && !$this->resolution_date) {
            $this->resolution_date = now();
        }

        $this->save();

        // Record status history
        $this->statusHistory()->create([
            'status' => $newStatus,
            'changed_by' => $changedBy ?? auth()->id(),
            'notes' => $notes ?? "Status changed from {$oldStatus} to {$newStatus}",
        ]);

        // Send notifications
        $this->sendStatusChangeNotifications($oldStatus, $newStatus);

        return true;
    }

    /**
     * Send notifications for status changes
     */
    protected function sendStatusChangeNotifications(string $oldStatus, string $newStatus): void
    {
        // Notify submitter
        if ($this->submitter) {
            $this->submitter->notify(new \App\Notifications\ComplaintStatusChanged($this, $oldStatus, $newStatus));
        }

        // Notify assignee if different from submitter
        if ($this->assignee && $this->assignee->id !== $this->submitter->id) {
            $this->assignee->notify(new \App\Notifications\ComplaintStatusChanged($this, $oldStatus, $newStatus));
        }
    }

    /**
     * Calculate SLA due date based on category and priority
     */
    public function calculateSLADueDate(): \Carbon\Carbon
    {
        $baseDays = $this->category->sla_days ?? 7;

        // Adjust based on priority
        $priorityMultiplier = match($this->priority) {
            'critical' => 0.25,  // 25% of base time
            'high' => 0.5,       // 50% of base time
            'medium' => 1.0,     // 100% of base time
            'low' => 1.5,        // 150% of base time
            default => 1.0,
        };

        $adjustedDays = $baseDays * $priorityMultiplier;

        return $this->created_at->addDays(ceil($adjustedDays));
    }

    /**
     * Check if complaint is overdue
     */
    public function isOverdue(): bool
    {
        if (in_array($this->status, ['resolved', 'closed'])) {
            return false;
        }

        return $this->due_date && $this->due_date < now();
    }

    /**
     * Get days until due date
     */
    public function getDaysUntilDue(): ?int
    {
        if (!$this->due_date || in_array($this->status, ['resolved', 'closed'])) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Auto-assign complaint based on category and workload
     */
    public function autoAssign(): bool
    {
        // Get eligible assignees (admin and teacher roles)
        $eligibleUsers = User::role(['admin', 'teacher'])->get();

        if ($eligibleUsers->isEmpty()) {
            return false;
        }

        // Simple round-robin assignment based on current workload
        $assignee = $eligibleUsers->sortBy(function ($user) {
            return $user->assignedComplaints()->whereNotIn('status', ['resolved', 'closed'])->count();
        })->first();

        if ($assignee) {
            $this->assigned_to = $assignee->id;
            $this->save();

            // Send assignment notification
            $assignee->notify(new \App\Notifications\ComplaintAssigned($this));

            return true;
        }

        return false;
    }

    /**
     * Escalate complaint to next level
     */
    public function escalate(int $escalatedTo, string $reason, ?int $escalatedBy = null, ?int $level = null): ComplaintEscalation
    {
        // Determine escalation level
        $currentLevel = $this->escalations()->max('escalation_level') ?? 0;
        $escalationLevel = $level ?? ($currentLevel + 1);

        // Create escalation record
        $escalation = $this->escalations()->create([
            'escalated_by' => $escalatedBy ?? auth()->id(),
            'escalated_to' => $escalatedTo,
            'escalation_level' => $escalationLevel,
            'reason' => $reason,
            'escalated_at' => now(),
        ]);

        // Update complaint assignment
        $this->update(['assigned_to' => $escalatedTo]);

        // Record status change
        $this->statusHistory()->create([
            'status' => $this->status,
            'changed_by' => $escalatedBy ?? auth()->id(),
            'notes' => "Complaint escalated to level {$escalationLevel}: {$reason}",
        ]);

        // Send escalation notifications
        $this->sendEscalationNotifications($escalation);

        return $escalation;
    }

    /**
     * Send escalation notifications
     */
    protected function sendEscalationNotifications(ComplaintEscalation $escalation): void
    {
        // Notify the person to whom it's escalated
        if ($escalation->escalatedTo) {
            $escalation->escalatedTo->notify(new \App\Notifications\ComplaintEscalated($this, $escalation));
        }

        // Notify the submitter
        if ($this->submitter) {
            $this->submitter->notify(new \App\Notifications\ComplaintEscalated($this, $escalation));
        }
    }

    /**
     * Check if complaint should be auto-escalated
     */
    public function shouldAutoEscalate(): bool
    {
        if (in_array($this->status, ['resolved', 'closed'])) {
            return false;
        }

        // Check if overdue by more than escalation threshold
        $escalationThreshold = $this->category->sla_days ?? 7;
        $daysPastDue = $this->getDaysUntilDue();

        return $daysPastDue !== null && $daysPastDue < -($escalationThreshold * 0.5);
    }

    /**
     * Get current escalation level
     */
    public function getCurrentEscalationLevel(): int
    {
        return $this->escalations()->max('escalation_level') ?? 0;
    }

    /**
     * Check if complaint is escalated
     */
    public function isEscalated(): bool
    {
        return $this->escalations()->exists();
    }

    /**
     * Get latest escalation
     */
    public function getLatestEscalation(): ?ComplaintEscalation
    {
        return $this->escalations()->latest('escalated_at')->first();
    }

    /**
     * Assign complaint to a user
     */
    public function assignTo(int $userId, ?string $reason = null, ?int $assignedBy = null): ComplaintAssignment
    {
        $previousAssignee = $this->assigned_to;

        // Update complaint assignment
        $this->update(['assigned_to' => $userId]);

        // Create assignment record
        $assignment = $this->assignments()->create([
            'assigned_from' => $previousAssignee,
            'assigned_to' => $userId,
            'assigned_by' => $assignedBy ?? auth()->id(),
            'assignment_reason' => $reason ?? 'Manual assignment',
            'assigned_at' => now(),
        ]);

        // Update status if new
        if ($this->status === 'new') {
            $this->transitionTo('assigned', $assignedBy);
        }

        // Record status change
        $this->statusHistory()->create([
            'status' => $this->status,
            'changed_by' => $assignedBy ?? auth()->id(),
            'notes' => $reason ?? 'Complaint assigned to staff member',
        ]);

        // Send assignment notification
        $assignee = User::find($userId);
        if ($assignee) {
            $assignee->notify(new \App\Notifications\ComplaintAssigned($this));
        }

        return $assignment;
    }

    /**
     * Unassign complaint
     */
    public function unassign(?string $reason = null, ?int $unassignedBy = null): void
    {
        // Mark current assignment as inactive
        $currentAssignment = $this->assignments()->whereNull('unassigned_at')->latest()->first();
        if ($currentAssignment) {
            $currentAssignment->unassign($reason);
        }

        // Update complaint
        $this->update(['assigned_to' => null]);

        // Record status change
        $this->statusHistory()->create([
            'status' => $this->status,
            'changed_by' => $unassignedBy ?? auth()->id(),
            'notes' => $reason ?? 'Complaint unassigned',
        ]);
    }

    /**
     * Get current assignment
     */
    public function getCurrentAssignment(): ?ComplaintAssignment
    {
        return $this->assignments()->whereNull('unassigned_at')->latest()->first();
    }

    /**
     * Get assignment history
     */
    public function getAssignmentHistory()
    {
        return $this->assignments()->with(['assignedFrom', 'assignedTo', 'assignedBy'])->latest('assigned_at');
    }

    /**
     * Check if complaint is assigned
     */
    public function isAssigned(): bool
    {
        return !is_null($this->assigned_to);
    }

    /**
     * Get workload for a user (number of active assignments)
     */
    public static function getWorkloadForUser(int $userId): int
    {
        return self::where('assigned_to', $userId)
            ->whereNotIn('status', ['resolved', 'closed'])
            ->count();
    }

    /**
     * Get users with lowest workload for auto-assignment
     */
    public static function getUsersWithLowestWorkload(array $roles = ['admin', 'teacher'], int $limit = 5)
    {
        return User::role($roles)
            ->get()
            ->map(function ($user) {
                $user->workload = self::getWorkloadForUser($user->id);
                return $user;
            })
            ->sortBy('workload')
            ->take($limit);
    }
}
