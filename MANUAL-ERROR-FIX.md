# Manual Error Fix Guide

The errors you're seeing are related to encryption/application key issues. Follow these steps to fix them:

## Quick Fix (Run ONE of these)

### Option 1: Emergency Fix Script
```bash
EMERGENCY-FIX.bat
```

### Option 2: Quick Error Fix
```bash
QUICK-ERROR-FIX.bat
```

## Manual Steps (if scripts don't work)

### Step 1: Clear All Caches
```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

### Step 2: Delete Cache Files Manually
```bash
del bootstrap\cache\config.php
del bootstrap\cache\routes-v7.php
del bootstrap\cache\services.php
del bootstrap\cache\packages.php
```

### Step 3: Fix Environment File
```bash
copy .env.example .env
```

### Step 4: Generate New Application Key
```bash
php artisan key:generate --force
```

### Step 5: Create Required Directories
```bash
mkdir storage\framework\cache\data
mkdir storage\framework\sessions
mkdir storage\framework\views
mkdir bootstrap\cache
```

### Step 6: Create Database
```bash
echo. > database\database.sqlite
```

### Step 7: Run Migrations
```bash
php artisan migrate:fresh --force
```

### Step 8: Seed Database
```bash
php artisan db:seed --force
```

### Step 9: Create Users
```bash
php auto-create-users.php
```

### Step 10: Start Server
```bash
php artisan serve
```

## If You Still Get Errors

### Check PHP Extensions
Make sure these PHP extensions are enabled:
- openssl
- sqlite3
- pdo_sqlite
- mbstring
- fileinfo

### Check File Permissions
```bash
# Windows (run as administrator)
icacls storage /grant Everyone:F /T
icacls bootstrap\cache /grant Everyone:F /T
icacls database /grant Everyone:F /T

# Or try
attrib -r storage\* /s /d
attrib -r bootstrap\cache\* /s /d
```

### Verify .env File
Make sure your .env file contains:
```env
APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=base64:SOME_LONG_KEY_HERE
APP_DEBUG=true
APP_URL=http://localhost

DB_CONNECTION=sqlite

SESSION_DRIVER=file
CACHE_DRIVER=file
```

### Nuclear Option (Complete Reset)
If nothing works, delete everything and start fresh:
```bash
# Delete problematic files
del .env
del database\database.sqlite
rmdir /s bootstrap\cache
rmdir /s storage\framework\cache
rmdir /s storage\framework\sessions
rmdir /s storage\framework\views

# Run emergency fix
EMERGENCY-FIX.bat
```

## Expected Result

After fixing, you should be able to access:
- **Admin Panel**: http://localhost:8000/admin
- **Student Portal**: http://localhost:8000/student/dashboard

**Login with**: <EMAIL> / 123456

## Common Error Messages and Solutions

### "The payload is invalid"
- **Solution**: Run `php artisan key:generate --force`

### "Class not found"
- **Solution**: Run `composer dump-autoload`

### "Database file not found"
- **Solution**: Create database file with `echo. > database\database.sqlite`

### "Permission denied"
- **Solution**: Fix file permissions or run as administrator

### "Configuration cache"
- **Solution**: Delete all cache files and run `php artisan config:clear`

## Verification Commands

After fixing, verify everything works:
```bash
# Check configuration
php artisan config:show app.key

# Check database connection
php artisan tinker
>>> DB::connection()->getPdo();
>>> exit

# Check routes
php artisan route:list

# Check if users exist
php artisan tinker
>>> App\Models\User::count();
>>> exit
```

If all commands work without errors, the system is fixed!
