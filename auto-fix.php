<?php

echo "🔧 Auto-fixing all issues...\n";

// Create required directories
$dirs = [
    'storage/framework/cache',
    'storage/framework/cache/data', 
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    }
}

// Create .env file
$envContent = 'APP_NAME="Complaints Management System"
APP_ENV=local
APP_KEY=base64:SGVsbG9Xb3JsZEhlbGxvV29ybGRIZWxsb1dvcmxkSGVsbG9Xb3JsZA==
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
';

file_put_contents('.env', $envContent);
echo "✅ Created .env file\n";

// Create SQLite database
if (!file_exists('database/database.sqlite')) {
    file_put_contents('database/database.sqlite', '');
    echo "✅ Created database file\n";
}

// Clear any existing cache
$cacheFiles = glob('bootstrap/cache/*.php');
foreach ($cacheFiles as $file) {
    if (is_file($file)) {
        unlink($file);
    }
}
echo "✅ Cleared cache files\n";

echo "\n🎉 Auto-fix complete!\n";
echo "Now run: php artisan migrate:fresh --force\n";
echo "Then run: php auto-create-users.php\n";
echo "Finally run: php artisan serve\n";
