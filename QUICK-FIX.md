# 🚀 QUICK FIX - Copy and Paste Solution

## Option 1: Run the Complete Fix Script

**Windows:**
```bash
COMPLETE-FIX.bat
```

**Linux/Mac:**
```bash
chmod +x COMPLETE-FIX.sh
./COMPLETE-FIX.sh
```

## Option 2: One-Command Fix (Copy & Paste)

Copy and paste this entire command block into your terminal:

**Windows (Command Prompt):**
```cmd
copy .env.example .env && echo. > database\database.sqlite && php artisan key:generate --force && php artisan config:clear && php artisan cache:clear && php artisan migrate:fresh --force && php artisan db:seed --force && php artisan storage:link && echo Setup complete! && echo. && echo Demo Credentials: && echo Admin: <EMAIL> / password && echo Teacher: <EMAIL> / password && echo Student: <EMAIL> / password && echo. && echo Starting server... && php artisan serve
```

**Linux/Mac (Terminal):**
```bash
cp .env.example .env && touch database/database.sqlite && php artisan key:generate --force && php artisan config:clear && php artisan cache:clear && php artisan migrate:fresh --force && php artisan db:seed --force && php artisan storage:link && echo "Setup complete!" && echo "" && echo "Demo Credentials:" && echo "Admin: <EMAIL> / password" && echo "Teacher: <EMAIL> / password" && echo "Student: <EMAIL> / password" && echo "" && echo "Starting server..." && php artisan serve
```

## Option 3: Step by Step (if above doesn't work)

Run these commands one by one:

```bash
# 1. Setup environment
cp .env.example .env

# 2. Create database (Windows)
echo. > database\database.sqlite

# 2. Create database (Linux/Mac)
touch database/database.sqlite

# 3. Generate key
php artisan key:generate --force

# 4. Clear cache
php artisan config:clear
php artisan cache:clear

# 5. Setup database
php artisan migrate:fresh --force
php artisan db:seed --force

# 6. Create storage link
php artisan storage:link

# 7. Start server
php artisan serve
```

## 🎯 After Running Any Option Above:

1. **Open your browser**
2. **Go to:** http://localhost:8000/admin
3. **Login with:**
   - Email: `<EMAIL>`
   - Password: `password`

## 🔑 All Demo Credentials:

- **Admin Panel:** <EMAIL> / password
- **Teacher Access:** <EMAIL> / password  
- **Student Portal:** <EMAIL> / password

## 📱 Access URLs:

- **Admin/Teacher:** http://localhost:8000/admin
- **Student Portal:** http://localhost:8000/student/dashboard

---

**If you still get errors after this, the issue might be:**
1. PHP version (need PHP 8.2+)
2. Missing PHP extensions
3. File permissions

Run `php --version` to check your PHP version.
