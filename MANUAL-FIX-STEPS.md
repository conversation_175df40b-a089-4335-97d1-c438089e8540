# 🛠️ MANUAL FIX - Step by Step

Follow these steps exactly, one by one:

## Step 1: Create Environment File
Copy and paste this command:
```bash
copy .env.example .env
```

## Step 2: Create Database File
**Windows:**
```bash
echo. > database\database.sqlite
```

**Linux/Mac:**
```bash
touch database/database.sqlite
```

## Step 3: Generate Application Key
```bash
php artisan key:generate
```

## Step 4: Clear Cache
```bash
php artisan config:clear
```

## Step 5: Run Migrations
```bash
php artisan migrate:fresh
```

## Step 6: Create Demo Users
```bash
php artisan db:seed
```

## Step 7: Start Server
```bash
php artisan serve
```

## Step 8: Test Login
1. Open browser: http://localhost:8000/admin
2. Login with:
   - Email: `<EMAIL>`
   - Password: `password`

---

## If Step 6 (db:seed) Fails:

Run this command to create users manually:
```bash
php artisan tinker
```

Then copy and paste this into tinker:
```php
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;

Role::create(['name' => 'admin']);
Role::create(['name' => 'teacher']);
Role::create(['name' => 'student']);

$admin = User::create([
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'password' => Hash::make('password'),
    'email_verified_at' => now(),
    'department' => 'IT',
    'user_type' => 'admin',
]);
$admin->assignRole('admin');

$teacher = User::create([
    'name' => 'Teacher User',
    'email' => '<EMAIL>',
    'password' => Hash::make('password'),
    'email_verified_at' => now(),
    'department' => 'Academic',
    'user_type' => 'teacher',
]);
$teacher->assignRole('teacher');

$student = User::create([
    'name' => 'Student User',
    'email' => '<EMAIL>',
    'password' => Hash::make('password'),
    'email_verified_at' => now(),
    'department' => 'Computer Science',
    'user_type' => 'student',
]);
$student->assignRole('student');

echo "Users created successfully!";
exit
```

---

## If You Get Errors:

### Error: "Class not found"
```bash
composer dump-autoload
```

### Error: "Permission denied"
Make sure you're in the project directory and have write permissions.

### Error: "Database file not found"
Make sure the database file was created in Step 2.

---

## Final Test:
- Admin: http://localhost:8000/admin (<EMAIL> / password)
- Student: http://localhost:8000/student/dashboard (<EMAIL> / password)
