<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ComplaintEscalation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'complaint_id',
        'escalated_by',
        'escalated_to',
        'escalation_level',
        'reason',
        'escalated_at',
        'resolved_at',
        'resolution_notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'escalated_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    /**
     * Escalation levels
     */
    public const ESCALATION_LEVELS = [
        1 => 'Level 1 - Supervisor',
        2 => 'Level 2 - Manager',
        3 => 'Level 3 - Director',
        4 => 'Level 4 - Executive',
    ];

    /**
     * Get the complaint that was escalated
     */
    public function complaint(): BelongsTo
    {
        return $this->belongsTo(Complaint::class);
    }

    /**
     * Get the user who escalated the complaint
     */
    public function escalatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'escalated_by');
    }

    /**
     * Get the user to whom the complaint was escalated
     */
    public function escalatedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'escalated_to');
    }

    /**
     * Check if escalation is resolved
     */
    public function isResolved(): bool
    {
        return !is_null($this->resolved_at);
    }

    /**
     * Resolve the escalation
     */
    public function resolve(string $notes = null): void
    {
        $this->update([
            'resolved_at' => now(),
            'resolution_notes' => $notes,
        ]);
    }

    /**
     * Get escalation duration in hours
     */
    public function getDurationInHours(): ?float
    {
        if (!$this->resolved_at) {
            return $this->escalated_at->diffInHours(now(), true);
        }
        
        return $this->escalated_at->diffInHours($this->resolved_at, true);
    }

    /**
     * Get escalation level name
     */
    public function getLevelName(): string
    {
        return self::ESCALATION_LEVELS[$this->escalation_level] ?? 'Unknown Level';
    }
}
