<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Report extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'parameters',
        'schedule',
        'recipients',
        'created_by',
        'last_generated_at',
        'is_active',
        'file_path',
        'format',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'parameters' => 'array',
        'recipients' => 'array',
        'last_generated_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Report types
     */
    public const TYPES = [
        'complaint_summary' => 'Complaint Summary Report',
        'sla_compliance' => 'SLA Compliance Report',
        'satisfaction_analysis' => 'Satisfaction Analysis Report',
        'staff_performance' => 'Staff Performance Report',
        'category_analysis' => 'Category Analysis Report',
        'trend_analysis' => 'Trend Analysis Report',
        'cost_analysis' => 'Cost Analysis Report',
        'iso_audit' => 'ISO 21001 Audit Report',
        'custom' => 'Custom Report',
    ];

    /**
     * Report formats
     */
    public const FORMATS = [
        'pdf' => 'PDF',
        'excel' => 'Excel',
        'csv' => 'CSV',
    ];

    /**
     * Schedule options
     */
    public const SCHEDULES = [
        'manual' => 'Manual',
        'daily' => 'Daily',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
        'quarterly' => 'Quarterly',
    ];

    /**
     * Get the user who created the report
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the report type name
     */
    public function getTypeNameAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'Unknown';
    }

    /**
     * Get the report format name
     */
    public function getFormatNameAttribute(): string
    {
        return self::FORMATS[$this->format] ?? 'Unknown';
    }

    /**
     * Get the schedule name
     */
    public function getScheduleNameAttribute(): string
    {
        return self::SCHEDULES[$this->schedule] ?? 'Unknown';
    }

    /**
     * Check if report is scheduled
     */
    public function isScheduled(): bool
    {
        return $this->schedule !== 'manual';
    }

    /**
     * Check if report is due for generation
     */
    public function isDue(): bool
    {
        if (!$this->isScheduled() || !$this->is_active) {
            return false;
        }

        $lastGenerated = $this->last_generated_at ?? $this->created_at;
        
        return match ($this->schedule) {
            'daily' => $lastGenerated->isBefore(now()->subDay()),
            'weekly' => $lastGenerated->isBefore(now()->subWeek()),
            'monthly' => $lastGenerated->isBefore(now()->subMonth()),
            'quarterly' => $lastGenerated->isBefore(now()->subMonths(3)),
            default => false,
        };
    }

    /**
     * Get next scheduled generation date
     */
    public function getNextGenerationDate(): ?\Carbon\Carbon
    {
        if (!$this->isScheduled()) {
            return null;
        }

        $lastGenerated = $this->last_generated_at ?? $this->created_at;
        
        return match ($this->schedule) {
            'daily' => $lastGenerated->addDay(),
            'weekly' => $lastGenerated->addWeek(),
            'monthly' => $lastGenerated->addMonth(),
            'quarterly' => $lastGenerated->addMonths(3),
            default => null,
        };
    }

    /**
     * Mark report as generated
     */
    public function markAsGenerated(string $filePath = null): void
    {
        $this->update([
            'last_generated_at' => now(),
            'file_path' => $filePath,
        ]);
    }

    /**
     * Get default parameters for report type
     */
    public static function getDefaultParameters(string $type): array
    {
        return match ($type) {
            'complaint_summary' => [
                'date_range' => '30days',
                'include_categories' => true,
                'include_priorities' => true,
                'include_status_breakdown' => true,
            ],
            'sla_compliance' => [
                'date_range' => '30days',
                'group_by' => 'category',
                'include_trends' => true,
            ],
            'satisfaction_analysis' => [
                'date_range' => '90days',
                'include_comments' => false,
                'group_by' => 'category',
            ],
            'staff_performance' => [
                'date_range' => '30days',
                'include_individual_metrics' => true,
                'include_comparisons' => true,
            ],
            'category_analysis' => [
                'date_range' => '90days',
                'include_subcategories' => true,
                'include_trends' => true,
            ],
            'trend_analysis' => [
                'date_range' => '12months',
                'metrics' => ['volume', 'resolution_time', 'satisfaction'],
            ],
            'cost_analysis' => [
                'date_range' => '12months',
                'include_projections' => true,
                'group_by' => 'priority',
            ],
            'iso_audit' => [
                'date_range' => '12months',
                'include_compliance_metrics' => true,
                'include_improvement_actions' => true,
            ],
            default => [],
        };
    }
}
