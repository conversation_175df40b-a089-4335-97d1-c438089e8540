<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use League\Csv\Reader;
use Spatie\Permission\Models\Role;

class ImportUsers extends Page
{
    protected static string $resource = UserResource::class;

    protected static string $view = 'filament.resources.user-resource.pages.import-users';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('csv_file')
                    ->label('CSV File')
                    ->disk('local')
                    ->directory('csv-imports')
                    ->acceptedFileTypes(['text/csv', 'application/vnd.ms-excel'])
                    ->required(),
                Select::make('default_role')
                    ->label('Default Role')
                    ->options(Role::pluck('name', 'name'))
                    ->required(),
                Toggle::make('send_email_verification')
                    ->label('Send Email Verification')
                    ->default(true),
            ]);
    }

    public function import(): void
    {
        $data = $this->form->getState();
        
        $path = Storage::disk('local')->path($data['csv_file']);
        
        $csv = Reader::createFromPath($path, 'r');
        $csv->setHeaderOffset(0);
        
        $records = $csv->getRecords();
        $importCount = 0;
        $errorCount = 0;
        
        foreach ($records as $record) {
            try {
                // Validate required fields
                if (empty($record['name']) || empty($record['email'])) {
                    $errorCount++;
                    continue;
                }
                
                // Check if user already exists
                $existingUser = User::where('email', $record['email'])->first();
                
                if ($existingUser) {
                    // Update existing user
                    $existingUser->name = $record['name'];
                    
                    if (!empty($record['password'])) {
                        $existingUser->password = Hash::make($record['password']);
                    }
                    
                    $existingUser->save();
                    
                    // Assign role if specified in CSV or use default
                    if (!empty($record['role'])) {
                        $existingUser->assignRole($record['role']);
                    } elseif (!$existingUser->hasRole($data['default_role'])) {
                        $existingUser->assignRole($data['default_role']);
                    }
                } else {
                    // Create new user
                    $user = new User();
                    $user->name = $record['name'];
                    $user->email = $record['email'];
                    $user->password = Hash::make($record['password'] ?? 'password');
                    
                    if ($data['send_email_verification']) {
                        $user->email_verified_at = null; // Will trigger verification email
                    } else {
                        $user->email_verified_at = now();
                    }
                    
                    $user->save();
                    
                    // Assign role
                    $role = $record['role'] ?? $data['default_role'];
                    $user->assignRole($role);
                }
                
                $importCount++;
            } catch (\Exception $e) {
                $errorCount++;
            }
        }
        
        // Clean up the uploaded file
        Storage::disk('local')->delete($data['csv_file']);
        
        // Show notification
        $this->notify('success', "Import completed: {$importCount} users imported, {$errorCount} errors.");
        
        // Redirect back to the users list
        $this->redirect(UserResource::getUrl('index'));
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('import')
                ->label('Import Users')
                ->action('import')
                ->color('success'),
            Action::make('cancel')
                ->label('Cancel')
                ->url(UserResource::getUrl('index'))
                ->color('secondary'),
        ];
    }
}
