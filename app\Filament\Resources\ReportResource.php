<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReportResource\Pages;
use App\Models\Report;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ReportResource extends Resource
{
    protected static ?string $model = Report::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    protected static ?string $navigationGroup = 'Business Intelligence';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Report Configuration')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->rows(3),
                        Forms\Components\Select::make('type')
                            ->options(Report::TYPES)
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('parameters', Report::getDefaultParameters($state));
                            }),
                        Forms\Components\Select::make('format')
                            ->options(Report::FORMATS)
                            ->default('pdf')
                            ->required(),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('Scheduling')
                    ->schema([
                        Forms\Components\Select::make('schedule')
                            ->options(Report::SCHEDULES)
                            ->default('manual')
                            ->required()
                            ->reactive(),
                        Forms\Components\TagsInput::make('recipients')
                            ->label('Email Recipients')
                            ->placeholder('Enter email addresses')
                            ->visible(fn (callable $get) => $get('schedule') !== 'manual'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('Parameters')
                    ->schema([
                        Forms\Components\KeyValue::make('parameters')
                            ->label('Report Parameters')
                            ->keyLabel('Parameter')
                            ->valueLabel('Value')
                            ->addActionLabel('Add Parameter')
                            ->reorderable(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type_name')
                    ->label('Type')
                    ->badge()
                    ->color('primary'),
                Tables\Columns\TextColumn::make('format_name')
                    ->label('Format')
                    ->badge(),
                Tables\Columns\TextColumn::make('schedule_name')
                    ->label('Schedule')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Manual' => 'gray',
                        'Daily' => 'success',
                        'Weekly' => 'info',
                        'Monthly' => 'warning',
                        'Quarterly' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_generated_at')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Never'),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options(Report::TYPES),
                Tables\Filters\SelectFilter::make('schedule')
                    ->options(Report::SCHEDULES),
                Tables\Filters\Filter::make('active')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true))
                    ->label('Active Only')
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\Action::make('generate')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->action(function (Report $record) {
                        // Dispatch report generation job
                        \App\Jobs\GenerateReport::dispatch($record);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Report generation started')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->visible(fn (Report $record) => $record->file_path && file_exists(storage_path('app/' . $record->file_path)))
                    ->url(fn (Report $record) => route('reports.download', $record))
                    ->openUrlInNewTab(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('generate_selected')
                        ->label('Generate Selected')
                        ->icon('heroicon-o-play')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                \App\Jobs\GenerateReport::dispatch($record);
                            }
                            
                            \Filament\Notifications\Notification::make()
                                ->title('Report generation started for selected reports')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReports::route('/'),
            'create' => Pages\CreateReport::route('/create'),
            'edit' => Pages\EditReport::route('/{record}/edit'),
        ];
    }
}
