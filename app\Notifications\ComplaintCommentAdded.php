<?php

namespace App\Notifications;

use App\Models\Complaint;
use App\Models\ComplaintComment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintCommentAdded extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Complaint $complaint,
        public ComplaintComment $comment
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New Comment on Your Complaint')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('A new comment has been added to your complaint.')
            ->line('**Reference Number:** ' . $this->complaint->reference_number)
            ->line('**Title:** ' . $this->complaint->title)
            ->line('**Comment by:** ' . $this->comment->user->name)
            ->line('**Comment:** ' . $this->comment->comment)
            ->action('View Complaint', url('/student/complaints/' . $this->complaint->id))
            ->line('Thank you for using our complaint management system.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'complaint_id' => $this->complaint->id,
            'reference_number' => $this->complaint->reference_number,
            'title' => $this->complaint->title,
            'comment_id' => $this->comment->id,
            'commenter_name' => $this->comment->user->name,
            'message' => 'A new comment has been added to your complaint.',
            'action_url' => url('/student/complaints/' . $this->complaint->id),
        ];
    }
}
