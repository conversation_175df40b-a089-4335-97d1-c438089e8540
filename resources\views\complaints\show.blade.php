<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Complaint Details - {{ $complaint->reference_number }}
            </h2>
            <div class="flex space-x-2">
                @if($complaint->status === 'new' && $complaint->submitted_by === Auth::id())
                    <a href="{{ route('complaints.edit', $complaint) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        Edit Complaint
                    </a>
                @endif
                <a href="{{ route('complaints.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to List
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Complaint Details -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $complaint->title }}</h3>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            @if($complaint->status === 'new') bg-blue-100 text-blue-800
                                            @elseif($complaint->status === 'assigned') bg-yellow-100 text-yellow-800
                                            @elseif($complaint->status === 'in_progress') bg-purple-100 text-purple-800
                                            @elseif($complaint->status === 'resolved') bg-green-100 text-green-800
                                            @elseif($complaint->status === 'closed') bg-gray-100 text-gray-800
                                            @else bg-red-100 text-red-800
                                            @endif">
                                            {{ ucfirst(str_replace('_', ' ', $complaint->status)) }}
                                        </span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            @if($complaint->priority === 'low') bg-green-100 text-green-800
                                            @elseif($complaint->priority === 'medium') bg-yellow-100 text-yellow-800
                                            @elseif($complaint->priority === 'high') bg-orange-100 text-orange-800
                                            @else bg-red-100 text-red-800
                                            @endif">
                                            {{ ucfirst($complaint->priority) }} Priority
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="prose max-w-none">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-700 whitespace-pre-wrap">{{ $complaint->description }}</p>
                            </div>

                            @if($complaint->attachments && count($complaint->attachments) > 0)
                                <div class="mt-6">
                                    <h4 class="text-sm font-medium text-gray-900 mb-3">Attachments</h4>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                        @foreach($complaint->attachments as $index => $attachment)
                                            <div class="flex items-center p-3 border border-gray-200 rounded-lg">
                                                <div class="flex-shrink-0">
                                                    @if(str_contains($attachment['mime_type'], 'image'))
                                                        <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                        </svg>
                                                    @else
                                                        <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                    @endif
                                                </div>
                                                <div class="ml-3 flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $attachment['original_name'] }}</p>
                                                    <p class="text-sm text-gray-500">{{ number_format($attachment['size'] / 1024, 1) }} KB</p>
                                                </div>
                                                <div class="ml-3">
                                                    <a href="{{ route('complaints.download', [$complaint, $index]) }}" 
                                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                        Download
                                                    </a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            @if($complaint->resolution)
                                <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <h4 class="text-sm font-medium text-green-900 mb-2">Resolution</h4>
                                    <p class="text-green-800 whitespace-pre-wrap">{{ $complaint->resolution }}</p>
                                    @if($complaint->resolved_at)
                                        <p class="text-xs text-green-600 mt-2">Resolved on {{ $complaint->resolved_at->format('M d, Y \a\t H:i') }}</p>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Comments Section -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Communication History</h3>
                            
                            @if($complaint->comments && $complaint->comments->count() > 0)
                                <div class="space-y-4">
                                    @foreach($complaint->comments as $comment)
                                        <div class="border border-gray-200 rounded-lg p-4">
                                            <div class="flex justify-between items-start mb-2">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                                        <span class="text-sm font-medium text-gray-700">
                                                            {{ substr($comment->user->name, 0, 1) }}
                                                        </span>
                                                    </div>
                                                    <div class="ml-3">
                                                        <p class="text-sm font-medium text-gray-900">{{ $comment->user->name }}</p>
                                                        <p class="text-xs text-gray-500">{{ $comment->created_at->format('M d, Y \a\t H:i') }}</p>
                                                    </div>
                                                </div>
                                                @if($comment->is_internal)
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                        Internal Note
                                                    </span>
                                                @endif
                                            </div>
                                            <p class="text-gray-700 whitespace-pre-wrap">{{ $comment->comment }}</p>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">No comments yet</h3>
                                    <p class="mt-1 text-sm text-gray-500">Communication history will appear here.</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Satisfaction Rating -->
                    @if($complaint->status === 'resolved' && !$complaint->satisfaction_rating && $complaint->submitted_by === Auth::id())
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Rate Your Experience</h3>
                                <p class="text-gray-600 mb-4">How satisfied are you with the resolution of your complaint?</p>
                                
                                <form action="{{ route('complaints.rating', $complaint) }}" method="POST">
                                    @csrf
                                    <div class="flex items-center space-x-4 mb-4">
                                        @for($i = 1; $i <= 5; $i++)
                                            <label class="flex items-center">
                                                <input type="radio" name="rating" value="{{ $i }}" class="sr-only" required>
                                                <div class="rating-star cursor-pointer text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-rating="{{ $i }}">
                                                    ⭐
                                                </div>
                                            </label>
                                        @endfor
                                    </div>
                                    <div class="mb-4">
                                        <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">Additional Comments (Optional)</label>
                                        <textarea name="comment" id="comment" rows="3" 
                                                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                                  placeholder="Share your feedback about the resolution..."></textarea>
                                    </div>
                                    <button type="submit" 
                                            class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        Submit Rating
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Complaint Info -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Complaint Information</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Reference Number</dt>
                                    <dd class="text-sm text-gray-900">{{ $complaint->reference_number }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Category</dt>
                                    <dd class="text-sm text-gray-900">{{ $complaint->category->name ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Submitted By</dt>
                                    <dd class="text-sm text-gray-900">
                                        @if($complaint->is_anonymous)
                                            Anonymous
                                        @else
                                            {{ $complaint->submitter->name ?? 'N/A' }}
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Assigned To</dt>
                                    <dd class="text-sm text-gray-900">{{ $complaint->assignee->name ?? 'Unassigned' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Submitted Date</dt>
                                    <dd class="text-sm text-gray-900">{{ $complaint->created_at->format('M d, Y \a\t H:i') }}</dd>
                                </div>
                                @if($complaint->due_date)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Due Date</dt>
                                        <dd class="text-sm text-gray-900">{{ $complaint->due_date->format('M d, Y') }}</dd>
                                    </div>
                                @endif
                                @if($complaint->satisfaction_rating)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Satisfaction Rating</dt>
                                        <dd class="text-sm text-gray-900">
                                            <div class="flex items-center">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <span class="text-lg {{ $i <= $complaint->satisfaction_rating ? 'text-yellow-400' : 'text-gray-300' }}">⭐</span>
                                                @endfor
                                                <span class="ml-2 text-gray-600">({{ $complaint->satisfaction_rating }}/5)</span>
                                            </div>
                                        </dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                    </div>

                    <!-- Actions -->
                    @if($complaint->status === 'resolved' && $complaint->submitted_by === Auth::id() && !in_array($complaint->status, ['closed']))
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                                <div class="space-y-3">
                                    <form action="{{ route('complaints.reopen', $complaint) }}" method="POST">
                                        @csrf
                                        <button type="submit" 
                                                class="w-full inline-flex justify-center items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-700 focus:bg-yellow-700 active:bg-yellow-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                                onclick="return confirm('Are you sure you want to reopen this complaint?')">
                                            Reopen Complaint
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script>
        // Rating stars interaction
        document.addEventListener('DOMContentLoaded', function() {
            const stars = document.querySelectorAll('.rating-star');
            const radioInputs = document.querySelectorAll('input[name="rating"]');
            
            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    const rating = parseInt(this.dataset.rating);
                    radioInputs[index].checked = true;
                    
                    // Update star colors
                    stars.forEach((s, i) => {
                        if (i < rating) {
                            s.classList.remove('text-gray-300');
                            s.classList.add('text-yellow-400');
                        } else {
                            s.classList.remove('text-yellow-400');
                            s.classList.add('text-gray-300');
                        }
                    });
                });
                
                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.dataset.rating);
                    stars.forEach((s, i) => {
                        if (i < rating) {
                            s.classList.add('text-yellow-400');
                        }
                    });
                });
                
                star.addEventListener('mouseleave', function() {
                    const checkedRating = document.querySelector('input[name="rating"]:checked');
                    const currentRating = checkedRating ? parseInt(checkedRating.value) : 0;
                    
                    stars.forEach((s, i) => {
                        if (i < currentRating) {
                            s.classList.add('text-yellow-400');
                            s.classList.remove('text-gray-300');
                        } else {
                            s.classList.remove('text-yellow-400');
                            s.classList.add('text-gray-300');
                        }
                    });
                });
            });
        });
    </script>
</x-app-layout>
