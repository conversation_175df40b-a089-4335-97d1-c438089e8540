@echo off
echo ========================================
echo EMERGENCY FIX - Resolving All Errors
echo ========================================

echo Step 1: Clearing all caches and configs...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
if exist bootstrap\cache\config.php del bootstrap\cache\config.php
if exist bootstrap\cache\routes-v7.php del bootstrap\cache\routes-v7.php
if exist bootstrap\cache\services.php del bootstrap\cache\services.php

echo Step 2: Fixing environment configuration...
if not exist .env (
    copy .env.example .env
    echo ✅ Created .env file
)

echo Step 3: Generating new application key...
php artisan key:generate --force

echo Step 4: Creating required directories...
if not exist storage\framework\cache mkdir storage\framework\cache
if not exist storage\framework\cache\data mkdir storage\framework\cache\data
if not exist storage\framework\sessions mkdir storage\framework\sessions
if not exist storage\framework\views mkdir storage\framework\views
if not exist storage\logs mkdir storage\logs
if not exist bootstrap\cache mkdir bootstrap\cache

echo Step 5: Setting proper permissions...
attrib -r storage\* /s /d
attrib -r bootstrap\cache\* /s /d

echo Step 6: Creating fresh database...
if exist database\database.sqlite del database\database.sqlite
echo. > database\database.sqlite

echo Step 7: Running migrations...
php artisan migrate:fresh --force

echo Step 8: Seeding database...
php artisan db:seed --force

echo Step 9: Creating storage link...
php artisan storage:link

echo Step 10: Final cache clear...
php artisan config:clear
php artisan cache:clear

echo.
echo ========================================
echo ALL ERRORS FIXED!
echo ========================================
echo.
echo The system is now ready to use.
echo.
echo Access URLs:
echo • Admin: http://localhost:8000/admin
echo • Student: http://localhost:8000/student/dashboard
echo.
echo Credentials:
echo • Admin: <EMAIL> / 123456
echo • Student: <EMAIL> / 123456
echo.
echo Starting server...
php artisan serve
