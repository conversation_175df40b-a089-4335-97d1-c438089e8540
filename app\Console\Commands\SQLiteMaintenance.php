<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class SQLiteMaintenance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sqlite:maintenance {--vacuum : Run VACUUM to reclaim space} {--analyze : Update statistics} {--integrity : Check database integrity} {--backup : Create backup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Perform SQLite database maintenance operations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (config('database.default') !== 'sqlite') {
            $this->error('This command is only for SQLite databases.');
            return 1;
        }

        $this->info('Starting SQLite maintenance...');

        if ($this->option('vacuum')) {
            $this->runVacuum();
        }

        if ($this->option('analyze')) {
            $this->runAnalyze();
        }

        if ($this->option('integrity')) {
            $this->runIntegrityCheck();
        }

        if ($this->option('backup')) {
            $this->createBackup();
        }

        // If no specific option, run all maintenance tasks
        if (!$this->option('vacuum') && !$this->option('analyze') && 
            !$this->option('integrity') && !$this->option('backup')) {
            $this->runVacuum();
            $this->runAnalyze();
            $this->runIntegrityCheck();
            $this->createBackup();
        }

        $this->info('SQLite maintenance completed.');
        return 0;
    }

    /**
     * Run VACUUM to reclaim unused space
     */
    private function runVacuum(): void
    {
        $this->info('Running VACUUM...');
        
        try {
            $start = microtime(true);
            DB::statement('VACUUM');
            $duration = round(microtime(true) - $start, 2);
            
            $this->info("✅ VACUUM completed in {$duration} seconds");
        } catch (\Exception $e) {
            $this->error("❌ VACUUM failed: {$e->getMessage()}");
        }
    }

    /**
     * Run ANALYZE to update query planner statistics
     */
    private function runAnalyze(): void
    {
        $this->info('Running ANALYZE...');
        
        try {
            $start = microtime(true);
            DB::statement('ANALYZE');
            $duration = round(microtime(true) - $start, 2);
            
            $this->info("✅ ANALYZE completed in {$duration} seconds");
        } catch (\Exception $e) {
            $this->error("❌ ANALYZE failed: {$e->getMessage()}");
        }
    }

    /**
     * Run integrity check
     */
    private function runIntegrityCheck(): void
    {
        $this->info('Running integrity check...');
        
        try {
            $result = DB::select('PRAGMA integrity_check');
            
            if (count($result) === 1 && $result[0]->integrity_check === 'ok') {
                $this->info('✅ Database integrity check passed');
            } else {
                $this->error('❌ Database integrity check failed:');
                foreach ($result as $issue) {
                    $this->error("  - {$issue->integrity_check}");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Integrity check failed: {$e->getMessage()}");
        }
    }

    /**
     * Create database backup
     */
    private function createBackup(): void
    {
        $this->info('Creating database backup...');
        
        try {
            $dbPath = database_path('database.sqlite');
            $backupDir = storage_path('backups');
            
            if (!File::exists($backupDir)) {
                File::makeDirectory($backupDir, 0755, true);
            }
            
            $backupFile = $backupDir . '/database_' . date('Y-m-d_H-i-s') . '.sqlite';
            
            if (File::copy($dbPath, $backupFile)) {
                $size = File::size($backupFile);
                $sizeFormatted = $this->formatBytes($size);
                
                $this->info("✅ Backup created: {$backupFile} ({$sizeFormatted})");
                
                // Clean up old backups
                $this->cleanupOldBackups($backupDir);
            } else {
                $this->error('❌ Failed to create backup');
            }
        } catch (\Exception $e) {
            $this->error("❌ Backup failed: {$e->getMessage()}");
        }
    }

    /**
     * Clean up old backup files
     */
    private function cleanupOldBackups(string $backupDir): void
    {
        $retentionDays = config('sqlite.backup.retention_days', 30);
        $cutoffTime = now()->subDays($retentionDays)->timestamp;
        
        $files = File::files($backupDir);
        $deletedCount = 0;
        
        foreach ($files as $file) {
            if ($file->getMTime() < $cutoffTime && str_contains($file->getFilename(), 'database_')) {
                File::delete($file->getPathname());
                $deletedCount++;
            }
        }
        
        if ($deletedCount > 0) {
            $this->info("🗑️  Cleaned up {$deletedCount} old backup(s)");
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
