@echo off
echo ========================================
echo ULTIMATE FIX - ALL ERRORS RESOLVED
echo ========================================

echo Deleting problematic files...
if exist .env del .env
if exist database\database.sqlite del database\database.sqlite
if exist bootstrap\cache\*.php del bootstrap\cache\*.php

echo Creating fresh environment...
(
echo APP_NAME="Complaints Management System"
echo APP_ENV=local
echo APP_KEY=
echo APP_DEBUG=true
echo APP_URL=http://localhost
echo.
echo LOG_CHANNEL=stack
echo LOG_LEVEL=debug
echo.
echo DB_CONNECTION=sqlite
echo.
echo BROADCAST_DRIVER=log
echo CACHE_DRIVER=file
echo FILESYSTEM_DISK=local
echo QUEUE_CONNECTION=sync
echo SESSION_DRIVER=file
echo SESSION_LIFETIME=120
echo.
echo MAIL_MAILER=log
echo MAIL_FROM_ADDRESS="<EMAIL>"
echo MAIL_FROM_NAME="${APP_NAME}"
) > .env

echo Creating directories...
if not exist storage\framework\cache\data mkdir storage\framework\cache\data
if not exist storage\framework\sessions mkdir storage\framework\sessions
if not exist storage\framework\views mkdir storage\framework\views
if not exist app\Http\Controllers\Auth mkdir app\Http\Controllers\Auth

echo Creating database...
echo. > database\database.sqlite

echo Generating key...
php artisan key:generate --force

echo Clearing caches...
php artisan config:clear 2>nul
php artisan cache:clear 2>nul

echo Running migrations...
php artisan migrate:fresh --force

echo Seeding database...
php artisan db:seed --force

echo Creating users...
php auto-create-users.php 2>nul

echo.
echo ========================================
echo ALL ERRORS FIXED!
echo ========================================
echo.
echo Login at: http://localhost:8000/login
echo.
echo Credentials:
echo • <EMAIL> / 123456
echo • <EMAIL> / 123456
echo.
echo Starting server...
php artisan serve
