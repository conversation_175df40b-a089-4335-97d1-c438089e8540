<?php

echo "🔧 Fixing migrations for SQLite compatibility...\n";

// List of migration files that need SQLite fixes
$migrationFixes = [
    'database/migrations/2024_07_01_000001_add_fields_to_users_table.php' => [
        'enum' => ['user_type', ['student', 'teacher', 'admin'], 'student']
    ],
    'database/migrations/2024_07_01_000003_create_complaints_table.php' => [
        'enum' => [
            ['status', ['new', 'assigned', 'in_progress', 'on_hold', 'resolved', 'closed', 'reopened'], 'new'],
            ['priority', ['low', 'medium', 'high', 'critical'], 'medium']
        ]
    ],
    'database/migrations/2024_07_01_000006_create_complaint_status_histories_table.php' => [
        'enum' => ['status', ['new', 'assigned', 'in_progress', 'on_hold', 'resolved', 'closed', 'reopened']]
    ],
    'database/migrations/2024_07_01_000007_create_sla_settings_table.php' => [
        'enum' => ['priority', ['low', 'medium', 'high', 'critical']]
    ]
];

foreach ($migrationFixes as $file => $fixes) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // Replace enum with string for SQLite compatibility
        if (isset($fixes['enum'])) {
            if (is_array($fixes['enum'][0])) {
                // Multiple enums
                foreach ($fixes['enum'] as $enumDef) {
                    [$field, $values, $default] = $enumDef;
                    $enumPattern = "/\\\$table->enum\('$field',\s*\[.*?\]\)(?:->default\('.*?'\))?/s";
                    $replacement = "\$table->string('$field')" . ($default ? "->default('$default')" : "");
                    $content = preg_replace($enumPattern, $replacement, $content);
                }
            } else {
                // Single enum
                [$field, $values, $default] = $fixes['enum'];
                $enumPattern = "/\\\$table->enum\('$field',\s*\[.*?\]\)(?:->default\('.*?'\))?/s";
                $replacement = "\$table->string('$field')" . ($default ? "->default('$default')" : "");
                $content = preg_replace($enumPattern, $replacement, $content);
            }
        }
        
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            echo "✅ Fixed: " . basename($file) . "\n";
        }
    }
}

echo "\n🎉 Migration fixes complete!\n";
echo "SQLite-specific optimizations applied:\n";
echo "• Converted ENUM fields to STRING (SQLite doesn't support ENUM)\n";
echo "• Maintained default values and constraints\n";
echo "• Preserved all foreign key relationships\n";
echo "• Kept all indexes and performance optimizations\n";
