# 🚀 SIMPLE STEPS - Nuclear Reset Approach

## Method 1: Nuclear Reset (Recommended)

**Just run this ONE command:**
```bash
NUCLEAR-RESET.bat
```

**New credentials will be:**
- Admin: `<EMAIL>` / `123456`
- Student: `<EMAIL>` / `123456`

---

## Method 2: Manual Nuclear Reset

**Step 1: Delete everything**
```bash
del database\database.sqlite
del .env
```

**Step 2: Create new .env**
```bash
echo APP_NAME="Complaints Management System" > .env
echo APP_ENV=local >> .env
echo APP_KEY= >> .env
echo APP_DEBUG=true >> .env
echo APP_URL=http://localhost >> .env
echo. >> .env
echo DB_CONNECTION=sqlite >> .env
echo. >> .env
echo SESSION_DRIVER=file >> .env
echo CACHE_DRIVER=file >> .env
```

**Step 3: Create database and setup**
```bash
echo. > database\database.sqlite
php artisan key:generate --force
php artisan migrate:fresh --force
```

**Step 4: Create admin directly**
```bash
php create-admin.php
```

**Step 5: Start server**
```bash
php artisan serve
```

---

## Method 3: If PHP scripts fail

**Do steps 1-3 from Method 2, then:**

**Step 4: Open database directly**
Download DB Browser for SQLite: https://sqlitebrowser.org/

**Step 5: Open database\database.sqlite and run this SQL:**
```sql
DELETE FROM users;
DELETE FROM roles;
DELETE FROM model_has_roles;

INSERT INTO roles (id, name, guard_name, created_at, updated_at) VALUES 
(1, 'admin', 'web', datetime('now'), datetime('now')),
(2, 'student', 'web', datetime('now'), datetime('now'));

INSERT INTO users (id, name, email, password, email_verified_at, department, user_type, created_at, updated_at) VALUES 
(1, 'Admin', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', datetime('now'), 'IT', 'admin', datetime('now'), datetime('now')),
(2, 'Student', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', datetime('now'), 'CS', 'student', datetime('now'), datetime('now'));

INSERT INTO model_has_roles (role_id, model_type, model_id) VALUES 
(1, 'App\Models\User', 1),
(2, 'App\Models\User', 2);
```

**Credentials for Method 3:**
- Admin: `<EMAIL>` / `password`
- Student: `<EMAIL>` / `password`

---

## What's Different This Time:

✅ **Completely deletes old database**
✅ **Creates fresh .env file**
✅ **Uses different email addresses**
✅ **Uses simpler passwords**
✅ **Direct database manipulation**
✅ **No dependency on seeders**

**Try Method 1 first - it should work!**
