<?php

return [

    /*
    |--------------------------------------------------------------------------
    | SQLite Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains SQLite-specific configuration options for the
    | Complaints Management System. These settings optimize performance
    | and ensure proper operation with SQLite database.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | These settings optimize SQLite performance for the application.
    |
    */

    'performance' => [
        'journal_mode' => env('SQLITE_JOURNAL_MODE', 'WAL'),
        'synchronous' => env('SQLITE_SYNCHRONOUS', 'NORMAL'),
        'cache_size' => env('SQLITE_CACHE_SIZE', 10000),
        'temp_store' => env('SQLITE_TEMP_STORE', 'MEMORY'),
        'mmap_size' => env('SQLITE_MMAP_SIZE', 268435456), // 256MB
        'busy_timeout' => env('SQLITE_BUSY_TIMEOUT', 30000), // 30 seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Pragma Settings
    |--------------------------------------------------------------------------
    |
    | SQLite PRAGMA statements to execute on connection.
    |
    */

    'pragmas' => [
        'foreign_keys' => 'ON',
        'journal_mode' => 'WAL',
        'synchronous' => 'NORMAL',
        'cache_size' => 10000,
        'temp_store' => 'MEMORY',
        'mmap_size' => 268435456,
        'optimize' => null, // Run PRAGMA optimize on connection
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for SQLite database backups.
    |
    */

    'backup' => [
        'enabled' => env('SQLITE_BACKUP_ENABLED', true),
        'path' => env('SQLITE_BACKUP_PATH', storage_path('backups')),
        'retention_days' => env('SQLITE_BACKUP_RETENTION', 30),
        'schedule' => env('SQLITE_BACKUP_SCHEDULE', 'daily'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Settings
    |--------------------------------------------------------------------------
    |
    | SQLite maintenance and optimization settings.
    |
    */

    'maintenance' => [
        'auto_vacuum' => env('SQLITE_AUTO_VACUUM', 'INCREMENTAL'),
        'vacuum_schedule' => env('SQLITE_VACUUM_SCHEDULE', 'weekly'),
        'analyze_schedule' => env('SQLITE_ANALYZE_SCHEDULE', 'daily'),
        'integrity_check' => env('SQLITE_INTEGRITY_CHECK', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Limits and Constraints
    |--------------------------------------------------------------------------
    |
    | SQLite-specific limits and constraints.
    |
    */

    'limits' => [
        'max_page_count' => env('SQLITE_MAX_PAGE_COUNT', 1073741823),
        'max_length' => env('SQLITE_MAX_LENGTH', 1000000000),
        'max_column' => env('SQLITE_MAX_COLUMN', 2000),
        'max_sql_length' => env('SQLITE_MAX_SQL_LENGTH', 1000000000),
        'max_function_arg' => env('SQLITE_MAX_FUNCTION_ARG', 127),
        'max_attached' => env('SQLITE_MAX_ATTACHED', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Settings
    |--------------------------------------------------------------------------
    |
    | Settings specific to development environment.
    |
    */

    'development' => [
        'query_log' => env('SQLITE_QUERY_LOG', false),
        'explain_query_plan' => env('SQLITE_EXPLAIN_QUERIES', false),
        'debug_mode' => env('SQLITE_DEBUG', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Production Settings
    |--------------------------------------------------------------------------
    |
    | Settings optimized for production environment.
    |
    */

    'production' => [
        'read_uncommitted' => env('SQLITE_READ_UNCOMMITTED', false),
        'secure_delete' => env('SQLITE_SECURE_DELETE', true),
        'checkpoint_fullfsync' => env('SQLITE_CHECKPOINT_FULLFSYNC', true),
        'defer_foreign_keys' => env('SQLITE_DEFER_FOREIGN_KEYS', false),
    ],

];
