<?php

namespace App\Filament\Widgets;

use App\Models\Complaint;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class ComplaintsChart extends ChartWidget
{
    protected static ?string $heading = 'Complaints Over Time';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $data = $this->getComplaintsPerMonth();

        return [
            'datasets' => [
                [
                    'label' => 'Complaints submitted',
                    'data' => $data['complaintsPerMonth'],
                    'borderColor' => '#f59e0b',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                ],
                [
                    'label' => 'Complaints resolved',
                    'data' => $data['resolvedPerMonth'],
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                ],
            ],
            'labels' => $data['months'],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    private function getComplaintsPerMonth(): array
    {
        $now = Carbon::now();
        $months = [];
        $complaintsPerMonth = [];
        $resolvedPerMonth = [];

        for ($i = 11; $i >= 0; $i--) {
            $month = $now->copy()->subMonths($i);
            $months[] = $month->format('M Y');

            $complaintsCount = Complaint::whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->count();
            $complaintsPerMonth[] = $complaintsCount;

            $resolvedCount = Complaint::whereYear('resolution_date', $month->year)
                ->whereMonth('resolution_date', $month->month)
                ->count();
            $resolvedPerMonth[] = $resolvedCount;
        }

        return [
            'months' => $months,
            'complaintsPerMonth' => $complaintsPerMonth,
            'resolvedPerMonth' => $resolvedPerMonth,
        ];
    }
}
